var h=(a,n,c)=>new Promise((_,l)=>{var u=d=>{try{g(c.next(d))}catch(y){l(y)}},i=d=>{try{g(c.throw(d))}catch(y){l(y)}},g=d=>d.done?_(d.value):Promise.resolve(d.value).then(u,i);g((c=c.apply(a,n)).next())});import{h as r,d as I,i as w,m as C,k as O,c as b,a as t,g as V,n as m,p as T,s as $,b as A,e as R,F as Z,l as q,t as p,E as D,o as v}from"./index-4iV_uVFP.js";import{_ as G}from"./_plugin-vue_export-helper-DlAUqK2U.js";const L={getList(a){return r.get("/tickets",{params:a})},getDetail(a){return r.get(`/tickets/${a}`)},create(a){return r.post("/tickets",a)},update(a,n){return r.put(`/tickets/${a}`,n)},delete(a){return r.delete(`/tickets/${a}`)},batchDelete(a){return r.post("/tickets/batch-delete",{ids:a})},updateStatus(a,n){return r.put(`/tickets/${a}/status`,{status:n})},updatePriority(a,n){return r.put(`/tickets/${a}/priority`,{priority:n})},assign(a,n){return r.put(`/tickets/${a}/assign`,{admin_id:n})},reply(a){return r.post("/tickets/reply",a)},getReplies(a){return r.get(`/tickets/${a}/replies`)},getStats(){return r.get("/tickets/stats")},exportTickets(a){return r.get("/tickets/export",{params:a,responseType:"blob"})},getCategories(){return r.get("/tickets/categories")}},H={class:"ticket-management p-4"},J={class:"admin-card mb-6"},K={class:"p-4"},Q={class:"flex items-center justify-between mb-4"},W={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},X={class:"admin-card p-4 mb-6 overflow-x-auto"},Y={class:"table-admin w-full"},tt={id:"tickets-tbody"},et={class:"px-4 py-3 border-t border-gray-700"},st={class:"px-4 py-3 border-t border-gray-700 max-w-xs truncate"},at={class:"px-4 py-3 border-t border-gray-700"},ot={class:"px-4 py-3 border-t border-gray-700"},lt={class:"px-2 py-1 rounded-full text-xs bg-blue-900 text-blue-300"},nt={class:"px-4 py-3 border-t border-gray-700"},rt={class:"px-4 py-3 border-t border-gray-700"},it={class:"px-4 py-3 border-t border-gray-700 text-sm text-gray-400"},dt={class:"px-4 py-3 border-t border-gray-700"},pt={class:"flex space-x-1"},ct=["onClick","data-id"],ut=["onClick","data-id"],gt=["onClick","data-id"],yt=["onClick","data-id"],xt={key:0},ft={class:"pagination"},bt=["disabled"],mt={class:"page-info"},vt=["disabled"],_t=I({__name:"TicketListView",setup(a){const n=w([]),c=w(!1),_=C({pending_tickets:0,processing_tickets:0,resolved_tickets:0,closed_tickets:0,today_tickets:0,avg_response_time:0}),l=C({search:"",status:"all",priority:"all",category:"all",page:1,per_page:20}),u=w(!1),i=C({page:1,pages:1,total:0,per_page:20}),g=s=>new Date(s).toLocaleDateString("zh-CN"),d=s=>({pending:"待处理",processing:"处理中",resolved:"已解决",closed:"已关闭"})[s]||s,y=s=>({low:"低",medium:"中",high:"高"})[s]||s,B=s=>({high:"px-2 py-1 rounded-full text-xs bg-red-900 text-red-300",medium:"px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300",low:"px-2 py-1 rounded-full text-xs bg-green-900 text-green-300"})[s]||"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300",F=s=>({pending:"px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300",processing:"px-2 py-1 rounded-full text-xs bg-blue-900 text-blue-300",resolved:"px-2 py-1 rounded-full text-xs bg-green-900 text-green-300",closed:"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300"})[s]||"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300",N=()=>{u.value=!u.value},x=()=>{l.page=1,f()},M=()=>h(this,null,function*(){try{const s=yield L.getStats();s.success&&Object.assign(_,s.data)}catch(s){console.error("加载工单统计失败:",s)}}),j=()=>{f(),M()},f=()=>h(this,null,function*(){c.value=!0;try{const s={page:l.page,per_page:l.per_page,search:l.search||void 0,status:l.status!=="all"?l.status:void 0,priority:l.priority!=="all"?l.priority:void 0,category:l.category!=="all"?l.category:void 0},e=yield L.getList(s);e.success?(n.value=e.data.items||[],Object.assign(i,{page:e.data.current_page||1,pages:e.data.pages||1,total:e.data.total||0,per_page:e.data.per_page||20,has_prev:e.data.has_prev||!1,has_next:e.data.has_next||!1,prev_num:e.data.prev_num||null,next_num:e.data.next_num||null})):D.error(e.message||"加载工单列表失败")}catch(s){console.error("加载工单失败:",s),D.error("加载工单列表失败"),n.value=[{id:1,title:"登录问题",content:"无法正常登录系统，提示密码错误但密码确实正确",status:"pending",priority:"high",category:"technical",user:{username:"user1"},created_at:"2024-01-01T00:00:00Z"}],i.total=1,i.pages=1}finally{c.value=!1}}),P=s=>{console.log("查看工单:",s)},E=s=>{console.log("编辑工单:",s)},U=s=>{console.log("回复工单:",s)},z=s=>{confirm(`确定要删除工单"${s.title}"吗？`)&&console.log("删除工单:",s)},S=s=>{l.page=s,i.page=s,f()};return O(()=>{f(),M()}),(s,e)=>(v(),b("div",H,[t("div",{class:"flex justify-between items-center mb-6"},[e[6]||(e[6]=t("h1",{class:"text-2xl font-bold text-yellow-400"},"💬 工单管理",-1)),t("button",{onClick:j,id:"refresh-tickets",class:"admin-btn-primary"},e[5]||(e[5]=[t("i",{class:"fas fa-sync-alt mr-2"},null,-1),t("span",{class:"hidden sm:inline"},"刷新",-1)]))]),t("div",J,[t("div",K,[t("div",Q,[e[7]||(e[7]=t("h3",{class:"text-lg font-medium text-yellow-400"},[t("i",{class:"fas fa-filter mr-2"}),V("筛选条件 ")],-1)),t("button",{onClick:N,id:"toggle-filters",class:"text-gray-400 hover:text-yellow-400 md:hidden"},[t("i",{class:m(["fas",u.value?"fa-chevron-up":"fa-chevron-down"]),id:"filter-icon"},null,2)])]),t("div",{class:m(["filter-content",u.value?"block":"hidden md:block"]),id:"filter-content"},[t("div",W,[t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"状态",-1)),T(t("select",{"onUpdate:modelValue":e[0]||(e[0]=o=>l.status=o),id:"status-filter",class:"form-input w-full",onChange:x},e[8]||(e[8]=[A('<option value="all" data-v-fc4449ca>全部状态</option><option value="pending" data-v-fc4449ca>待处理</option><option value="processing" data-v-fc4449ca>处理中</option><option value="resolved" data-v-fc4449ca>已解决</option><option value="closed" data-v-fc4449ca>已关闭</option>',5)]),544),[[$,l.status]])]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"分类",-1)),T(t("select",{"onUpdate:modelValue":e[1]||(e[1]=o=>l.category=o),id:"category-filter",class:"form-input w-full",onChange:x},e[10]||(e[10]=[t("option",{value:"all"},"全部分类",-1),t("option",{value:"售前咨询"},"售前咨询",-1),t("option",{value:"售后支持"},"售后支持",-1)]),544),[[$,l.category]])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"优先级",-1)),T(t("select",{"onUpdate:modelValue":e[2]||(e[2]=o=>l.priority=o),id:"priority-filter",class:"form-input w-full",onChange:x},e[12]||(e[12]=[t("option",{value:"all"},"全部优先级",-1),t("option",{value:"low"},"低",-1),t("option",{value:"normal"},"普通",-1),t("option",{value:"high"},"高",-1)]),544),[[$,l.priority]])]),t("div",{class:"flex items-end"},[t("button",{onClick:x,id:"apply-filters",class:"admin-btn-primary w-full"},e[14]||(e[14]=[t("i",{class:"fas fa-search mr-2"},null,-1),V("筛选 ",-1)]))])])],2)])]),t("div",X,[t("table",Y,[e[20]||(e[20]=t("thead",null,[t("tr",null,[t("th",{class:"px-4 py-2 text-left"},"ID"),t("th",{class:"px-4 py-2 text-left"},"标题"),t("th",{class:"px-4 py-2 text-left"},"用户"),t("th",{class:"px-4 py-2 text-left"},"分类"),t("th",{class:"px-4 py-2 text-left"},"优先级"),t("th",{class:"px-4 py-2 text-left"},"状态"),t("th",{class:"px-4 py-2 text-left"},"创建时间"),t("th",{class:"px-4 py-2 text-left"},"操作")])],-1)),t("tbody",tt,[(v(!0),b(Z,null,q(n.value,o=>(v(),b("tr",{key:o.id,class:"hover:bg-gray-700/50 transition-colors"},[t("td",et,p(o.id),1),t("td",st,p(o.title),1),t("td",at,p(o.user_name),1),t("td",ot,[t("span",lt,p(o.category),1)]),t("td",nt,[t("span",{class:m(B(o.priority))},p(y(o.priority)),3)]),t("td",rt,[t("span",{class:m(F(o.status))},p(d(o.status)),3)]),t("td",it,p(g(o.created_at)),1),t("td",dt,[t("div",pt,[t("button",{onClick:k=>P(o),class:"view-ticket-btn admin-btn-secondary px-2 py-1 rounded text-sm","data-id":o.id,title:"查看详情"},e[15]||(e[15]=[t("i",{class:"fas fa-eye"},null,-1)]),8,ct),t("button",{onClick:k=>E(o),class:"edit-ticket-btn admin-btn-primary px-2 py-1 rounded text-sm","data-id":o.id,title:"编辑"},e[16]||(e[16]=[t("i",{class:"fas fa-edit"},null,-1)]),8,ut),t("button",{onClick:k=>U(o),class:"reply-ticket-btn admin-btn-secondary px-2 py-1 rounded text-sm","data-id":o.id,title:"回复"},e[17]||(e[17]=[t("i",{class:"fas fa-reply"},null,-1)]),8,gt),t("button",{onClick:k=>z(o),class:"delete-ticket-btn admin-btn-danger px-2 py-1 rounded text-sm","data-id":o.id,title:"删除"},e[18]||(e[18]=[t("i",{class:"fas fa-trash-alt"},null,-1)]),8,yt)])])]))),128)),n.value.length===0?(v(),b("tr",xt,e[19]||(e[19]=[t("td",{colspan:"8",class:"px-4 py-8 text-center text-gray-400"},[t("i",{class:"fas fa-ticket-alt text-4xl mb-4"}),t("p",null,"暂无工单数据")],-1)]))):R("",!0)])]),t("div",ft,[t("button",{class:"btn-secondary",disabled:i.page<=1,onClick:e[3]||(e[3]=o=>S(i.page-1))}," 上一页 ",8,bt),t("span",mt," 第 "+p(i.page)+" 页，共 "+p(i.pages)+" 页 ",1),t("button",{class:"btn-secondary",disabled:i.page>=i.pages,onClick:e[4]||(e[4]=o=>S(i.page+1))}," 下一页 ",8,vt)])])]))}}),Ct=G(_t,[["__scopeId","data-v-fc4449ca"]]);export{Ct as default};
