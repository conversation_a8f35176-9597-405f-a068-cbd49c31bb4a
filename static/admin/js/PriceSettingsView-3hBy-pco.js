var k=Object.defineProperty;var f=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,V=Object.prototype.propertyIsEnumerable;var v=(p,n,l)=>n in p?k(p,n,{enumerable:!0,configurable:!0,writable:!0,value:l}):p[n]=l,x=(p,n)=>{for(var l in n||(n={}))w.call(n,l)&&v(p,l,n[l]);if(f)for(var l of f(n))V.call(n,l)&&v(p,l,n[l]);return p};var b=(p,n,l)=>new Promise((m,u)=>{var g=t=>{try{i(l.next(t))}catch(e){u(e)}},y=t=>{try{i(l.throw(t))}catch(e){u(e)}},i=t=>t.done?m(t.value):Promise.resolve(t.value).then(g,y);i((l=l.apply(p,n)).next())});import{d as P,i as c,m as U,k as I,c as q,a as s,g as a,a7 as j,p as r,v as o,E as d,o as C}from"./index-4iV_uVFP.js";import{s as _}from"./settings-BVtu9YEI.js";const M={class:"price-settings"},S={class:"admin-card p-6 mb-6"},B={class:"mb-8"},E={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},N={class:"flex items-center"},O={class:"flex items-center"},T={class:"flex items-center"},A={class:"flex items-center"},D={class:"mb-8"},z={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},F={class:"flex items-center"},G={class:"flex items-center"},H={class:"flex items-center"},J={class:"mb-8"},K={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},L={class:"flex items-center space-x-2"},Q={class:"flex items-center space-x-2"},R={class:"mb-8"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},X={class:"flex items-center"},Y={class:"flex items-center"},tt=P({__name:"PriceSettingsView",setup(p){const n=c(!1),l=U({basic_content_price:9.9,premium_content_price:19.9,vip_content_price:39.9,consultation_price:99,monthly_vip_price:29.9,quarterly_vip_price:79.9,yearly_vip_price:299.9,points_package_small:9.9,points_small_amount:100,points_package_large:49.9,points_large_amount:600,vip_discount:.85,bulk_discount:.9}),m=c({}),u=()=>b(this,null,function*(){n.value=!0;try{const i=yield _.updatePriceSettings(l);i.success?(d.success("价格设置保存成功"),m.value=x({},l)):d.error(i.error||"保存价格失败")}catch(i){console.error("保存价格失败:",i),d.error("保存价格失败")}finally{n.value=!1}}),g=()=>{Object.assign(l,m.value),d.info("价格已重置")},y=()=>b(this,null,function*(){n.value=!0;try{const i=yield _.getPriceSettings();i.success?(Object.assign(l,i.data),m.value=x({},i.data)):d.error(i.error||"加载价格失败")}catch(i){console.error("加载价格失败:",i),d.error("加载价格失败")}finally{n.value=!1}});return I(()=>{y()}),(i,t)=>(C(),q("div",M,[s("div",{class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},[t[15]||(t[15]=s("h1",{class:"text-2xl font-bold text-white"},"价格设置",-1)),s("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[s("button",{onClick:g,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},t[13]||(t[13]=[s("i",{class:"fas fa-undo mr-2"},null,-1),a("重置价格 ",-1)])),s("button",{onClick:u,class:"admin-btn-primary px-4 py-2 rounded-lg"},t[14]||(t[14]=[s("i",{class:"fas fa-save mr-2"},null,-1),a("保存价格 ",-1)]))])]),s("div",S,[s("form",{onSubmit:j(u,["prevent"])},[s("div",B,[t[28]||(t[28]=s("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[s("i",{class:"fas fa-file-alt mr-2 text-green-500"}),a("内容价格设置 ")],-1)),s("div",E,[s("div",null,[t[17]||(t[17]=s("label",{for:"basic_content_price",class:"block mb-2 text-gray-300"},"基础内容价格",-1)),s("div",N,[t[16]||(t[16]=s("span",{class:"text-gray-400 mr-2"},"¥",-1)),r(s("input",{type:"number",id:"basic_content_price","onUpdate:modelValue":t[0]||(t[0]=e=>l.basic_content_price=e),step:"0.01",min:"0",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"9.90"},null,512),[[o,l.basic_content_price]])]),t[18]||(t[18]=s("p",{class:"text-gray-400 text-sm mt-1"},"标准塔罗牌解读价格",-1))]),s("div",null,[t[20]||(t[20]=s("label",{for:"premium_content_price",class:"block mb-2 text-gray-300"},"高级内容价格",-1)),s("div",O,[t[19]||(t[19]=s("span",{class:"text-gray-400 mr-2"},"¥",-1)),r(s("input",{type:"number",id:"premium_content_price","onUpdate:modelValue":t[1]||(t[1]=e=>l.premium_content_price=e),step:"0.01",min:"0",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"19.90"},null,512),[[o,l.premium_content_price]])]),t[21]||(t[21]=s("p",{class:"text-gray-400 text-sm mt-1"},"深度塔罗牌解读价格",-1))]),s("div",null,[t[23]||(t[23]=s("label",{for:"vip_content_price",class:"block mb-2 text-gray-300"},"VIP内容价格",-1)),s("div",T,[t[22]||(t[22]=s("span",{class:"text-gray-400 mr-2"},"¥",-1)),r(s("input",{type:"number",id:"vip_content_price","onUpdate:modelValue":t[2]||(t[2]=e=>l.vip_content_price=e),step:"0.01",min:"0",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"39.90"},null,512),[[o,l.vip_content_price]])]),t[24]||(t[24]=s("p",{class:"text-gray-400 text-sm mt-1"},"专业塔罗师一对一解读",-1))]),s("div",null,[t[26]||(t[26]=s("label",{for:"consultation_price",class:"block mb-2 text-gray-300"},"咨询服务价格",-1)),s("div",A,[t[25]||(t[25]=s("span",{class:"text-gray-400 mr-2"},"¥",-1)),r(s("input",{type:"number",id:"consultation_price","onUpdate:modelValue":t[3]||(t[3]=e=>l.consultation_price=e),step:"0.01",min:"0",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"99.00"},null,512),[[o,l.consultation_price]])]),t[27]||(t[27]=s("p",{class:"text-gray-400 text-sm mt-1"},"个人咨询服务价格",-1))])])]),s("div",D,[t[38]||(t[38]=s("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[s("i",{class:"fas fa-crown mr-2 text-yellow-500"}),a("会员价格设置 ")],-1)),s("div",z,[s("div",null,[t[30]||(t[30]=s("label",{for:"monthly_vip_price",class:"block mb-2 text-gray-300"},"月度VIP价格",-1)),s("div",F,[t[29]||(t[29]=s("span",{class:"text-gray-400 mr-2"},"¥",-1)),r(s("input",{type:"number",id:"monthly_vip_price","onUpdate:modelValue":t[4]||(t[4]=e=>l.monthly_vip_price=e),step:"0.01",min:"0",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"29.90"},null,512),[[o,l.monthly_vip_price]])]),t[31]||(t[31]=s("p",{class:"text-gray-400 text-sm mt-1"},"30天VIP会员",-1))]),s("div",null,[t[33]||(t[33]=s("label",{for:"quarterly_vip_price",class:"block mb-2 text-gray-300"},"季度VIP价格",-1)),s("div",G,[t[32]||(t[32]=s("span",{class:"text-gray-400 mr-2"},"¥",-1)),r(s("input",{type:"number",id:"quarterly_vip_price","onUpdate:modelValue":t[5]||(t[5]=e=>l.quarterly_vip_price=e),step:"0.01",min:"0",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"79.90"},null,512),[[o,l.quarterly_vip_price]])]),t[34]||(t[34]=s("p",{class:"text-gray-400 text-sm mt-1"},"90天VIP会员",-1))]),s("div",null,[t[36]||(t[36]=s("label",{for:"yearly_vip_price",class:"block mb-2 text-gray-300"},"年度VIP价格",-1)),s("div",H,[t[35]||(t[35]=s("span",{class:"text-gray-400 mr-2"},"¥",-1)),r(s("input",{type:"number",id:"yearly_vip_price","onUpdate:modelValue":t[6]||(t[6]=e=>l.yearly_vip_price=e),step:"0.01",min:"0",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"299.90"},null,512),[[o,l.yearly_vip_price]])]),t[37]||(t[37]=s("p",{class:"text-gray-400 text-sm mt-1"},"365天VIP会员",-1))])])]),s("div",J,[t[47]||(t[47]=s("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[s("i",{class:"fas fa-coins mr-2 text-blue-500"}),a("积分价格设置 ")],-1)),s("div",K,[s("div",null,[t[42]||(t[42]=s("label",{for:"points_package_small",class:"block mb-2 text-gray-300"},"小积分包价格",-1)),s("div",L,[t[39]||(t[39]=s("span",{class:"text-gray-400"},"¥",-1)),r(s("input",{type:"number",id:"points_package_small","onUpdate:modelValue":t[7]||(t[7]=e=>l.points_package_small=e),step:"0.01",min:"0",class:"form-input flex-1 px-3 py-2 rounded-lg",placeholder:"9.90"},null,512),[[o,l.points_package_small]]),t[40]||(t[40]=s("span",{class:"text-gray-400"},"=",-1)),r(s("input",{type:"number","onUpdate:modelValue":t[8]||(t[8]=e=>l.points_small_amount=e),min:"1",class:"form-input w-20 px-3 py-2 rounded-lg",placeholder:"100"},null,512),[[o,l.points_small_amount]]),t[41]||(t[41]=s("span",{class:"text-gray-400"},"积分",-1))])]),s("div",null,[t[46]||(t[46]=s("label",{for:"points_package_large",class:"block mb-2 text-gray-300"},"大积分包价格",-1)),s("div",Q,[t[43]||(t[43]=s("span",{class:"text-gray-400"},"¥",-1)),r(s("input",{type:"number",id:"points_package_large","onUpdate:modelValue":t[9]||(t[9]=e=>l.points_package_large=e),step:"0.01",min:"0",class:"form-input flex-1 px-3 py-2 rounded-lg",placeholder:"49.90"},null,512),[[o,l.points_package_large]]),t[44]||(t[44]=s("span",{class:"text-gray-400"},"=",-1)),r(s("input",{type:"number","onUpdate:modelValue":t[10]||(t[10]=e=>l.points_large_amount=e),min:"1",class:"form-input w-20 px-3 py-2 rounded-lg",placeholder:"600"},null,512),[[o,l.points_large_amount]]),t[45]||(t[45]=s("span",{class:"text-gray-400"},"积分",-1))])])])]),s("div",R,[t[54]||(t[54]=s("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[s("i",{class:"fas fa-percentage mr-2 text-red-500"}),a("折扣设置 ")],-1)),s("div",W,[s("div",null,[t[49]||(t[49]=s("label",{for:"vip_discount",class:"block mb-2 text-gray-300"},"VIP会员折扣",-1)),s("div",X,[r(s("input",{type:"number",id:"vip_discount","onUpdate:modelValue":t[11]||(t[11]=e=>l.vip_discount=e),step:"0.01",min:"0",max:"1",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"0.85"},null,512),[[o,l.vip_discount]]),t[48]||(t[48]=s("span",{class:"text-gray-400 ml-2"},"折",-1))]),t[50]||(t[50]=s("p",{class:"text-gray-400 text-sm mt-1"},"VIP会员享受的折扣比例",-1))]),s("div",null,[t[52]||(t[52]=s("label",{for:"bulk_discount",class:"block mb-2 text-gray-300"},"批量购买折扣",-1)),s("div",Y,[r(s("input",{type:"number",id:"bulk_discount","onUpdate:modelValue":t[12]||(t[12]=e=>l.bulk_discount=e),step:"0.01",min:"0",max:"1",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"0.90"},null,512),[[o,l.bulk_discount]]),t[51]||(t[51]=s("span",{class:"text-gray-400 ml-2"},"折",-1))]),t[53]||(t[53]=s("p",{class:"text-gray-400 text-sm mt-1"},"批量购买时的折扣比例",-1))])])]),s("div",{class:"flex justify-end space-x-4"},[s("button",{type:"button",onClick:g,class:"admin-btn-secondary px-6 py-2 rounded-lg"},t[55]||(t[55]=[s("i",{class:"fas fa-undo mr-2"},null,-1),a("重置 ",-1)])),t[56]||(t[56]=s("button",{type:"submit",class:"admin-btn-primary px-6 py-2 rounded-lg"},[s("i",{class:"fas fa-save mr-2"}),a("保存价格 ")],-1))])],32)])]))}});export{tt as default};
