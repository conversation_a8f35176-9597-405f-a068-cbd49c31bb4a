var A=(a,r,u)=>new Promise((k,v)=>{var b=d=>{try{y(u.next(d))}catch(w){v(w)}},C=d=>{try{y(u.throw(d))}catch(w){v(w)}},y=d=>d.done?k(d.value):Promise.resolve(d.value).then(b,C);y((u=u.apply(a,r)).next())});import{h as m,d as W,i as _,m as V,k as X,c as x,a as t,e as Y,g as o,p as $,v as j,q as B,s as z,F as I,l as P,t as p,a7 as Z,E as f,n as N,y as tt,o as g}from"./index-B79VaFD-.js";import{_ as st}from"./_plugin-vue_export-helper-DlAUqK2U.js";const et={getList(a){console.log("🌐 [authorsApi] 调用getList，参数:",a),console.log("🌐 [authorsApi] 请求URL: /admin/api/authors");const r=m.get("/authors",{params:a});return r.then(u=>{console.log("🌐 [authorsApi] getList响应成功:",u)}).catch(u=>{console.error("🌐 [authorsApi] getList响应失败:",u)}),r},getDetail(a){return m.get(`/authors/${a}`)},create(a){return m.post("/authors",a)},update(a,r){return m.put(`/authors/${a}`,r)},delete(a){return m.delete(`/authors/${a}`)},batchDelete(a){return m.post("/authors/batch-delete",{ids:a})},toggleActive(a,r){return m.put(`/authors/${a}/active`,{is_active:r})},getStats(){return m.get("/authors/stats")},getAuthorArticles(a,r){return m.get(`/authors/${a}/articles`,{params:r})},uploadAvatar(a){const r=new FormData;return r.append("file",a),m.post("/authors/upload-avatar",r,{headers:{"Content-Type":"multipart/form-data"}})},getPopularAuthors(a=10){return m.get("/authors/popular",{params:{limit:a}})}},lt={class:"author-management"},at={class:"admin-card p-4 mb-6"},ot={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},nt={class:"admin-card p-4 mb-6 overflow-x-auto"},it={key:0,class:"text-center py-8"},rt={key:1},dt={class:"table-admin w-full"},ut={class:"px-4 py-3 text-gray-300"},pt={class:"px-4 py-3 text-white font-medium"},ct={class:"px-4 py-3"},xt=["onClick"],ft={class:"px-4 py-3"},gt={class:"flex space-x-2"},mt=["onClick"],yt=["onClick"],vt=["onClick"],ht={key:2,class:"text-center py-8"},bt={class:"flex justify-between items-center mb-6"},wt={class:"text-xl font-bold text-white"},_t={class:"flex space-x-4 mt-2 text-sm"},kt={class:"text-gray-300"},Ct={class:"text-blue-400"},At={class:"text-gray-300"},$t={class:"text-green-400"},Vt={class:"text-gray-300"},Lt={class:"text-yellow-400"},Dt={class:"text-gray-300"},Tt={class:"text-purple-400"},Mt={class:"admin-card p-4 mb-6"},zt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Et={class:"admin-card p-4 overflow-y-auto max-h-96"},St={key:0,class:"text-center py-8"},Ut={key:1},jt={class:"table-admin w-full"},Bt={class:"px-4 py-3 text-gray-300"},It={class:"px-4 py-3 text-white font-medium max-w-xs truncate"},Pt={class:"px-4 py-3"},Nt={class:"px-4 py-3"},Ft={class:"px-4 py-3 text-blue-400"},Ht={class:"px-4 py-3 text-gray-300"},Kt={class:"px-4 py-3"},Rt={class:"flex space-x-2"},qt=["onClick"],Gt=["onClick"],Jt=["onClick"],Ot={key:2,class:"text-center py-8"},Qt={class:"flex justify-between items-center mt-6"},Wt={class:"text-gray-300"},Xt={class:"text-blue-400"},Yt=W({__name:"AuthorListView",setup(a){const r=tt(),u=_([]),k=_(!1),v=V({search:"",is_active:void 0,page:1,per_page:20}),b=V({page:1,per_page:20,total:0,pages:0}),C=_(!1),y=_(null),d=_([]),w=_(!1),n=V({total:0,published:0,draft:0,totalViews:0}),i=V({search:"",status:"",type:"",page:1,per_page:20}),L=()=>A(this,null,function*(){k.value=!0;try{const e=yield et.getList(v);e&&e.success?e.data&&e.data.data?(u.value=e.data.data,e.data.pagination?(b.page=e.data.pagination.page,b.pages=e.data.pagination.pages,b.total=e.data.pagination.total,b.per_page=e.data.pagination.per_page):(b.total=e.data.data.length,b.pages=1)):u.value=Array.isArray(e.data)?e.data:[]:(f.error((e==null?void 0:e.error)||(e==null?void 0:e.message)||"加载塔罗师失败"),u.value=[])}catch(e){f.error("加载塔罗师列表失败"),u.value=[]}finally{k.value=!1}}),F=()=>{f.info("创建功能开发中...")},H=e=>{f.info(`编辑塔罗师: ${e.name}`)},K=e=>{confirm(`确定要删除塔罗师"${e.name}"吗？`)&&f.success(`已删除塔罗师: ${e.name}`)},R=()=>{L()},E=()=>{v.page=1,L()},S=e=>A(this,null,function*(){y.value=e,C.value=!0,i.search="",i.status="",i.type="",i.page=1,yield D()}),D=()=>A(this,null,function*(){if(y.value){w.value=!0,console.log("开始加载塔罗师文章:",y.value);try{const e=new URLSearchParams({author:y.value.id.toString()});i.search&&e.append("search",i.search),i.status&&e.append("status",i.status),i.type&&e.append("content_type",i.type);const s=yield fetch(`/admin/api/articles?${e}`,{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(console.log("API响应状态:",s.status),s.ok){const h=yield s.json();if(console.log("API响应数据:",h),h.success){const l=h.data||h.articles||[];console.log("获取到的文章:",l),d.value=l,n.total=l.length,n.published=l.filter(c=>c.status==="published").length,n.draft=l.filter(c=>c.status==="draft").length,n.totalViews=l.reduce((c,Q)=>c+(Q.views||0),0),console.log("统计数据:",n),f.success(`加载完成，找到 ${l.length} 篇文章`)}else console.error("API返回错误:",h.error),f.error(h.error||"加载文章失败"),d.value=[],T()}else console.error("HTTP请求失败:",s.status,s.statusText),f.error(`请求失败: ${s.status}`),d.value=[],T()}catch(e){console.error("加载文章列表异常:",e),f.error("网络错误，请重试"),d.value=[],T()}finally{w.value=!1}}}),T=()=>{n.total=0,n.published=0,n.draft=0,n.totalViews=0},U=()=>{i.page=1,D()},M=()=>{C.value=!1,y.value=null,d.value=[],n.total=0,n.published=0,n.draft=0,n.totalViews=0},q=e=>{r.push(`/articles/${e.id}/edit`)},G=e=>{r.push(`/articles/${e.id}/buyers`)},J=e=>A(this,null,function*(){if(confirm(`确定要删除文章"${e.title}"吗？`))try{(yield fetch(`/admin/api/articles/${e.id}`,{method:"DELETE",credentials:"include"})).ok?(f.success("文章删除成功"),yield D()):f.error("删除失败")}catch(s){console.error("删除文章失败:",s),f.error("删除失败，请重试")}}),O=e=>{if(!e)return"未知";const s=new Date(e);return s.toLocaleDateString("zh-CN")+" "+s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"})};return X(()=>{L()}),(e,s)=>{var h;return g(),x("div",lt,[t("div",{class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},[s[8]||(s[8]=t("h1",{class:"text-2xl font-bold text-white"},"塔罗师管理",-1)),t("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[t("button",{onClick:F,class:"admin-btn-primary px-4 py-2 rounded-lg text-center"},s[6]||(s[6]=[t("i",{class:"fas fa-plus mr-2"},null,-1),o("新增塔罗师 ",-1)])),t("button",{onClick:R,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},s[7]||(s[7]=[t("i",{class:"fas fa-sync-alt mr-2"},null,-1),o("刷新列表 ",-1)]))])]),t("div",at,[t("div",ot,[t("div",null,[s[9]||(s[9]=t("label",{for:"search",class:"block mb-2 text-gray-300"},"搜索塔罗师",-1)),$(t("input",{type:"text",id:"search","onUpdate:modelValue":s[0]||(s[0]=l=>v.search=l),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"输入塔罗师姓名...",onKeyup:B(E,["enter"])},null,544),[[j,v.search]])]),t("div",null,[s[11]||(s[11]=t("label",{for:"status",class:"block mb-2 text-gray-300"},"状态过滤",-1)),$(t("select",{id:"status","onUpdate:modelValue":s[1]||(s[1]=l=>v.is_active=l),class:"form-input w-full px-3 py-2 rounded-lg"},s[10]||(s[10]=[t("option",{value:""},"全部状态",-1),t("option",{value:"true"},"活跃",-1),t("option",{value:"false"},"停用",-1)]),512),[[z,v.is_active]])]),t("div",{class:"flex items-end"},[t("button",{onClick:E,class:"admin-btn-primary px-4 py-2 rounded-lg w-full"},s[12]||(s[12]=[t("i",{class:"fas fa-search mr-2"},null,-1),o("搜索 ",-1)]))])])]),t("div",nt,[k.value?(g(),x("div",it,s[13]||(s[13]=[t("div",{class:"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-indigo-500"},[t("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]),o(" 加载中... ")],-1)]))):u.value.length>0?(g(),x("div",rt,[t("table",dt,[s[17]||(s[17]=t("thead",null,[t("tr",null,[t("th",{class:"px-4 py-3 text-left"},"ID"),t("th",{class:"px-4 py-3 text-left"},"姓名"),t("th",{class:"px-4 py-3 text-left"},"文章数"),t("th",{class:"px-4 py-3 text-left"},"操作")])],-1)),t("tbody",null,[(g(!0),x(I,null,P(u.value,l=>(g(),x("tr",{key:l.id,class:"hover:bg-gray-700/50 transition-colors"},[t("td",ut,p(l.id),1),t("td",pt,p(l.name),1),t("td",ct,[t("button",{onClick:c=>S(l),class:"text-blue-400 hover:text-blue-300 font-medium",title:"点击查看文章列表"},p(l.articles_count||0)+" 篇 ",9,xt)]),t("td",ft,[t("div",gt,[t("button",{onClick:c=>S(l),class:"text-blue-400 hover:text-blue-300",title:"查看文章"},s[14]||(s[14]=[t("i",{class:"fas fa-file-alt mr-1"},null,-1),o("查看文章 ",-1)]),8,mt),t("button",{onClick:c=>H(l),class:"text-green-400 hover:text-green-300",title:"编辑"},s[15]||(s[15]=[t("i",{class:"fas fa-edit mr-1"},null,-1),o("编辑 ",-1)]),8,yt),t("button",{onClick:c=>K(l),class:"text-red-400 hover:text-red-300",title:"删除"},s[16]||(s[16]=[t("i",{class:"fas fa-trash mr-1"},null,-1),o("删除 ",-1)]),8,vt)])])]))),128))])])])):(g(),x("div",ht,s[18]||(s[18]=[t("div",{class:"text-gray-400"},[t("i",{class:"fas fa-users text-4xl mb-4"}),t("p",{class:"text-lg"},"暂无塔罗师数据"),t("p",{class:"text-sm"},"点击上方按钮创建第一个塔罗师")],-1)])))]),C.value?(g(),x("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:M},[t("div",{class:"bg-gray-800 rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-hidden",onClick:s[5]||(s[5]=Z(()=>{},["stop"]))},[t("div",bt,[t("div",null,[t("h2",wt," 塔罗师「"+p((h=y.value)==null?void 0:h.name)+"」的文章列表 ",1),t("div",_t,[t("span",kt,[s[19]||(s[19]=o("总文章: ",-1)),t("span",Ct,p(n.total),1)]),t("span",At,[s[20]||(s[20]=o("已发布: ",-1)),t("span",$t,p(n.published),1)]),t("span",Vt,[s[21]||(s[21]=o("草稿: ",-1)),t("span",Lt,p(n.draft),1)]),t("span",Dt,[s[22]||(s[22]=o("总阅读: ",-1)),t("span",Tt,p(n.totalViews),1)])])]),t("button",{onClick:M,class:"text-gray-400 hover:text-white"},s[23]||(s[23]=[t("i",{class:"fas fa-times text-xl"},null,-1)]))]),t("div",Mt,[t("div",zt,[t("div",null,[s[24]||(s[24]=t("label",{class:"block mb-2 text-gray-300"},"搜索文章",-1)),$(t("input",{type:"text","onUpdate:modelValue":s[2]||(s[2]=l=>i.search=l),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"输入文章标题...",onKeyup:B(U,["enter"])},null,544),[[j,i.search]])]),t("div",null,[s[26]||(s[26]=t("label",{class:"block mb-2 text-gray-300"},"状态",-1)),$(t("select",{"onUpdate:modelValue":s[3]||(s[3]=l=>i.status=l),class:"form-input w-full px-3 py-2 rounded-lg"},s[25]||(s[25]=[t("option",{value:""},"全部状态",-1),t("option",{value:"published"},"已发布",-1),t("option",{value:"draft"},"草稿",-1)]),512),[[z,i.status]])]),t("div",null,[s[28]||(s[28]=t("label",{class:"block mb-2 text-gray-300"},"类型",-1)),$(t("select",{"onUpdate:modelValue":s[4]||(s[4]=l=>i.type=l),class:"form-input w-full px-3 py-2 rounded-lg"},s[27]||(s[27]=[t("option",{value:""},"全部类型",-1),t("option",{value:"free"},"免费",-1),t("option",{value:"paid"},"付费",-1)]),512),[[z,i.type]])]),t("div",{class:"flex items-end"},[t("button",{onClick:U,class:"admin-btn-primary px-4 py-2 rounded-lg w-full"},s[29]||(s[29]=[t("i",{class:"fas fa-search mr-2"},null,-1),o("搜索 ",-1)]))])])]),t("div",Et,[w.value?(g(),x("div",St,s[30]||(s[30]=[t("div",{class:"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-indigo-500"},[t("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]),o(" 加载文章中... ")],-1)]))):d.value.length>0?(g(),x("div",Ut,[t("table",jt,[s[34]||(s[34]=t("thead",null,[t("tr",null,[t("th",{class:"px-4 py-3 text-left"},"ID"),t("th",{class:"px-4 py-3 text-left"},"标题"),t("th",{class:"px-4 py-3 text-left"},"类型"),t("th",{class:"px-4 py-3 text-left"},"状态"),t("th",{class:"px-4 py-3 text-left"},"阅读量"),t("th",{class:"px-4 py-3 text-left"},"创建时间"),t("th",{class:"px-4 py-3 text-left"},"操作")])],-1)),t("tbody",null,[(g(!0),x(I,null,P(d.value,l=>(g(),x("tr",{key:l.id,class:"hover:bg-gray-700/50 transition-colors"},[t("td",Bt,p(l.id),1),t("td",It,p(l.title),1),t("td",Pt,[t("span",{class:N(l.is_free?"text-green-400":"text-yellow-400")},p(l.is_free?"免费":"付费"),3)]),t("td",Nt,[t("span",{class:N(l.status==="published"?"text-green-400":"text-gray-400")},p(l.status==="published"?"已发布":"草稿"),3)]),t("td",Ft,p(l.views||0),1),t("td",Ht,p(O(l.created_at)),1),t("td",Kt,[t("div",Rt,[t("button",{onClick:c=>q(l),class:"text-blue-400 hover:text-blue-300",title:"编辑文章"},s[31]||(s[31]=[t("i",{class:"fas fa-edit mr-1"},null,-1),o("编辑 ",-1)]),8,qt),t("button",{onClick:c=>G(l),class:"text-green-400 hover:text-green-300",title:"查看购买者"},s[32]||(s[32]=[t("i",{class:"fas fa-users mr-1"},null,-1),o("购买者 ",-1)]),8,Gt),t("button",{onClick:c=>J(l),class:"text-red-400 hover:text-red-300",title:"删除文章"},s[33]||(s[33]=[t("i",{class:"fas fa-trash mr-1"},null,-1),o("删除 ",-1)]),8,Jt)])])]))),128))])])])):(g(),x("div",Ot,s[35]||(s[35]=[t("div",{class:"text-gray-400"},[t("i",{class:"fas fa-file-alt text-4xl mb-4"}),t("p",{class:"text-lg"},"该塔罗师暂无文章"),t("p",{class:"text-sm"},"可以为该塔罗师创建新文章")],-1)])))]),t("div",Qt,[t("div",Wt,[s[36]||(s[36]=o(" 共找到 ",-1)),t("span",Xt,p(d.value.length),1),s[37]||(s[37]=o(" 篇文章 ",-1))]),t("button",{onClick:M,class:"admin-btn-secondary px-4 py-2 rounded-lg"}," 关闭 ")])])])):Y("",!0)])}}}),es=st(Yt,[["__scopeId","data-v-59e15e69"]]);export{es as default};
