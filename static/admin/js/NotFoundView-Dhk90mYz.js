var h=Object.defineProperty,V=Object.defineProperties;var B=Object.getOwnPropertyDescriptors;var y=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var b=(o,t,e)=>t in o?h(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,v=(o,t)=>{for(var e in t||(t={}))E.call(t,e)&&b(o,e,t[e]);if(y)for(var e of y(t))I.call(t,e)&&b(o,e,t[e]);return o},g=(o,t)=>V(o,B(t));/* empty css                  */import{Z as S,bN as w,bO as R,bP as T,bQ as P,a0 as j,d as m,a2 as D,j as F,c,o as l,a as _,e as p,aj as d,a6 as M,_ as n,n as r,ak as z,t as k,an as H,f as C,w as $,g as O,y as Q}from"./index-4iV_uVFP.js";import{E as Z}from"./index-oLYaZnUp.js";import{_ as q}from"./_plugin-vue_export-helper-DlAUqK2U.js";const a={primary:"icon-primary",success:"icon-success",warning:"icon-warning",error:"icon-error",info:"icon-info"},N={[a.primary]:w,[a.success]:P,[a.warning]:T,[a.error]:R,[a.info]:w},x=S({title:{type:String,default:""},subTitle:{type:String,default:""},icon:{type:String,values:["primary","success","warning","info","error"],default:"info"}}),A=m({name:"ElResult"}),G=m(g(v({},A),{props:x,setup(o){const t=o,e=D("result"),u=F(()=>{const s=t.icon,i=s&&a[s]?a[s]:"icon-info",f=N[i]||N["icon-info"];return{class:i,component:f}});return(s,i)=>(l(),c("div",{class:r(n(e).b())},[_("div",{class:r(n(e).e("icon"))},[d(s.$slots,"icon",{},()=>[n(u).component?(l(),M(z(n(u).component),{key:0,class:r(n(u).class)},null,8,["class"])):p("v-if",!0)])],2),s.title||s.$slots.title?(l(),c("div",{key:0,class:r(n(e).e("title"))},[d(s.$slots,"title",{},()=>[_("p",null,k(s.title),1)])],2)):p("v-if",!0),s.subTitle||s.$slots["sub-title"]?(l(),c("div",{key:1,class:r(n(e).e("subtitle"))},[d(s.$slots,"sub-title",{},()=>[_("p",null,k(s.subTitle),1)])],2)):p("v-if",!0),s.$slots.extra?(l(),c("div",{key:2,class:r(n(e).e("extra"))},[d(s.$slots,"extra")],2)):p("v-if",!0)],2))}}));var J=j(G,[["__file","result.vue"]]);const K=H(J),L={class:"not-found"},U=m({__name:"NotFoundView",setup(o){const t=Q(),e=()=>{t.push("/dashboard")};return(u,s)=>{const i=Z,f=K;return l(),c("div",L,[C(f,{icon:"warning",title:"404","sub-title":"抱歉，您访问的页面不存在"},{extra:$(()=>[C(i,{type:"primary",onClick:e},{default:$(()=>s[0]||(s[0]=[O(" 返回首页 ",-1)])),_:1,__:[0]})]),_:1})])}}}),te=q(U,[["__scopeId","data-v-fdd41ea7"]]);export{te as default};
