var D=(o,d,b)=>new Promise((i,c)=>{var m=a=>{try{x(b.next(a))}catch(f){c(f)}},n=a=>{try{x(b.throw(a))}catch(f){c(f)}},x=a=>a.done?i(a.value):Promise.resolve(a.value).then(m,n);x((b=b.apply(o,d)).next())});import{h as r,d as G,i as k,m as V,k as H,c as y,a as t,e as $,g as _,p,v as h,q as Q,s as A,b as W,F as X,l as Y,t as u,x as Z,a7 as tt,E as g,n as S,o as v}from"./index-4iV_uVFP.js";const et={getList(o){return r.get("/announcements",{params:o})},getDetail(o){return r.get(`/announcements/${o}`)},create(o){return r.post("/announcements",o)},update(o,d){return r.put(`/announcements/${o}`,d)},delete(o){return r.delete(`/announcements/${o}`)},batchDelete(o){return r.post("/announcements/batch-delete",{ids:o})},toggleActive(o,d){return r.put(`/announcements/${o}/active`,{is_active:d})},getStats(){return r.get("/announcements/stats")},getActiveAnnouncements(){return r.get("/announcements/active")},getUserAnnouncements(o){return r.get(`/announcements/user/${o}`)},markAsRead(o,d){return r.post(`/announcements/${o}/read`,{user_id:d})},getReadStats(o){return r.get(`/announcements/${o}/read-stats`)},sendNotification(o){return r.post(`/announcements/${o}/notify`)}},st={class:"announcement-management"},lt={class:"admin-card p-4 mb-6"},at={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ot={class:"admin-card p-4 mb-6 overflow-x-auto table-responsive"},nt={class:"table-admin w-full"},rt={class:"px-4 py-3 border-t border-gray-700"},dt={class:"px-4 py-3 border-t border-gray-700 max-w-xs truncate"},it={class:"text-white font-medium"},ut={class:"px-4 py-3 border-t border-gray-700"},pt={class:"px-4 py-3 border-t border-gray-700"},ct={class:"px-4 py-3 border-t border-gray-700 text-sm text-gray-300"},xt={key:0},yt={key:1,class:"text-gray-500"},gt={class:"px-4 py-3 border-t border-gray-700"},vt={class:"px-4 py-3 border-t border-gray-700"},bt={class:"flex flex-wrap gap-1"},mt=["onClick"],ft=["onClick"],wt=["onClick"],kt={key:0},_t={class:"flex justify-between items-center mb-6"},ht={class:"text-xl font-bold text-white"},Ct={class:"space-y-4"},Mt=["disabled"],Dt=["disabled"],Vt=["disabled"],$t={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},At=["disabled"],St=["disabled"],Tt={class:"flex items-center space-x-2"},Ut=["disabled"],Et={class:"flex justify-end space-x-3 mt-6"},jt=G({__name:"AnnouncementListView",setup(o){const d=k([]),b=k(!1),i=V({search:"",type:"",status:""}),c=V({page:1,per_page:20,total:0,pages:0}),m=k(!1),n=k("create"),x=k(null),a=V({title:"",content:"",priority:"normal",start_date:"",end_date:"",is_active:!0}),f=()=>{n.value="create",T(),m.value=!0},j=s=>{n.value="view",x.value=s,U(s),m.value=!0},F=s=>{n.value="edit",x.value=s,U(s),m.value=!0},P=s=>D(this,null,function*(){if(confirm(`确定要删除公告"${s.title}"吗？`))try{(yield fetch(`/admin/api/announcements/${s.id}`,{method:"DELETE",credentials:"include"})).ok?(g.success("公告删除成功"),yield w()):g.error("删除失败")}catch(e){console.error("删除公告失败:",e),g.error("删除失败，请重试")}}),T=()=>{a.title="",a.content="",a.priority="normal",a.start_date="",a.end_date="",a.is_active=!0},U=s=>{a.title=s.title,a.content=s.content,a.priority=s.priority,a.start_date=s.start_date?s.start_date.slice(0,16):"",a.end_date=s.end_date?s.end_date.slice(0,16):"",a.is_active=s.is_active},C=()=>{m.value=!1,x.value=null,T()},B=()=>D(this,null,function*(){try{const s=n.value==="create"?"/admin/api/announcements":`/admin/api/announcements/${x.value.id}`,e=n.value==="create"?"POST":"PUT";(yield fetch(s,{method:e,credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).ok?(g.success(n.value==="create"?"公告创建成功":"公告更新成功"),C(),yield w()):g.error("保存失败")}catch(s){console.error("保存公告失败:",s),g.error("保存失败，请重试")}}),w=()=>D(this,null,function*(){b.value=!0;try{const s={page:c.page,per_page:c.per_page,search:i.search||void 0,type:i.type||void 0,status:i.status||void 0},e=yield et.getList(s);e.success?(d.value=e.data||[],c.total=d.value.length,c.pages=1):g.error(e.error||"加载公告失败")}catch(s){console.error("加载公告失败:",s),g.error("加载公告列表失败")}finally{b.value=!1}}),z=()=>{w()},E=()=>{c.page=1,w()},K=s=>({system:"px-2 py-1 rounded-full text-xs bg-purple-900 text-purple-300",activity:"px-2 py-1 rounded-full text-xs bg-green-900 text-green-300",daily:"px-2 py-1 rounded-full text-xs bg-blue-900 text-blue-300",welcome:"px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300"})[s]||"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300",O=s=>({system:"系统公告",activity:"活动公告",daily:"每日公告",welcome:"欢迎公告"})[s]||s,R=s=>({low:"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300",normal:"px-2 py-1 rounded-full text-xs bg-blue-700 text-blue-300",high:"px-2 py-1 rounded-full text-xs bg-orange-700 text-orange-300",urgent:"px-2 py-1 rounded-full text-xs bg-red-700 text-red-300"})[s]||"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300",q=s=>({low:"低",normal:"普通",high:"高",urgent:"紧急"})[s]||s,I=s=>{const e=L(s);return{active:"px-2 py-1 rounded-full text-xs bg-green-700 text-green-300",upcoming:"px-2 py-1 rounded-full text-xs bg-blue-700 text-blue-300",expired:"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300"}[e]||"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300"},J=s=>{const e=L(s);return{active:"生效中",upcoming:"未开始",expired:"已过期"}[e]||"未知"},L=s=>{if(!s.start_date||!s.end_date)return"active";const e=new Date,l=new Date(s.start_date),M=new Date(s.end_date);return e<l?"upcoming":e>M?"expired":"active"},N=s=>new Date(s).toLocaleDateString();return H(()=>{console.log("公告管理页面已加载"),w()}),(s,e)=>(v(),y("div",st,[t("div",{class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},[e[12]||(e[12]=t("h1",{class:"text-2xl font-bold text-white"},"公告列表",-1)),t("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[t("button",{onClick:z,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},e[10]||(e[10]=[t("i",{class:"fas fa-sync-alt mr-2"},null,-1),_("刷新列表 ",-1)])),t("button",{onClick:f,class:"admin-btn-primary px-4 py-2 rounded-lg"},e[11]||(e[11]=[t("i",{class:"fas fa-plus mr-2"},null,-1),_("添加公告 ",-1)]))])]),t("div",lt,[t("div",at,[t("div",null,[e[13]||(e[13]=t("label",{for:"search",class:"block mb-2 text-gray-300"},"搜索公告",-1)),p(t("input",{type:"text",id:"search","onUpdate:modelValue":e[0]||(e[0]=l=>i.search=l),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"搜索标题或内容...",onKeyup:Q(E,["enter"])},null,544),[[h,i.search]])]),t("div",null,[e[15]||(e[15]=t("label",{for:"type",class:"block mb-2 text-gray-300"},"公告类型",-1)),p(t("select",{id:"type","onUpdate:modelValue":e[1]||(e[1]=l=>i.type=l),class:"form-input w-full px-3 py-2 rounded-lg"},e[14]||(e[14]=[W('<option value="">所有类型</option><option value="system">系统公告</option><option value="activity">活动公告</option><option value="daily">每日公告</option><option value="welcome">欢迎公告</option>',5)]),512),[[A,i.type]])]),t("div",null,[e[17]||(e[17]=t("label",{for:"status",class:"block mb-2 text-gray-300"},"公告状态",-1)),p(t("select",{id:"status","onUpdate:modelValue":e[2]||(e[2]=l=>i.status=l),class:"form-input w-full px-3 py-2 rounded-lg"},e[16]||(e[16]=[t("option",{value:""},"所有状态",-1),t("option",{value:"active"},"生效中",-1),t("option",{value:"upcoming"},"未开始",-1),t("option",{value:"expired"},"已过期",-1)]),512),[[A,i.status]])]),t("div",{class:"flex items-end"},[t("button",{onClick:E,class:"admin-btn-primary px-4 py-2 rounded-lg w-full"},e[18]||(e[18]=[t("i",{class:"fas fa-search mr-2"},null,-1),_("应用筛选 ",-1)]))])])]),t("div",ot,[t("table",nt,[e[24]||(e[24]=t("thead",null,[t("tr",null,[t("th",{class:"px-4 py-3 text-left"},"ID"),t("th",{class:"px-4 py-3 text-left"},"标题"),t("th",{class:"px-4 py-3 text-left"},"类型"),t("th",{class:"px-4 py-3 text-left"},"优先级"),t("th",{class:"px-4 py-3 text-left"},"有效期"),t("th",{class:"px-4 py-3 text-left"},"状态"),t("th",{class:"px-4 py-3 text-left"},"操作")])],-1)),t("tbody",null,[(v(!0),y(X,null,Y(d.value,l=>(v(),y("tr",{key:l.id,class:"hover:bg-gray-700/50 transition-colors"},[t("td",rt,u(l.id),1),t("td",dt,[t("div",it,u(l.title),1)]),t("td",ut,[t("span",{class:S(K(l.type))},u(O(l.type)),3)]),t("td",pt,[t("span",{class:S(R(l.priority))},u(q(l.priority)),3)]),t("td",ct,[l.start_date&&l.end_date?(v(),y("div",xt,[_(u(N(l.start_date))+" 至",1),e[19]||(e[19]=t("br",null,null,-1)),_(" "+u(N(l.end_date)),1)])):(v(),y("div",yt,"未设置"))]),t("td",gt,[t("span",{class:S(I(l))},u(J(l)),3)]),t("td",vt,[t("div",bt,[t("button",{onClick:M=>j(l),class:"admin-btn-secondary px-2 py-1 rounded text-sm",title:"查看详情"},e[20]||(e[20]=[t("i",{class:"fas fa-eye"},null,-1)]),8,mt),t("button",{onClick:M=>F(l),class:"admin-btn-primary px-2 py-1 rounded text-sm",title:"编辑公告"},e[21]||(e[21]=[t("i",{class:"fas fa-edit"},null,-1)]),8,ft),t("button",{onClick:M=>P(l),class:"admin-btn-danger px-2 py-1 rounded text-sm",title:"删除公告"},e[22]||(e[22]=[t("i",{class:"fas fa-trash-alt"},null,-1)]),8,wt)])])]))),128)),d.value.length===0?(v(),y("tr",kt,e[23]||(e[23]=[t("td",{colspan:"7",class:"px-4 py-8 text-center text-gray-400"},[t("i",{class:"fas fa-bullhorn text-4xl mb-4"}),t("p",null,"暂无公告数据")],-1)]))):$("",!0)])])]),m.value?(v(),y("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:C},[t("div",{class:"bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",onClick:e[9]||(e[9]=tt(()=>{},["stop"]))},[t("div",_t,[t("h2",ht,u(n.value==="create"?"创建公告":n.value==="edit"?"编辑公告":"查看公告"),1),t("button",{onClick:C,class:"text-gray-400 hover:text-white"},e[25]||(e[25]=[t("i",{class:"fas fa-times text-xl"},null,-1)]))]),t("div",Ct,[t("div",null,[e[26]||(e[26]=t("label",{class:"block mb-2 text-gray-300"},"公告标题 *",-1)),p(t("input",{type:"text","onUpdate:modelValue":e[3]||(e[3]=l=>a.title=l),disabled:n.value==="view",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"请输入公告标题"},null,8,Mt),[[h,a.title]])]),t("div",null,[e[27]||(e[27]=t("label",{class:"block mb-2 text-gray-300"},"公告内容 *",-1)),p(t("textarea",{"onUpdate:modelValue":e[4]||(e[4]=l=>a.content=l),disabled:n.value==="view",class:"form-input w-full px-3 py-2 rounded-lg h-32",placeholder:"请输入公告内容"},null,8,Dt),[[h,a.content]])]),t("div",null,[e[29]||(e[29]=t("label",{class:"block mb-2 text-gray-300"},"优先级",-1)),p(t("select",{"onUpdate:modelValue":e[5]||(e[5]=l=>a.priority=l),disabled:n.value==="view",class:"form-input w-full px-3 py-2 rounded-lg"},e[28]||(e[28]=[t("option",{value:"low"},"低",-1),t("option",{value:"normal"},"普通",-1),t("option",{value:"high"},"高",-1),t("option",{value:"urgent"},"紧急",-1)]),8,Vt),[[A,a.priority]])]),t("div",$t,[t("div",null,[e[30]||(e[30]=t("label",{class:"block mb-2 text-gray-300"},"开始时间 *",-1)),p(t("input",{type:"datetime-local","onUpdate:modelValue":e[6]||(e[6]=l=>a.start_date=l),disabled:n.value==="view",class:"form-input w-full px-3 py-2 rounded-lg"},null,8,At),[[h,a.start_date]])]),t("div",null,[e[31]||(e[31]=t("label",{class:"block mb-2 text-gray-300"},"结束时间",-1)),p(t("input",{type:"datetime-local","onUpdate:modelValue":e[7]||(e[7]=l=>a.end_date=l),disabled:n.value==="view",class:"form-input w-full px-3 py-2 rounded-lg"},null,8,St),[[h,a.end_date]])])]),t("div",null,[t("label",Tt,[p(t("input",{type:"checkbox","onUpdate:modelValue":e[8]||(e[8]=l=>a.is_active=l),disabled:n.value==="view",class:"form-checkbox"},null,8,Ut),[[Z,a.is_active]]),e[32]||(e[32]=t("span",{class:"text-gray-300"},"启用公告",-1))])])]),t("div",Et,[t("button",{onClick:C,class:"admin-btn-secondary px-4 py-2 rounded-lg"},u(n.value==="view"?"关闭":"取消"),1),n.value!=="view"?(v(),y("button",{key:0,onClick:B,class:"admin-btn-primary px-4 py-2 rounded-lg"},u(n.value==="create"?"创建":"保存"),1)):$("",!0)])])])):$("",!0)]))}});export{jt as default};
