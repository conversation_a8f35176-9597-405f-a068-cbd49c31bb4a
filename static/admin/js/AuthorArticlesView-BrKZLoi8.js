var y=(k,v,p)=>new Promise((m,x)=>{var g=o=>{try{i(p.next(o))}catch(l){x(l)}},r=o=>{try{i(p.throw(o))}catch(l){x(l)}},i=o=>o.done?m(o.value):Promise.resolve(o.value).then(g,r);i((p=p.apply(k,v)).next())});import{d as K,j as P,z as q,i as _,m as b,k as H,c as d,a as t,e as O,g as u,t as n,p as h,v as G,q as J,s as A,F as Q,l as W,y as X,h as w,n as M,o as c}from"./index-4iV_uVFP.js";const Y={class:"author-articles-management"},Z={class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},tt={class:"text-2xl font-bold text-white"},et={class:"text-lg text-gray-400 ml-2"},st={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},lt={class:"admin-card p-4"},at={class:"flex items-center"},ot={class:"text-2xl font-bold text-white"},nt={class:"admin-card p-4"},it={class:"flex items-center"},rt={class:"text-2xl font-bold text-white"},dt={class:"admin-card p-4"},ct={class:"flex items-center"},pt={class:"text-2xl font-bold text-white"},ut={class:"admin-card p-4"},xt={class:"flex items-center"},gt={class:"text-2xl font-bold text-white"},ft={class:"admin-card p-4 mb-6"},mt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},yt={class:"admin-card p-4 mb-6 overflow-x-auto"},vt={key:0,class:"text-center py-8"},_t={key:1},bt={class:"table-admin w-full"},ht={class:"px-4 py-3 text-gray-300"},wt={class:"px-4 py-3"},kt={class:"flex items-center space-x-3"},Ct=["src","alt"],$t={key:1,class:"w-12 h-8 bg-gray-600 rounded flex items-center justify-center"},St={class:"text-white font-medium"},At={class:"text-gray-400 text-sm"},Mt={class:"px-4 py-3"},Vt={class:"px-4 py-3"},jt={class:"px-4 py-3 text-gray-300"},Bt={class:"px-4 py-3 text-gray-300"},Dt={class:"px-4 py-3"},Tt={class:"flex space-x-2"},zt=["onClick"],Nt=["onClick"],It=["onClick"],Lt={key:2,class:"text-center py-8"},Ut={key:0,class:"admin-card p-4"},Et={class:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"},Rt={class:"text-gray-400"},Ft={class:"flex space-x-2"},Kt=["disabled"],Pt={class:"px-3 py-2 text-white"},qt=["disabled"],Gt=K({__name:"AuthorArticlesView",setup(k){const v=q(),p=X(),m=P(()=>v.params.id),x=_(""),g=_(!1),r=_([]),i=b({total_articles:0,published_articles:0,draft_articles:0,pending_articles:0,total_views:0}),o=b({search:"",status:"",content_type:"",page:1}),l=b({page:1,per_page:20,total:0}),V=()=>{p.push("/authors")},j=()=>{f()},C=()=>{o.page=1,l.page=1,f()},$=s=>{s>=1&&s<=Math.ceil(l.total/l.per_page)&&(o.page=s,l.page=s,f())},f=()=>y(this,null,function*(){g.value=!0;try{const s=new URLSearchParams({page:o.page.toString(),per_page:l.per_page.toString(),author_id:m.value.toString()});o.search&&s.append("search",o.search),o.status&&s.append("status",o.status),o.content_type&&s.append("content_type",o.content_type);const e=yield w.get(`/articles?${s}`);e.success&&(r.value=e.data||[],e.pagination&&Object.assign(l,e.pagination),B())}catch(s){console.error("加载文章失败:",s)}finally{g.value=!1}}),B=()=>{i.total_articles=r.value.length,i.published_articles=r.value.filter(s=>s.status==="published").length,i.draft_articles=r.value.filter(s=>s.status==="draft").length,i.pending_articles=r.value.filter(s=>s.status==="pending").length,i.total_views=r.value.reduce((s,e)=>s+(e.views||0),0)},D=s=>({published:"bg-green-500/20 text-green-400",draft:"bg-yellow-500/20 text-yellow-400",pending:"bg-blue-500/20 text-blue-400"})[s]||"bg-gray-500/20 text-gray-400",T=s=>({published:"已发布",draft:"草稿",pending:"待审核"})[s]||"未知",z=s=>({basic:"bg-blue-500/20 text-blue-400",premium:"bg-purple-500/20 text-purple-400",free:"bg-green-500/20 text-green-400"})[s]||"bg-gray-500/20 text-gray-400",N=s=>({basic:"基础",premium:"高级",free:"免费"})[s]||"普通",I=s=>s?new Date(s).toLocaleDateString("zh-CN"):"-",L=s=>{const e=s.target;e.style.display="none"},U=s=>{p.push(`/articles/${s.id}/edit`)},E=s=>{p.push(`/articles/${s.id}/buyers`)},R=s=>y(this,null,function*(){if(confirm(`确定要删除文章"${s.title}"吗？`))try{const e=yield w.delete(`/articles/${s.id}`);e.success?(alert("文章删除成功"),f()):alert(`删除失败: ${e.message}`)}catch(e){console.error("删除文章失败:",e),alert("删除失败，请重试")}}),F=()=>y(this,null,function*(){try{const s=yield w.get(`/authors/${m.value}`);s.success&&s.data&&(x.value=s.data.name)}catch(s){console.error("加载塔罗师信息失败:",s)}});return H(()=>{F(),f()}),(s,e)=>(c(),d("div",Y,[t("div",Z,[t("div",null,[t("div",{class:"flex items-center space-x-3 mb-2"},[t("button",{onClick:V,class:"text-gray-400 hover:text-white"},e[5]||(e[5]=[t("i",{class:"fas fa-arrow-left mr-2"},null,-1),u("返回塔罗师列表 ",-1)]))]),t("h1",tt,[u(n(x.value)+" 的文章管理 ",1),t("span",et,"("+n(i.total_articles)+" 篇)",1)])]),t("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[t("button",{onClick:j,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},e[6]||(e[6]=[t("i",{class:"fas fa-sync-alt mr-2"},null,-1),u("刷新列表 ",-1)]))])]),t("div",st,[t("div",lt,[t("div",at,[e[8]||(e[8]=t("div",{class:"p-3 rounded-full bg-blue-500/20 text-blue-400 mr-4"},[t("i",{class:"fas fa-file-alt text-xl"})],-1)),t("div",null,[e[7]||(e[7]=t("p",{class:"text-gray-400 text-sm"},"总文章数",-1)),t("p",ot,n(i.total_articles),1)])])]),t("div",nt,[t("div",it,[e[10]||(e[10]=t("div",{class:"p-3 rounded-full bg-green-500/20 text-green-400 mr-4"},[t("i",{class:"fas fa-check-circle text-xl"})],-1)),t("div",null,[e[9]||(e[9]=t("p",{class:"text-gray-400 text-sm"},"已发布",-1)),t("p",rt,n(i.published_articles),1)])])]),t("div",dt,[t("div",ct,[e[12]||(e[12]=t("div",{class:"p-3 rounded-full bg-yellow-500/20 text-yellow-400 mr-4"},[t("i",{class:"fas fa-edit text-xl"})],-1)),t("div",null,[e[11]||(e[11]=t("p",{class:"text-gray-400 text-sm"},"草稿",-1)),t("p",pt,n(i.draft_articles),1)])])]),t("div",ut,[t("div",xt,[e[14]||(e[14]=t("div",{class:"p-3 rounded-full bg-purple-500/20 text-purple-400 mr-4"},[t("i",{class:"fas fa-eye text-xl"})],-1)),t("div",null,[e[13]||(e[13]=t("p",{class:"text-gray-400 text-sm"},"总浏览量",-1)),t("p",gt,n(i.total_views),1)])])])]),t("div",ft,[t("div",mt,[t("div",null,[e[15]||(e[15]=t("label",{for:"search",class:"block mb-2 text-gray-300"},"搜索文章",-1)),h(t("input",{type:"text",id:"search","onUpdate:modelValue":e[0]||(e[0]=a=>o.search=a),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"输入文章标题...",onKeyup:J(C,["enter"])},null,544),[[G,o.search]])]),t("div",null,[e[17]||(e[17]=t("label",{for:"status",class:"block mb-2 text-gray-300"},"状态过滤",-1)),h(t("select",{id:"status","onUpdate:modelValue":e[1]||(e[1]=a=>o.status=a),class:"form-input w-full px-3 py-2 rounded-lg"},e[16]||(e[16]=[t("option",{value:""},"全部状态",-1),t("option",{value:"published"},"已发布",-1),t("option",{value:"draft"},"草稿",-1),t("option",{value:"pending"},"待审核",-1)]),512),[[A,o.status]])]),t("div",null,[e[19]||(e[19]=t("label",{for:"content_type",class:"block mb-2 text-gray-300"},"内容类型",-1)),h(t("select",{id:"content_type","onUpdate:modelValue":e[2]||(e[2]=a=>o.content_type=a),class:"form-input w-full px-3 py-2 rounded-lg"},e[18]||(e[18]=[t("option",{value:""},"全部类型",-1),t("option",{value:"basic"},"基础内容",-1),t("option",{value:"premium"},"高级内容",-1),t("option",{value:"free"},"免费内容",-1)]),512),[[A,o.content_type]])]),t("div",{class:"flex items-end"},[t("button",{onClick:C,class:"admin-btn-primary px-4 py-2 rounded-lg w-full"},e[20]||(e[20]=[t("i",{class:"fas fa-search mr-2"},null,-1),u("搜索 ",-1)]))])])]),t("div",yt,[g.value?(c(),d("div",vt,e[21]||(e[21]=[t("div",{class:"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-indigo-500"},[t("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]),u(" 加载中... ")],-1)]))):r.value.length>0?(c(),d("div",_t,[t("table",bt,[e[26]||(e[26]=t("thead",null,[t("tr",null,[t("th",{class:"px-4 py-3 text-left"},"ID"),t("th",{class:"px-4 py-3 text-left"},"标题"),t("th",{class:"px-4 py-3 text-left"},"状态"),t("th",{class:"px-4 py-3 text-left"},"类型"),t("th",{class:"px-4 py-3 text-left"},"浏览量"),t("th",{class:"px-4 py-3 text-left"},"发布时间"),t("th",{class:"px-4 py-3 text-left"},"操作")])],-1)),t("tbody",null,[(c(!0),d(Q,null,W(r.value,a=>(c(),d("tr",{key:a.id,class:"hover:bg-gray-700/50 transition-colors"},[t("td",ht,n(a.id),1),t("td",wt,[t("div",kt,[a.cover_url?(c(),d("img",{key:0,src:a.cover_url,alt:a.title,class:"w-12 h-8 object-cover rounded",onError:L},null,40,Ct)):(c(),d("div",$t,e[22]||(e[22]=[t("i",{class:"fas fa-image text-gray-400 text-xs"},null,-1)]))),t("div",null,[t("p",St,n(a.title),1),t("p",At,n(a.category||"未分类"),1)])])]),t("td",Mt,[t("span",{class:M([D(a.status),"px-2 py-1 rounded-full text-xs font-medium"])},n(T(a.status)),3)]),t("td",Vt,[t("span",{class:M([z(a.content_type),"px-2 py-1 rounded-full text-xs font-medium"])},n(N(a.content_type)),3)]),t("td",jt,n(a.views||0),1),t("td",Bt,n(I(a.publish_date||a.created_at)),1),t("td",Dt,[t("div",Tt,[t("button",{onClick:S=>U(a),class:"text-blue-400 hover:text-blue-300",title:"编辑"},e[23]||(e[23]=[t("i",{class:"fas fa-edit mr-1"},null,-1),u("编辑 ",-1)]),8,zt),t("button",{onClick:S=>E(a),class:"text-green-400 hover:text-green-300",title:"查看购买者"},e[24]||(e[24]=[t("i",{class:"fas fa-users mr-1"},null,-1),u("购买者 ",-1)]),8,Nt),t("button",{onClick:S=>R(a),class:"text-red-400 hover:text-red-300",title:"删除"},e[25]||(e[25]=[t("i",{class:"fas fa-trash mr-1"},null,-1),u("删除 ",-1)]),8,It)])])]))),128))])])])):(c(),d("div",Lt,e[27]||(e[27]=[t("i",{class:"fas fa-file-alt text-gray-400 text-4xl mb-4"},null,-1),t("p",{class:"text-gray-400 text-lg"},"该塔罗师暂无文章",-1)])))]),l.total>0?(c(),d("div",Ut,[t("div",Et,[t("div",Rt," 显示第 "+n((l.page-1)*l.per_page+1)+" - "+n(Math.min(l.page*l.per_page,l.total))+" 条， 共 "+n(l.total)+" 条记录 ",1),t("div",Ft,[t("button",{onClick:e[3]||(e[3]=a=>$(l.page-1)),disabled:l.page<=1,class:"admin-btn-secondary px-3 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"}," 上一页 ",8,Kt),t("span",Pt," 第 "+n(l.page)+" / "+n(Math.ceil(l.total/l.per_page))+" 页 ",1),t("button",{onClick:e[4]||(e[4]=a=>$(l.page+1)),disabled:l.page>=Math.ceil(l.total/l.per_page),class:"admin-btn-secondary px-3 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"}," 下一页 ",8,qt)])])])):O("",!0)]))}});export{Gt as default};
