var v=(w,n,i)=>new Promise((x,a)=>{var c=r=>{try{u(i.next(r))}catch(g){a(g)}},_=r=>{try{u(i.throw(r))}catch(g){a(g)}},u=r=>r.done?x(r.value):Promise.resolve(r.value).then(c,_);u((i=i.apply(w,n)).next())});import{d as T,i as f,k as L,c as p,a as t,e as h,g as m,p as M,s as $,F as z,l as B,t as l,n as k,o as d}from"./index-B79VaFD-.js";import{f as C}from"./finance-D9qcS87O.js";const F={class:"min-h-screen bg-gray-900 text-white"},j={class:"bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700"},A={class:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0"},E={class:"flex space-x-4"},I=["disabled"],O=["disabled"],P={class:"flex space-x-4"},U={class:"bg-gray-800 rounded-lg border border-gray-700 overflow-hidden"},Z={class:"overflow-x-auto"},q={class:"w-full"},G={class:"divide-y divide-gray-700"},H={class:"px-6 py-4 whitespace-nowrap text-sm text-white"},J={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},K={class:"px-6 py-4 whitespace-nowrap text-sm text-yellow-400"},Q={class:"px-6 py-4 whitespace-nowrap text-sm text-blue-400"},W={class:"px-6 py-4 whitespace-nowrap text-sm text-green-400"},X={class:"px-6 py-4 whitespace-nowrap text-sm text-purple-400"},Y={class:"px-6 py-4 whitespace-nowrap"},tt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},et={class:"px-6 py-4 whitespace-nowrap text-sm"},st=["onClick"],at=["onClick"],ot={key:0,class:"bg-gray-700 px-6 py-3 flex items-center justify-between"},lt={class:"text-sm text-gray-400"},nt={class:"flex space-x-2"},it=["disabled"],rt=["disabled"],ct={key:0,class:"text-center py-12"},ut=T({__name:"ReconciliationView",setup(w){const n=f(!1),i=f([]),x=f({status:""}),a=f({page:1,pages:1,per_page:20,total:0,has_prev:!1,has_next:!1}),c=e=>(e/100).toFixed(2),_=e=>e?new Date(e).toLocaleDateString("zh-CN"):"-",u=e=>e?new Date(e).toLocaleString("zh-CN"):"-",r=e=>e===0?"text-green-400":e>0?"text-red-400":"text-orange-400",g=e=>({balanced:"px-2 py-1 text-xs rounded-full bg-green-900 text-green-300",variance_detected:"px-2 py-1 text-xs rounded-full bg-red-900 text-red-300",pending:"px-2 py-1 text-xs rounded-full bg-yellow-900 text-yellow-300"})[e]||"px-2 py-1 text-xs rounded-full bg-gray-900 text-gray-300",D=e=>({balanced:"已平衡",variance_detected:"发现差异",pending:"待处理"})[e]||e,y=()=>v(this,null,function*(){n.value=!0;try{const e=yield C.getReconciliationList({page:a.value.page,per_page:a.value.per_page,status:x.value.status});e.success&&(i.value=e.data.data,a.value=e.data.pagination)}catch(e){console.error("获取对账记录失败:",e),i.value=[{id:"1",reconciliation_date:"2025-01-01",total_orders:156,total_amount:12345600,alipay_amount:6789e3,wechat_amount:4567800,stripe_amount:988800,variance_amount:0,status:"balanced",created_at:"2025-01-01T10:00:00Z"}],a.value={page:1,pages:1,per_page:20,total:1,has_prev:!1,has_next:!1}}finally{n.value=!1}}),S=()=>v(this,null,function*(){n.value=!0;try{const e=new Date().toISOString().split("T")[0];(yield C.createReconciliation(e)).success&&(yield y())}catch(e){console.error("创建对账记录失败:",e)}finally{n.value=!1}}),b=e=>{a.value.page=e,y()},V=e=>{console.log("查看对账记录详情:",e)},R=e=>{console.log("处理差异:",e)};return L(()=>{y()}),(e,s)=>(d(),p("div",F,[s[10]||(s[10]=t("div",{class:"mb-6"},[t("h1",{class:"text-2xl font-bold text-white"},"对账记录管理"),t("p",{class:"text-gray-400 mt-1"},"财务对账记录和差异分析")],-1)),t("div",j,[t("div",A,[t("div",E,[t("button",{onClick:S,disabled:n.value,class:"px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors disabled:opacity-50"},s[3]||(s[3]=[t("i",{class:"fas fa-plus mr-2"},null,-1),m("创建对账记录 ",-1)]),8,I),t("button",{onClick:y,disabled:n.value,class:"px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors disabled:opacity-50"},s[4]||(s[4]=[t("i",{class:"fas fa-refresh mr-2"},null,-1),m("刷新 ",-1)]),8,O)]),t("div",P,[M(t("select",{"onUpdate:modelValue":s[0]||(s[0]=o=>x.value.status=o),class:"px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500",onChange:y},s[5]||(s[5]=[t("option",{value:""},"全部状态",-1),t("option",{value:"balanced"},"已平衡",-1),t("option",{value:"variance_detected"},"发现差异",-1),t("option",{value:"pending"},"待处理",-1)]),544),[[$,x.value.status]])])])]),t("div",U,[t("div",Z,[t("table",q,[s[8]||(s[8]=t("thead",{class:"bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"对账日期"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"订单总数"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"总金额"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"支付宝"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"微信支付"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"Stripe"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"差异金额"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"状态"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"创建时间"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"操作")])],-1)),t("tbody",G,[(d(!0),p(z,null,B(i.value,o=>(d(),p("tr",{key:o.id,class:"hover:bg-gray-700 transition-colors"},[t("td",H,l(_(o.reconciliation_date)),1),t("td",J,l(o.total_orders),1),t("td",K,"¥"+l(c(o.total_amount)),1),t("td",Q,"¥"+l(c(o.alipay_amount)),1),t("td",W,"¥"+l(c(o.wechat_amount)),1),t("td",X,"¥"+l(c(o.stripe_amount)),1),t("td",{class:k(["px-6 py-4 whitespace-nowrap text-sm",r(o.variance_amount)])}," ¥"+l(c(Math.abs(o.variance_amount))),3),t("td",Y,[t("span",{class:k(g(o.status))},l(D(o.status)),3)]),t("td",tt,l(u(o.created_at)),1),t("td",et,[t("button",{onClick:N=>V(o.id),class:"text-yellow-400 hover:text-yellow-300 transition-colors mr-3"},s[6]||(s[6]=[t("i",{class:"fas fa-eye mr-1"},null,-1),m("详情 ",-1)]),8,st),o.status==="variance_detected"?(d(),p("button",{key:0,onClick:N=>R(o.id),class:"text-green-400 hover:text-green-300 transition-colors"},s[7]||(s[7]=[t("i",{class:"fas fa-check mr-1"},null,-1),m("处理 ",-1)]),8,at)):h("",!0)])]))),128))])])]),a.value.total>0?(d(),p("div",ot,[t("div",lt," 显示 "+l((a.value.page-1)*a.value.per_page+1)+" 到 "+l(Math.min(a.value.page*a.value.per_page,a.value.total))+" 条， 共 "+l(a.value.total)+" 条记录 ",1),t("div",nt,[t("button",{onClick:s[1]||(s[1]=o=>b(a.value.page-1)),disabled:!a.value.has_prev,class:"px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500 transition-colors"}," 上一页 ",8,it),t("button",{onClick:s[2]||(s[2]=o=>b(a.value.page+1)),disabled:!a.value.has_next,class:"px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500 transition-colors"}," 下一页 ",8,rt)])])):h("",!0)]),i.value.length===0&&!n.value?(d(),p("div",ct,s[9]||(s[9]=[t("i",{class:"fas fa-calculator fa-3x text-gray-500 mb-4"},null,-1),t("p",{class:"text-gray-400 text-lg"},"暂无对账记录",-1),t("p",{class:"text-gray-500 text-sm mt-2"},'点击"创建对账记录"开始财务对账',-1)]))):h("",!0)]))}});export{ut as default};
