var F=(j,I,k)=>new Promise((x,y)=>{var r=_=>{try{f(k.next(_))}catch(T){y(T)}},d=_=>{try{f(k.throw(_))}catch(T){y(T)}},f=_=>_.done?x(_.value):Promise.resolve(_.value).then(r,d);f((k=k.apply(j,I)).next())});import{d as tt,i as P,m as $,k as et,c as l,a as t,t as s,g as M,e as U,n as b,a8 as R,F as N,l as A,p as w,s as C,b as V,z as at,y as st,o as i}from"./index-4iV_uVFP.js";import{_ as nt}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ot={class:"user-detail"},lt={class:"page-header"},it={class:"page-title"},dt={key:0,class:"loading-container"},rt={key:1,class:"error-container"},ut={class:"text-red-400"},_t={key:2,class:"space-y-6"},ct={class:"info-card"},pt={class:"user-info-grid"},mt={class:"info-item"},vt={class:"info-item"},yt={class:"info-item"},gt={class:"info-item"},ht={class:"vip-info"},ft={key:0,class:"vip-expire-info"},bt={class:"text-gray-400"},xt={key:0,class:"text-red-400 ml-2"},qt={key:1,class:"quota-info mt-2"},kt={class:"quota-item"},Tt={class:"text-gray-400"},wt={class:"quota-item"},Ct={class:"text-gray-400"},Vt={class:"info-card"},Dt={class:"stats-grid"},Pt={class:"stat-item"},It={class:"stat-value text-green-400"},Zt={class:"stat-item"},$t={class:"stat-value text-blue-400"},Ut={class:"stat-item"},Et={class:"stat-value text-purple-400"},St={class:"stat-item"},Ft={class:"stat-value text-pink-400"},Mt={class:"stat-item"},Nt={class:"stat-value text-cyan-400"},At={class:"stat-item"},jt={class:"stat-value text-orange-400"},Bt={class:"info-card"},Ot={class:"payment-stats-grid"},Rt={class:"stat-item"},zt={class:"stat-value text-green-400"},Qt={class:"stat-sublabel"},Wt={class:"stat-item"},Gt={class:"stat-value text-cyan-400"},Ht={class:"stat-sublabel"},Yt={class:"stat-item"},Jt={class:"stat-value text-orange-400"},Kt={class:"stat-sublabel"},Lt={class:"info-card"},Xt={class:"quota-details-grid"},te={class:"quota-detail-item"},ee={class:"quota-progress"},ae={class:"quota-bar"},se={class:"quota-text"},ne={class:"quota-detail-item"},oe={class:"quota-progress"},le={class:"quota-bar"},ie={class:"quota-text"},de={class:"info-card"},re={class:"table-container"},ue={class:"data-table"},_e={class:"text-green-400"},ce={class:"text-red-400"},pe={key:1},me={class:"info-card"},ve={class:"filter-form"},ye={class:"form-group"},ge={class:"form-group"},he={class:"form-group"},fe={class:"info-card"},be={class:"card-title"},xe={key:0,class:"loading-container"},qe={key:1,class:"no-data-container"},ke={key:2,class:"table-container"},Te={class:"data-table"},we={class:"text-sm"},Ce={class:"text-sm"},Ve={class:"text-sm"},De={key:0,class:"payment-details"},Pe={key:1,class:"text-gray-400"},Ie={key:1,class:"text-gray-400"},Ze={key:0},$e={key:1,class:"text-gray-400"},Ue={key:0},Ee={class:"text-xs"},Se={key:1,class:"text-gray-400"},Fe={key:0},Me={key:1,class:"text-gray-400"},Ne={class:"text-gray-300"},Ae={key:0},je={class:"text-xs"},Be={class:"text-xs"},Oe={key:1,class:"text-gray-400"},Re={class:"text-cyan-300"},ze={key:0},Qe={class:"text-xs"},We={class:"text-xs"},Ge={key:1,class:"text-gray-400"},He={class:"text-orange-300"},Ye={key:0},Je={class:"text-xs"},Ke={class:"text-xs"},Le={key:1,class:"text-gray-400"},Xe={class:"text-xs text-gray-400"},ta={key:3,class:"pagination"},ea=["disabled"],aa={class:"page-numbers"},sa=["onClick"],na=["disabled"],oa={class:"info-card"},la={class:"filter-form"},ia={class:"form-group"},da={class:"form-group"},ra={class:"form-group"},ua=tt({__name:"UserDetailView",setup(j){const I=at(),k=st(),x=P(!0),y=P(""),r=P({}),d=$({total_spent:0,total_purchases:0,basic_purchases:0,premium_purchases:0,total_consumption:0,current_balance:0,cash_payments:0,quota_payments:0,points_payments:0}),f=P([]),_=P([]),T=$({loadTransactionsCalled:!1,loadUserDetailCalled:!1,mountedCalled:!1,mockDataLength:0}),o=$({record_type:"",time_range:"",content_type:"",page:1,per_page:20}),m=$({page:1,pages:1,total:0,per_page:20,has_prev:!1,has_next:!1,prev_num:null,next_num:null}),E=n=>{if(!n)return"无";try{const e=new Date(n);return isNaN(e.getTime())?"无效日期":e.toLocaleString("zh-CN")}catch(e){return console.error("formatDateTime error:",e),"格式错误"}},z=(n,e)=>!n||n===0?"普通用户":e&&new Date(e)<=new Date?"已过期":n===2?"VIP Pro":n===1?"VIP":"普通用户",Q=n=>n?new Date(n)<=new Date:!1,B=(n,e)=>!e||e===0?0:Math.min(n/e*100,100),W=(n,e)=>n>0&&e&&new Date(e)<=new Date?"vip-expired":!n||n===0?"vip-free":n===2?"vip-premium":"vip-basic",G=n=>({purchase:"购买",consumption:"消费",quota_consumption:"配额消费",quota_purchase:"配额购买",points_earned:"积分获得",points_consumption:"积分消费",points_used:"积分消费",subscription:"订阅",recharge:"充值",refund:"退款",quota_reset:"配额重置",vip_upgrade:"VIP升级",vip_expire:"VIP到期"})[n]||n,H=n=>({basic:"基础内容",premium:"高级内容",vip:"VIP服务",recharge:"充值",subscription:"订阅服务",quota_pack:"配额包"})[n]||n,Y=n=>n>0?"text-green-400":n<0?"text-red-400":"text-gray-400",J=n=>n>0?"text-green-400":n<0?"text-red-400":"text-gray-400",K=n=>n>0?"text-green-400":n<0?"text-red-400":"text-gray-400",L=()=>{const n=[],e=m.page,u=m.pages,c=Math.max(1,e-2),q=Math.min(u,e+2);for(let v=c;v<=q;v++)n.push(v);return n},O=()=>F(this,null,function*(){x.value=!0,y.value="";try{const n=I.params.id;try{console.log("🔍 开始调用用户财务详情API:",`/admin/api/users/${n}/finance`);const e=yield fetch(`/admin/api/users/${n}/finance`,{method:"GET",headers:{"Content-Type":"application/json"},credentials:"include"});if(console.log("📡 API响应状态:",e.status,e.statusText),!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const u=yield e.json();if(console.log("📊 API返回数据:",u),u.success){r.value=u.user,Object.assign(d,u.finance_stats),f.value=u.monthly_data,console.log("✅ 用户财务数据加载成功"),yield g();return}else throw new Error(u.error||"获取用户财务详情失败")}catch(e){console.error("❌ API调用失败:",e),y.value=`API调用失败: ${e.message}`;return}}catch(n){y.value="加载用户详情失败: "+n.message}finally{x.value=!1,console.log("🐛 loadUserDetail 完成，loading:",x.value,"error:",y.value)}}),g=()=>F(this,null,function*(){try{T.loadTransactionsCalled=!0,console.log("🐛 loadTransactions 被调用了!");const n=I.params.id,e=new URLSearchParams({page:o.page.toString(),per_page:o.per_page.toString(),record_type:o.record_type||"",time_range:o.time_range||"",content_type:o.content_type||""});console.log("🐛 查询参数:",e.toString());try{const p=yield(yield fetch(`/admin/api/users/${n}/transactions?${e.toString()}`)).json();if(p.success){_.value=p.data.transactions||[],Object.assign(m,{page:p.data.pagination.page||1,pages:p.data.pagination.pages||1,total:p.data.pagination.total||0,per_page:p.data.pagination.per_page||20,has_prev:p.data.pagination.has_prev||!1,has_next:p.data.pagination.has_next||!1,prev_num:p.data.pagination.prev_num||null,next_num:p.data.pagination.next_num||null});return}else throw new Error(p.error||"获取交易记录失败")}catch(h){console.warn("API调用失败，使用模拟数据:",h)}const u=[{id:1,type:"purchase",date:"2024-01-01T10:00:00Z",description:"支付宝充值",article_title:null,content_type:"recharge",payment_method:"alipay",amount:2e4,quota_change:0,quota_type:null,points_change:200,balance_before:0,balance_after:2e4,transaction_id:"ALI_20240101_001"},{id:2,type:"purchase",date:"2024-01-02T09:15:00Z",description:"购买VIP1个月",article_title:null,content_type:"vip",payment_method:"balance",amount:-12800,quota_change:0,quota_type:null,points_change:0,balance_before:2e4,balance_after:7200,transaction_id:"VIP_20240102_001"},{id:3,type:"purchase",date:"2024-01-03T14:20:00Z",description:"购买文章: 塔罗牌高级解读技巧",article_title:"塔罗牌高级解读技巧",content_type:"premium",payment_method:"balance",amount:-1e3,quota_change:0,quota_type:null,points_change:10,balance_before:7200,balance_after:6200,transaction_id:"ART_20240103_001"},{id:4,type:"consumption",date:"2024-01-04T16:45:00Z",description:"消费: 爱情塔罗牌阵详解",article_title:"爱情塔罗牌阵详解",content_type:"premium",payment_method:null,cost_type:"quota",amount:0,quota_change:-1,quota_type:"premium",points_change:0,balance_before:6200,balance_after:6200,transaction_id:null},{id:5,type:"consumption",date:"2024-01-05T11:30:00Z",description:"消费: 塔罗牌入门指南",article_title:"塔罗牌入门指南",content_type:"basic",payment_method:null,cost_type:"balance",amount:-500,quota_change:0,quota_type:null,points_change:0,balance_before:6200,balance_after:5700,transaction_id:null},{id:6,type:"consumption",date:"2024-01-06T13:15:00Z",description:"消费: 塔罗牌牌意解析",article_title:"塔罗牌牌意解析",content_type:"basic",payment_method:null,cost_type:"points",amount:0,quota_change:0,quota_type:null,points_change:-50,balance_before:5700,balance_after:5700,transaction_id:null},{id:7,type:"points_earned",date:"2024-01-07T08:00:00Z",description:"每日签到奖励",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:0,quota_type:null,points_change:10,balance_before:5700,balance_after:5700,transaction_id:null},{id:8,type:"quota_reset",date:"2024-01-08T00:00:00Z",description:"VIP配额月度重置",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:10,quota_type:"basic",points_change:0,balance_before:5700,balance_after:5700,transaction_id:null},{id:9,type:"refund",date:"2024-01-09T15:30:00Z",description:"文章购买退款: 塔罗牌高级解读技巧",article_title:"塔罗牌高级解读技巧",content_type:"premium",payment_method:"balance",amount:1e3,quota_change:0,quota_type:null,points_change:-10,balance_before:5700,balance_after:6700,transaction_id:"REF_20240109_001"},{id:10,type:"subscription",date:"2024-01-10T12:00:00Z",description:"订阅塔罗师Alice",article_title:null,content_type:"subscription",payment_method:"balance",amount:-2e3,quota_change:0,quota_type:null,points_change:20,balance_before:6700,balance_after:4700,transaction_id:"SUB_20240110_001"},{id:11,type:"points_consumption",date:"2024-01-11T09:30:00Z",description:"积分兑换高级配额",article_title:null,content_type:null,payment_method:null,cost_type:"points",amount:0,quota_change:5,quota_type:"premium",points_change:-500,balance_before:4700,balance_after:4700,transaction_id:null},{id:12,type:"points_earned",date:"2024-01-12T08:00:00Z",description:"每日签到奖励",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:0,quota_type:null,points_change:10,balance_before:4700,balance_after:4700,transaction_id:null},{id:13,type:"points_earned",date:"2024-01-13T14:20:00Z",description:"推荐新用户奖励",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:0,quota_type:null,points_change:100,balance_before:4700,balance_after:4700,transaction_id:null},{id:14,type:"quota_consumption",date:"2024-01-14T16:45:00Z",description:"查看高级塔罗解读",article_title:"深度塔罗牌解读：爱情运势",content_type:"premium",payment_method:null,cost_type:"quota",amount:0,quota_change:-1,quota_type:"premium",points_change:0,balance_before:4700,balance_after:4700,transaction_id:null},{id:15,type:"quota_reset",date:"2024-02-01T00:00:00Z",description:"VIP配额月度重置",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:20,quota_type:"premium",points_change:0,balance_before:4700,balance_after:4700,transaction_id:null},{id:16,type:"points_consumption",date:"2024-02-02T11:15:00Z",description:"积分购买文章",article_title:"塔罗牌占卜技巧大全",content_type:"basic",payment_method:null,cost_type:"points",amount:0,quota_change:0,quota_type:null,points_change:-80,balance_before:4700,balance_after:4700,transaction_id:null},{id:17,type:"points_earned",date:"2024-02-03T15:30:00Z",description:"优质评论奖励",article_title:"塔罗牌占卜技巧大全",content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:0,quota_type:null,points_change:20,balance_before:4700,balance_after:4700,transaction_id:null},{id:18,type:"quota_consumption",date:"2024-02-04T10:20:00Z",description:"查看基础塔罗内容",article_title:"塔罗牌基础知识入门",content_type:"basic",payment_method:null,cost_type:"quota",amount:0,quota_change:-1,quota_type:"basic",points_change:0,balance_before:4700,balance_after:4700,transaction_id:null},{id:19,type:"points_earned",date:"2024-02-05T18:00:00Z",description:"春节活动奖励",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:0,quota_type:null,points_change:200,balance_before:4700,balance_after:4700,transaction_id:"EVENT_CNY_2024"},{id:20,type:"quota_purchase",date:"2024-02-06T13:45:00Z",description:"购买高级配额包",article_title:null,content_type:"quota_pack",payment_method:"balance",amount:-1500,quota_change:10,quota_type:"premium",points_change:15,balance_before:4700,balance_after:3200,transaction_id:"QUOTA_20240206_001"}];console.log("🐛 模拟数据长度:",u.length),T.mockDataLength=u.length;let c=u;o.record_type&&(c=c.filter(h=>h.type===o.record_type)),o.content_type&&(c=c.filter(h=>h.content_type===o.content_type)),console.log("🐛 过滤后数据长度:",c.length);const q=c.length,v=Math.ceil(q/o.per_page),D=(o.page-1)*o.per_page,Z=D+o.per_page,a=c.slice(D,Z);console.log("🐛 分页后数据长度:",a.length),console.log("🐛 第一条记录:",a[0]),_.value=a,console.log("🐛 transactions.value 设置完成，长度:",_.value.length),Object.assign(m,{page:o.page,pages:v,total:q,per_page:o.per_page,has_prev:o.page>1,has_next:o.page<v,prev_num:o.page>1?o.page-1:null,next_num:o.page<v?o.page+1:null})}catch(n){console.error("加载交易记录失败:",n)}}),S=n=>{n&&n!==o.page&&(o.page=n,g())},X=()=>{k.push("/users")};return et(()=>{O(),g()}),(n,e)=>{var u,c,q,v,D,Z;return i(),l("div",ot,[t("div",lt,[t("h1",it,"用户财务详情 - "+s(r.value.username||"加载中..."),1),t("div",{class:"header-actions"},[t("button",{class:"btn-secondary",onClick:X},e[8]||(e[8]=[t("i",{class:"fas fa-arrow-left mr-2"},null,-1),M("返回用户列表 ",-1)]))])]),x.value?(i(),l("div",dt,e[9]||(e[9]=[t("i",{class:"fas fa-spinner fa-spin text-2xl text-gray-400"},null,-1),t("p",{class:"text-gray-400 mt-2"},"加载中...",-1)]))):y.value?(i(),l("div",rt,[e[10]||(e[10]=t("i",{class:"fas fa-exclamation-triangle text-4xl text-red-400 mb-4"},null,-1)),t("p",ut,s(y.value),1),t("button",{class:"btn-primary mt-4",onClick:O},"重试")])):(i(),l("div",_t,[t("div",ct,[e[15]||(e[15]=t("h2",{class:"card-title"},"用户信息",-1)),t("div",pt,[t("div",mt,[e[11]||(e[11]=t("label",null,"用户名",-1)),t("p",null,s(r.value.username||"testuser"),1)]),t("div",vt,[e[12]||(e[12]=t("label",null,"邮箱",-1)),t("p",null,s(r.value.email||"<EMAIL>"),1)]),t("div",yt,[e[13]||(e[13]=t("label",null,"注册时间",-1)),t("p",null,s(r.value.created_at?E(r.value.created_at):"2024/1/1 08:00:00"),1)]),t("div",gt,[e[14]||(e[14]=t("label",null,"VIP等级",-1)),t("div",ht,[t("span",{class:b(["vip-badge",W((u=r.value.finance)==null?void 0:u.vip_level,(c=r.value.finance)==null?void 0:c.vip_expire_at)])},s(z((q=r.value.finance)==null?void 0:q.vip_level,(v=r.value.finance)==null?void 0:v.vip_expire_at)),3),((D=r.value.finance)==null?void 0:D.vip_level)>0&&((Z=r.value.finance)!=null&&Z.vip_expire_at)?(i(),l("div",ft,[t("small",bt,[M(" 到期时间: "+s(E(r.value.finance.vip_expire_at))+" ",1),Q(r.value.finance.vip_expire_at)?(i(),l("span",xt,"(已过期)")):U("",!0)])])):U("",!0),r.value.finance?(i(),l("div",qt,[t("div",kt,[t("small",Tt," 基础配额: "+s(r.value.finance.basic_quota-r.value.finance.basic_quota_used||0)+"/"+s(r.value.finance.basic_quota||0),1)]),t("div",wt,[t("small",Ct," 高级配额: "+s(r.value.finance.premium_quota-r.value.finance.premium_quota_used||0)+"/"+s(r.value.finance.premium_quota||0),1)])])):U("",!0)])])])]),t("div",Vt,[e[22]||(e[22]=t("h2",{class:"card-title"},"财务统计概览",-1)),t("div",Dt,[t("div",Pt,[t("div",It,"¥"+s(((d.total_cash_used||0)/100).toFixed(2)),1),e[16]||(e[16]=t("div",{class:"stat-label"},"现金支出总额",-1))]),t("div",Zt,[t("div",$t,s(d.total_purchases||0),1),e[17]||(e[17]=t("div",{class:"stat-label"},"购买次数",-1))]),t("div",Ut,[t("div",Et,s(d.basic_purchases||0),1),e[18]||(e[18]=t("div",{class:"stat-label"},"基础内容",-1))]),t("div",St,[t("div",Ft,s(d.premium_purchases||0),1),e[19]||(e[19]=t("div",{class:"stat-label"},"高级内容",-1))]),t("div",Mt,[t("div",Nt,s(d.total_quota_used||0)+"个",1),e[20]||(e[20]=t("div",{class:"stat-label"},"配额使用总量",-1))]),t("div",At,[t("div",jt,s(d.total_points_used||0)+"分",1),e[21]||(e[21]=t("div",{class:"stat-label"},"积分使用总量",-1))])])]),t("div",Bt,[e[26]||(e[26]=t("h2",{class:"card-title"},"支付方式统计",-1)),t("div",Ot,[t("div",Rt,[t("div",zt,"¥"+s(((d.total_cash_used||0)/100).toFixed(2)),1),e[23]||(e[23]=t("div",{class:"stat-label"},"现金使用总额",-1)),t("div",Qt,s(d.cash_payments||0)+"次支付",1)]),t("div",Wt,[t("div",Gt,s(d.total_quota_used||0)+"个",1),e[24]||(e[24]=t("div",{class:"stat-label"},"配额使用总量",-1)),t("div",Ht,s(d.quota_payments||0)+"次支付",1)]),t("div",Yt,[t("div",Jt,s(d.total_points_used||0)+"分",1),e[25]||(e[25]=t("div",{class:"stat-label"},"积分使用总量",-1)),t("div",Kt,s(d.points_payments||0)+"次支付",1)])])]),t("div",Lt,[e[29]||(e[29]=t("h2",{class:"card-title"},"配额使用详情",-1)),t("div",Xt,[t("div",te,[e[27]||(e[27]=t("div",{class:"quota-type"},"基础配额",-1)),t("div",ee,[t("div",ae,[t("div",{class:"quota-used",style:R({width:B(d.basic_quota_used,d.basic_quota_total)+"%"})},null,4)]),t("div",se,s(d.basic_quota_used||0)+" / "+s(d.basic_quota_total||0)+" (剩余: "+s(d.basic_quota_remaining||0)+") ",1)])]),t("div",ne,[e[28]||(e[28]=t("div",{class:"quota-type"},"高级配额",-1)),t("div",oe,[t("div",le,[t("div",{class:"quota-used premium",style:R({width:B(d.premium_quota_used,d.premium_quota_total)+"%"})},null,4)]),t("div",ie,s(d.premium_quota_used||0)+" / "+s(d.premium_quota_total||0)+" (剩余: "+s(d.premium_quota_remaining||0)+") ",1)])])])]),t("div",de,[e[32]||(e[32]=t("h2",{class:"card-title"},"月度财务统计",-1)),t("div",re,[t("table",ue,[e[31]||(e[31]=t("thead",null,[t("tr",null,[t("th",null,"月份"),t("th",null,"购买金额"),t("th",null,"消费金额"),t("th",null,"净收支")])],-1)),t("tbody",null,[f.value&&f.value.length>0?(i(!0),l(N,{key:0},A(f.value.slice(0,12),(a,h)=>(i(),l("tr",{key:`month-${h}`},[t("td",null,s(a.month||"无"),1),t("td",_e,"¥"+s(((a.purchases||0)/100).toFixed(2)),1),t("td",ce,"¥"+s(((a.consumption||0)/100).toFixed(2)),1),t("td",{class:b((a.net||0)>=0?"text-green-400":"text-red-400")}," ¥"+s(((a.net||0)/100).toFixed(2)),3)]))),128)):(i(),l("tr",pe,e[30]||(e[30]=[t("td",{colspan:"4",class:"text-center text-gray-400"},"暂无月度数据",-1)])))])])])]),t("div",me,[e[39]||(e[39]=t("h2",{class:"card-title"},"财务记录筛选",-1)),t("div",ve,[t("div",ye,[e[34]||(e[34]=t("label",null,"记录类型",-1)),w(t("select",{"onUpdate:modelValue":e[0]||(e[0]=a=>o.record_type=a),class:"form-select",onChange:g},e[33]||(e[33]=[V('<option value="" data-v-54946d16>全部记录</option><option value="purchase" data-v-54946d16>购买记录</option><option value="consumption" data-v-54946d16>消费记录</option><option value="quota" data-v-54946d16>配额使用</option><option value="points" data-v-54946d16>积分记录</option><option value="subscription" data-v-54946d16>订阅记录</option>',6)]),544),[[C,o.record_type]])]),t("div",ge,[e[36]||(e[36]=t("label",null,"时间范围",-1)),w(t("select",{"onUpdate:modelValue":e[1]||(e[1]=a=>o.time_range=a),class:"form-select",onChange:g},e[35]||(e[35]=[V('<option value="" data-v-54946d16>全部时间</option><option value="today" data-v-54946d16>今天</option><option value="week" data-v-54946d16>最近一周</option><option value="month" data-v-54946d16>最近一月</option><option value="quarter" data-v-54946d16>最近三月</option>',5)]),544),[[C,o.time_range]])]),t("div",he,[e[38]||(e[38]=t("label",null,"内容类型",-1)),w(t("select",{"onUpdate:modelValue":e[2]||(e[2]=a=>o.content_type=a),class:"form-select",onChange:g},e[37]||(e[37]=[V('<option value="" data-v-54946d16>全部类型</option><option value="basic" data-v-54946d16>基础内容</option><option value="premium" data-v-54946d16>高级内容</option><option value="vip" data-v-54946d16>VIP服务</option><option value="recharge" data-v-54946d16>充值</option>',5)]),544),[[C,o.content_type]])])])]),t("div",fe,[t("h2",be,"详细财务流水（原始单据）- 共"+s(_.value.length)+"条记录",1),x.value?(i(),l("div",xe,e[40]||(e[40]=[t("i",{class:"fas fa-spinner fa-spin text-2xl text-gray-400"},null,-1),t("p",{class:"text-gray-400 mt-2"},"加载中...",-1)]))):_.value.length===0?(i(),l("div",qe,e[41]||(e[41]=[t("i",{class:"fas fa-receipt text-4xl text-gray-600 mb-4"},null,-1),t("p",{class:"text-gray-400"},"暂无财务记录",-1)]))):(i(),l("div",ke,[t("table",Te,[e[42]||(e[42]=t("thead",null,[t("tr",null,[t("th",null,"时间"),t("th",null,"类型"),t("th",null,"描述"),t("th",null,"支付详情"),t("th",null,"内容类型"),t("th",null,"现金变动"),t("th",null,"配额变动"),t("th",null,"积分变动"),t("th",null,"现金余额"),t("th",null,"配额余额"),t("th",null,"积分余额"),t("th",null,"交易ID")])],-1)),t("tbody",null,[(i(!0),l(N,null,A(_.value,a=>(i(),l("tr",{key:`${a.type}-${a.id}`},[t("td",we,s(E(a.date)),1),t("td",null,[t("span",{class:b(["type-badge",`type-${a.type}`])},s(G(a.type)),3)]),t("td",Ce,s(a.description),1),t("td",Ve,[a.payment_details?(i(),l("span",De,s(a.payment_details),1)):(i(),l("span",Pe,"-"))]),t("td",null,[a.content_type?(i(),l("span",{key:0,class:b(["content-badge",`content-${a.content_type}`])},s(H(a.content_type)),3)):(i(),l("span",Ie,"-"))]),t("td",{class:b(Y(a.amount))},[a.amount!==0?(i(),l("span",Ze,s(a.amount>0?"+":"")+"¥"+s((Math.abs(a.amount)/100).toFixed(2)),1)):(i(),l("span",$e,"-"))],2),t("td",{class:b(J(a.quota_change))},[a.quota_change?(i(),l("span",Ue,[M(s(a.quota_change>0?"+":"")+s(a.quota_change)+" ",1),t("span",Ee," ("+s(a.quota_type==="basic"?"基础配额":a.quota_type==="premium"?"高级配额":a.quota_type||"配额")+") ",1)])):(i(),l("span",Se,"-"))],2),t("td",{class:b(K(a.points_change))},[a.points_change?(i(),l("span",Fe,s(a.points_change>0?"+":"")+s(a.points_change),1)):(i(),l("span",Me,"-"))],2),t("td",Ne,[a.balance_before!==null||a.balance_after!==null?(i(),l("div",Ae,[t("div",je,"前: ¥"+s(((a.balance_before||0)/100).toFixed(2)),1),t("div",Be,"后: ¥"+s(((a.balance_after||0)/100).toFixed(2)),1)])):(i(),l("span",Oe,"-"))]),t("td",Re,[a.quota_before!==null||a.quota_after!==null?(i(),l("div",ze,[t("div",Qe,"前: "+s(a.quota_before||0),1),t("div",We,"后: "+s(a.quota_after||0),1)])):(i(),l("span",Ge,"-"))]),t("td",He,[a.points_before!==null||a.points_after!==null?(i(),l("div",Ye,[t("div",Je,"前: "+s(a.points_before||0),1),t("div",Ke,"后: "+s(a.points_after||0),1)])):(i(),l("span",Le,"-"))]),t("td",Xe,s(a.transaction_id||"-"),1)]))),128))])])])),m.pages>1?(i(),l("div",ta,[t("button",{class:"btn-secondary",disabled:!m.has_prev,onClick:e[3]||(e[3]=a=>S(m.prev_num))}," 上一页 ",8,ea),t("div",aa,[(i(!0),l(N,null,A(L(),a=>(i(),l("button",{key:a,class:b(["page-btn",a===m.page?"active":""]),onClick:h=>S(a)},s(a),11,sa))),128))]),t("button",{class:"btn-secondary",disabled:!m.has_next,onClick:e[4]||(e[4]=a=>S(m.next_num))}," 下一页 ",8,na)])):U("",!0)]),t("div",oa,[e[49]||(e[49]=t("h2",{class:"card-title"},"财务记录筛选",-1)),t("div",la,[t("div",ia,[e[44]||(e[44]=t("label",null,"记录类型",-1)),w(t("select",{"onUpdate:modelValue":e[5]||(e[5]=a=>o.record_type=a),class:"form-select",onChange:g},e[43]||(e[43]=[V('<option value="" data-v-54946d16>全部记录</option><option value="purchase" data-v-54946d16>购买记录</option><option value="consumption" data-v-54946d16>消费记录</option><option value="quota" data-v-54946d16>配额使用</option><option value="points" data-v-54946d16>积分记录</option><option value="subscription" data-v-54946d16>订阅记录</option>',6)]),544),[[C,o.record_type]])]),t("div",da,[e[46]||(e[46]=t("label",null,"时间范围",-1)),w(t("select",{"onUpdate:modelValue":e[6]||(e[6]=a=>o.time_range=a),class:"form-select",onChange:g},e[45]||(e[45]=[V('<option value="" data-v-54946d16>全部时间</option><option value="today" data-v-54946d16>今天</option><option value="week" data-v-54946d16>最近一周</option><option value="month" data-v-54946d16>最近一月</option><option value="quarter" data-v-54946d16>最近三月</option>',5)]),544),[[C,o.time_range]])]),t("div",ra,[e[48]||(e[48]=t("label",null,"内容类型",-1)),w(t("select",{"onUpdate:modelValue":e[7]||(e[7]=a=>o.content_type=a),class:"form-select",onChange:g},e[47]||(e[47]=[V('<option value="" data-v-54946d16>全部类型</option><option value="basic" data-v-54946d16>基础内容</option><option value="premium" data-v-54946d16>高级内容</option><option value="vip" data-v-54946d16>VIP服务</option><option value="recharge" data-v-54946d16>充值</option>',5)]),544),[[C,o.content_type]])])])])]))])}}}),ma=nt(ua,[["__scopeId","data-v-54946d16"]]);export{ma as default};
