var k=(w,m,o)=>new Promise((r,d)=>{var u=n=>{try{f(o.next(n))}catch(p){d(p)}},g=n=>{try{f(o.throw(n))}catch(p){d(p)}},f=n=>n.done?r(n.value):Promise.resolve(n.value).then(u,g);f((o=o.apply(w,m)).next())});import{d as U,i as C,m as j,k as I,c as i,a as t,g as y,p as _,v as L,s as N,b as T,t as a,e as M,F as $,l as E,E as b,n as v,o as c}from"./index-4iV_uVFP.js";const q={class:"upload-logs"},G={class:"admin-card p-4 mb-6"},K={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},A={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},H={class:"admin-card p-4 bg-gradient-to-br from-green-900 to-emerald-900 border border-green-700"},J={class:"flex items-center justify-between"},O={class:"text-2xl font-bold text-white"},P={class:"admin-card p-4 bg-gradient-to-br from-red-900 to-pink-900 border border-red-700"},Q={class:"flex items-center justify-between"},R={class:"text-2xl font-bold text-white"},W={class:"admin-card p-4 bg-gradient-to-br from-blue-900 to-indigo-900 border border-blue-700"},X={class:"flex items-center justify-between"},Y={class:"text-2xl font-bold text-white"},Z={class:"admin-card p-4 bg-gradient-to-br from-yellow-900 to-amber-900 border border-yellow-700"},tt={class:"flex items-center justify-between"},et={class:"text-2xl font-bold text-white"},st={class:"admin-card p-4 mb-6 overflow-x-auto table-responsive"},lt={class:"table-admin w-full"},ot={class:"px-4 py-3 border-t border-gray-700"},at={class:"px-4 py-3 border-t border-gray-700 max-w-xs truncate"},rt={class:"flex items-center"},nt={class:"text-white"},dt={class:"px-4 py-3 border-t border-gray-700 text-gray-300"},it={class:"px-4 py-3 border-t border-gray-700"},ct={class:"px-4 py-3 border-t border-gray-700 text-sm text-gray-300"},ut={class:"px-4 py-3 border-t border-gray-700 text-gray-300"},ft={class:"px-4 py-3 border-t border-gray-700 max-w-xs truncate"},pt={key:0,class:"text-red-400 text-sm"},xt={key:1,class:"text-gray-500"},mt={class:"px-4 py-3 border-t border-gray-700"},yt={class:"flex flex-wrap gap-1"},bt=["onClick"],gt=["onClick"],_t=["onClick"],vt={key:0},kt=U({__name:"UploadLogsView",setup(w){const m=C(!1),o=C([]),r=j({date_from:"",date_to:"",status:""}),d=j({success_count:156,failed_count:12,total_size:2048576e3,today_count:23}),u=()=>k(this,null,function*(){m.value=!0;try{console.log("刷新上传日志"),o.value=[{id:1,filename:"tarot_reading_guide.pdf",file_size:2048576,status:"success",upload_time:"2025-08-03 14:30:25",duration:2.5,error_message:null},{id:2,filename:"card_meanings.docx",file_size:1024e3,status:"failed",upload_time:"2025-08-03 14:25:10",duration:null,error_message:"文件格式不支持"}]}catch(l){console.error("加载日志失败:",l),b.error("加载日志失败")}finally{m.value=!1}}),g=()=>{console.log("搜索日志:",r),u()},f=()=>{confirm("确定要清空所有上传日志吗？此操作不可恢复。")&&(o.value=[],b.success("日志已清空"))},n=l=>{console.log("查看日志详情:",l)},p=l=>{console.log("重新上传:",l),b.info("重新上传功能开发中")},z=l=>{if(confirm(`确定要删除文件"${l.filename}"的上传日志吗？`)){const e=o.value.findIndex(s=>s.id===l.id);e!==-1&&(o.value.splice(e,1),b.success("日志已删除"))}},h=l=>{if(l===0)return"0 B";const e=1024,s=["B","KB","MB","GB"],x=Math.floor(Math.log(l)/Math.log(e));return parseFloat((l/Math.pow(e,x)).toFixed(2))+" "+s[x]},B=l=>new Date(l).toLocaleString(),S=l=>{var s;switch((s=l.split(".").pop())==null?void 0:s.toLowerCase()){case"pdf":return"fas fa-file-pdf text-red-400";case"doc":case"docx":return"fas fa-file-word text-blue-400";case"txt":case"md":return"fas fa-file-alt text-gray-400";default:return"fas fa-file text-gray-400"}},V=l=>{switch(l){case"success":return"px-2 py-1 rounded-full text-xs bg-green-700 text-green-300";case"failed":return"px-2 py-1 rounded-full text-xs bg-red-700 text-red-300";case"pending":return"px-2 py-1 rounded-full text-xs bg-yellow-700 text-yellow-300";case"processing":return"px-2 py-1 rounded-full text-xs bg-blue-700 text-blue-300";default:return"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300"}},D=l=>{switch(l){case"success":return"fas fa-check";case"failed":return"fas fa-times";case"pending":return"fas fa-clock";case"processing":return"fas fa-spinner fa-spin";default:return"fas fa-question"}},F=l=>{switch(l){case"success":return"成功";case"failed":return"失败";case"pending":return"等待中";case"processing":return"处理中";default:return"未知"}};return I(()=>{u()}),(l,e)=>(c(),i("div",q,[t("div",{class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},[e[5]||(e[5]=t("h1",{class:"text-2xl font-bold text-white"},"上传日志",-1)),t("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[t("button",{onClick:f,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},e[3]||(e[3]=[t("i",{class:"fas fa-trash mr-2"},null,-1),y("清空日志 ",-1)])),t("button",{onClick:u,class:"admin-btn-primary px-4 py-2 rounded-lg"},e[4]||(e[4]=[t("i",{class:"fas fa-sync-alt mr-2"},null,-1),y("刷新日志 ",-1)]))])]),t("div",G,[t("div",K,[t("div",null,[e[6]||(e[6]=t("label",{for:"date_from",class:"block mb-2 text-gray-300"},"开始日期",-1)),_(t("input",{type:"date",id:"date_from","onUpdate:modelValue":e[0]||(e[0]=s=>r.date_from=s),class:"form-input w-full px-3 py-2 rounded-lg"},null,512),[[L,r.date_from]])]),t("div",null,[e[7]||(e[7]=t("label",{for:"date_to",class:"block mb-2 text-gray-300"},"结束日期",-1)),_(t("input",{type:"date",id:"date_to","onUpdate:modelValue":e[1]||(e[1]=s=>r.date_to=s),class:"form-input w-full px-3 py-2 rounded-lg"},null,512),[[L,r.date_to]])]),t("div",null,[e[9]||(e[9]=t("label",{for:"status",class:"block mb-2 text-gray-300"},"上传状态",-1)),_(t("select",{id:"status","onUpdate:modelValue":e[2]||(e[2]=s=>r.status=s),class:"form-input w-full px-3 py-2 rounded-lg"},e[8]||(e[8]=[T('<option value="">所有状态</option><option value="success">成功</option><option value="failed">失败</option><option value="pending">等待中</option><option value="processing">处理中</option>',5)]),512),[[N,r.status]])]),t("div",{class:"flex items-end"},[t("button",{onClick:g,class:"admin-btn-primary px-4 py-2 rounded-lg w-full"},e[10]||(e[10]=[t("i",{class:"fas fa-search mr-2"},null,-1),y("筛选日志 ",-1)]))])])]),t("div",A,[t("div",H,[t("div",J,[t("div",null,[e[11]||(e[11]=t("h2",{class:"text-gray-300 mb-1"},"成功上传",-1)),t("p",O,a(d.success_count),1)]),e[12]||(e[12]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-green-800 text-green-200"},[t("i",{class:"fas fa-check text-xl"})],-1))])]),t("div",P,[t("div",Q,[t("div",null,[e[13]||(e[13]=t("h2",{class:"text-gray-300 mb-1"},"失败上传",-1)),t("p",R,a(d.failed_count),1)]),e[14]||(e[14]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-red-800 text-red-200"},[t("i",{class:"fas fa-times text-xl"})],-1))])]),t("div",W,[t("div",X,[t("div",null,[e[15]||(e[15]=t("h2",{class:"text-gray-300 mb-1"},"总文件大小",-1)),t("p",Y,a(h(d.total_size)),1)]),e[16]||(e[16]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-blue-800 text-blue-200"},[t("i",{class:"fas fa-hdd text-xl"})],-1))])]),t("div",Z,[t("div",tt,[t("div",null,[e[17]||(e[17]=t("h2",{class:"text-gray-300 mb-1"},"今日上传",-1)),t("p",et,a(d.today_count),1)]),e[18]||(e[18]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-yellow-800 text-yellow-200"},[t("i",{class:"fas fa-calendar-day text-xl"})],-1))])])]),t("div",st,[t("table",lt,[e[23]||(e[23]=t("thead",null,[t("tr",null,[t("th",{class:"px-4 py-3 text-left"},"ID"),t("th",{class:"px-4 py-3 text-left"},"文件名"),t("th",{class:"px-4 py-3 text-left"},"文件大小"),t("th",{class:"px-4 py-3 text-left"},"上传状态"),t("th",{class:"px-4 py-3 text-left"},"上传时间"),t("th",{class:"px-4 py-3 text-left"},"耗时"),t("th",{class:"px-4 py-3 text-left"},"错误信息"),t("th",{class:"px-4 py-3 text-left"},"操作")])],-1)),t("tbody",null,[(c(!0),i($,null,E(o.value,s=>(c(),i("tr",{key:s.id,class:"hover:bg-gray-700/50 transition-colors"},[t("td",ot,a(s.id),1),t("td",at,[t("div",rt,[t("i",{class:v([S(s.filename),"mr-2"])},null,2),t("span",nt,a(s.filename),1)])]),t("td",dt,a(h(s.file_size)),1),t("td",it,[t("span",{class:v(V(s.status))},[t("i",{class:v([D(s.status),"mr-1"])},null,2),y(" "+a(F(s.status)),1)],2)]),t("td",ct,a(B(s.upload_time)),1),t("td",ut,a(s.duration?s.duration+"s":"-"),1),t("td",ft,[s.error_message?(c(),i("span",pt,a(s.error_message),1)):(c(),i("span",xt,"-"))]),t("td",mt,[t("div",yt,[t("button",{onClick:x=>n(s),class:"admin-btn-secondary px-2 py-1 rounded text-sm",title:"查看详情"},e[19]||(e[19]=[t("i",{class:"fas fa-eye"},null,-1)]),8,bt),s.status==="failed"?(c(),i("button",{key:0,onClick:x=>p(s),class:"admin-btn-primary px-2 py-1 rounded text-sm",title:"重新上传"},e[20]||(e[20]=[t("i",{class:"fas fa-redo"},null,-1)]),8,gt)):M("",!0),t("button",{onClick:x=>z(s),class:"admin-btn-danger px-2 py-1 rounded text-sm",title:"删除日志"},e[21]||(e[21]=[t("i",{class:"fas fa-trash-alt"},null,-1)]),8,_t)])])]))),128)),o.value.length===0?(c(),i("tr",vt,e[22]||(e[22]=[t("td",{colspan:"8",class:"px-4 py-8 text-center text-gray-400"},[t("i",{class:"fas fa-file-upload text-4xl mb-4"}),t("p",null,"暂无上传日志")],-1)]))):M("",!0)])])])]))}});export{kt as default};
