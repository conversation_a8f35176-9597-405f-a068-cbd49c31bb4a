import{h as n}from"./index-B79VaFD-.js";const a={getFinanceStats(){return n.get("/finance/stats")},getList(e){return n.get("/admin/api/finance",{params:e})},getOrdersList(e){return n.get("/finance/orders",{params:e})},getOrderDetail(e){return n.get(`/finance/orders/${e}`)},getReconciliationList(e){return n.get("/finance/reconciliation",{params:e})},createReconciliation(e){return n.post("/finance/reconciliation",{date:e})},getRevenueTrend(e="30d"){return n.get("/finance/revenue-trend",{params:{period:e}})},getPaymentMethodStats(e="30d"){return n.get("/finance/payment-methods",{params:{period:e}})},getAnomalyList(e){return n.get("/finance/anomalies",{params:e})},resolveAnomaly(e,t){return n.post(`/finance/anomalies/${e}/resolve`,{resolution:t})}};export{a as f};
