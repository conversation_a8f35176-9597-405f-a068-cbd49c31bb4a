import{h as e}from"./index-B79VaFD-.js";const s={getList(t){return e.get("/articles",{params:t})},getDetail(t){return e.get(`/articles/${t}`)},create(t){return e.post("/articles",t)},update(t,r){return e.put(`/articles/${t}`,r)},delete(t){return e.delete(`/articles/${t}`)},batchDelete(t){return e.post("/articles/batch_delete",{ids:t})},updateStatus(t,r){return e.put(`/articles/${t}/status`,{status:r})},togglePublish(t,r){return e.put(`/articles/${t}/publish`,{is_published:r})},togglePremium(t,r){return e.put(`/articles/${t}/premium`,{is_premium:r})},getStats(){return e.get("/articles/stats")},uploadCover(t){const r=new FormData;return r.append("file",t),e.post("/articles/upload-cover",r,{headers:{"Content-Type":"multipart/form-data"}})},getTagSuggestions(t){return e.get("/articles/tag-suggestions",{params:{q:t}})},getBuyers(t){return e.get(`/article-buyers/${t}`)}};export{s as a};
