var _=(f,g,n)=>new Promise((y,r)=>{var a=i=>{try{d(n.next(i))}catch(c){r(c)}},p=i=>{try{d(n.throw(i))}catch(c){r(c)}},d=i=>i.done?y(i.value):Promise.resolve(i.value).then(a,p);d((n=n.apply(f,g)).next())});import{d as L,i as x,k as N,c as m,a as t,t as o,p as b,v as O,s as k,g as C,e as B,F as I,l as P,n as S,o as v}from"./index-B79VaFD-.js";import{f as U}from"./finance-D9qcS87O.js";const $={class:"min-h-screen bg-gray-900 text-white"},z={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},A={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},E={class:"flex items-center justify-between"},Q={class:"text-2xl font-bold text-white"},q={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},G={class:"flex items-center justify-between"},H={class:"text-2xl font-bold text-white"},J={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},K={class:"flex items-center justify-between"},R={class:"text-2xl font-bold text-white"},W={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},X={class:"flex items-center justify-between"},Y={class:"text-2xl font-bold text-white"},Z={class:"bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700"},tt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},et={class:"bg-gray-800 rounded-lg border border-gray-700 overflow-hidden"},st={class:"overflow-x-auto"},at={class:"w-full"},lt={class:"divide-y divide-gray-700"},ot={class:"px-6 py-4 whitespace-nowrap text-sm text-white"},rt={class:"px-6 py-4 whitespace-nowrap text-sm text-white"},nt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},it={class:"px-6 py-4 whitespace-nowrap text-sm text-yellow-400"},dt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},pt={class:"px-6 py-4 whitespace-nowrap"},ut={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-300"},xt={class:"px-6 py-4 whitespace-nowrap text-sm"},ct=["onClick"],gt={key:0,class:"bg-gray-700 px-6 py-3 flex items-center justify-between"},yt={class:"text-sm text-gray-400"},mt={class:"flex space-x-2"},vt=["disabled"],bt=["disabled"],kt=L({__name:"OrderListView",setup(f){const g=x(!1),n=x(""),y=x([]),r=x({payment_method:"",status:""}),a=x({page:1,pages:1,per_page:20,total:0,has_prev:!1,has_next:!1}),p=x({total_today:0,total_month:0,total_revenue:0,avg_amount:0}),d=s=>(s/100).toFixed(2),i=s=>s?new Date(s).toLocaleString("zh-CN"):"-",c=s=>({alipay:"text-blue-400",wechat:"text-green-400",stripe:"text-purple-400"})[s]||"text-gray-400",T=s=>({alipay:"支付宝",wechat:"微信支付",stripe:"Stripe"})[s]||s,V=s=>({success:"px-2 py-1 text-xs rounded-full bg-green-900 text-green-300",pending:"px-2 py-1 text-xs rounded-full bg-yellow-900 text-yellow-300",failed:"px-2 py-1 text-xs rounded-full bg-red-900 text-red-300"})[s]||"px-2 py-1 text-xs rounded-full bg-gray-900 text-gray-300",D=s=>({success:"成功",pending:"待处理",failed:"失败"})[s]||s,u=()=>_(this,null,function*(){g.value=!0;try{const s=yield U.getOrdersList({page:a.value.page,per_page:a.value.per_page,search:n.value,payment_method:r.value.payment_method,status:r.value.status});s.success&&(y.value=s.data.data,a.value=s.data.pagination)}catch(s){console.error("获取订单列表失败:",s)}finally{g.value=!1}}),w=s=>{a.value.page=s,u()},M=()=>{n.value="",r.value={payment_method:"",status:""},a.value.page=1,u()},j=s=>{console.log("查看订单详情:",s)};let h;const F=()=>{clearTimeout(h),h=setTimeout(()=>{a.value.page=1,u()},500)};return N(()=>{u()}),(s,e)=>(v(),m("div",$,[e[21]||(e[21]=t("div",{class:"mb-6"},[t("h1",{class:"text-2xl font-bold text-white"},"订单管理"),t("p",{class:"text-gray-400 mt-1"},"详细购买凭据和订单信息")],-1)),t("div",z,[t("div",A,[t("div",E,[t("div",null,[e[5]||(e[5]=t("p",{class:"text-gray-400 text-sm"},"今日订单",-1)),t("p",Q,o(p.value.total_today||0),1)]),e[6]||(e[6]=t("div",{class:"bg-blue-500 bg-opacity-20 p-3 rounded-lg"},[t("i",{class:"fas fa-shopping-cart text-blue-400 text-xl"})],-1))])]),t("div",q,[t("div",G,[t("div",null,[e[7]||(e[7]=t("p",{class:"text-gray-400 text-sm"},"本月订单",-1)),t("p",H,o(p.value.total_month||0),1)]),e[8]||(e[8]=t("div",{class:"bg-green-500 bg-opacity-20 p-3 rounded-lg"},[t("i",{class:"fas fa-chart-line text-green-400 text-xl"})],-1))])]),t("div",J,[t("div",K,[t("div",null,[e[9]||(e[9]=t("p",{class:"text-gray-400 text-sm"},"总收入",-1)),t("p",R,"¥"+o(d(p.value.total_revenue||0)),1)]),e[10]||(e[10]=t("div",{class:"bg-yellow-500 bg-opacity-20 p-3 rounded-lg"},[t("i",{class:"fas fa-coins text-yellow-400 text-xl"})],-1))])]),t("div",W,[t("div",X,[t("div",null,[e[11]||(e[11]=t("p",{class:"text-gray-400 text-sm"},"平均订单额",-1)),t("p",Y,"¥"+o(d(p.value.avg_amount||0)),1)]),e[12]||(e[12]=t("div",{class:"bg-purple-500 bg-opacity-20 p-3 rounded-lg"},[t("i",{class:"fas fa-calculator text-purple-400 text-xl"})],-1))])])]),t("div",Z,[t("div",tt,[t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-400 mb-2"},"搜索订单",-1)),b(t("input",{"onUpdate:modelValue":e[0]||(e[0]=l=>n.value=l),type:"text",placeholder:"订单ID或用户名",class:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-500",onInput:F},null,544),[[O,n.value]])]),t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-400 mb-2"},"支付方式",-1)),b(t("select",{"onUpdate:modelValue":e[1]||(e[1]=l=>r.value.payment_method=l),class:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500",onChange:u},e[14]||(e[14]=[t("option",{value:""},"全部",-1),t("option",{value:"alipay"},"支付宝",-1),t("option",{value:"wechat"},"微信支付",-1),t("option",{value:"stripe"},"Stripe",-1)]),544),[[k,r.value.payment_method]])]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-400 mb-2"},"订单状态",-1)),b(t("select",{"onUpdate:modelValue":e[2]||(e[2]=l=>r.value.status=l),class:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500",onChange:u},e[16]||(e[16]=[t("option",{value:""},"全部",-1),t("option",{value:"success"},"成功",-1),t("option",{value:"pending"},"待处理",-1),t("option",{value:"failed"},"失败",-1)]),544),[[k,r.value.status]])]),t("div",{class:"flex items-end"},[t("button",{onClick:M,class:"w-full px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors"},e[18]||(e[18]=[t("i",{class:"fas fa-refresh mr-2"},null,-1),C("重置 ",-1)]))])])]),t("div",et,[t("div",st,[t("table",at,[e[20]||(e[20]=t("thead",{class:"bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"订单ID"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"用户名"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"商品类型"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"金额"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"支付方式"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"状态"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"创建时间"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"},"操作")])],-1)),t("tbody",lt,[(v(!0),m(I,null,P(y.value,l=>(v(),m("tr",{key:l.id,class:"hover:bg-gray-700 transition-colors"},[t("td",ot,"#"+o(l.id),1),t("td",rt,o(l.username),1),t("td",nt,o(l.product_type),1),t("td",it,"¥"+o(d(l.amount)),1),t("td",dt,[t("span",{class:S(c(l.payment_method))},o(T(l.payment_method)),3)]),t("td",pt,[t("span",{class:S(V(l.status))},o(D(l.status)),3)]),t("td",ut,o(i(l.created_at)),1),t("td",xt,[t("button",{onClick:ft=>j(l.id),class:"text-yellow-400 hover:text-yellow-300 transition-colors"},e[19]||(e[19]=[t("i",{class:"fas fa-eye mr-1"},null,-1),C("详情 ",-1)]),8,ct)])]))),128))])])]),a.value.total>0?(v(),m("div",gt,[t("div",yt," 显示 "+o((a.value.page-1)*a.value.per_page+1)+" 到 "+o(Math.min(a.value.page*a.value.per_page,a.value.total))+" 条， 共 "+o(a.value.total)+" 条记录 ",1),t("div",mt,[t("button",{onClick:e[3]||(e[3]=l=>w(a.value.page-1)),disabled:!a.value.has_prev,class:"px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500 transition-colors"}," 上一页 ",8,vt),t("button",{onClick:e[4]||(e[4]=l=>w(a.value.page+1)),disabled:!a.value.has_next,class:"px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500 transition-colors"}," 下一页 ",8,bt)])])):B("",!0)])]))}});export{kt as default};
