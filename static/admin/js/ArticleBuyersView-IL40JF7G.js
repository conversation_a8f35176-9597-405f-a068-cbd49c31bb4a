var C=(b,m,c)=>new Promise((p,r)=>{var i=d=>{try{u(c.next(d))}catch(g){r(g)}},x=d=>{try{u(c.throw(d))}catch(g){r(g)}},u=d=>d.done?p(d.value):Promise.resolve(d.value).then(i,x);u((c=c.apply(b,m)).next())});import{d as F,z as L,i as _,k as P,c as o,a as t,e as v,f as z,t as l,w as A,r as S,n as y,g as T,F as j,l as E,o as n}from"./index-4iV_uVFP.js";import{a as Q}from"./articles-DE40p57e.js";import{_ as R}from"./_plugin-vue_export-helper-DlAUqK2U.js";const G={class:"article-buyers"},H={class:"mb-6 flex justify-between items-center"},J={class:"text-xl font-bold text-white"},K={key:0,class:"admin-card p-4 mb-6"},O={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},U={class:"ml-2 text-white"},W={class:"ml-2 text-white"},X={class:"admin-card p-4"},Y={key:0,id:"buyers-loading",class:"text-center py-8"},Z={key:1,id:"buyers-error",class:"text-center py-8"},$={class:"text-red-400 mb-4"},tt={id:"error-message"},et={key:2,id:"buyers-empty",class:"text-center py-8"},st={key:3,id:"buyers-list",class:"overflow-x-auto"},at={class:"table-admin w-full"},lt={id:"buyers-table-body"},ot={class:"px-3 py-2"},nt={class:"text-white font-medium"},rt={class:"text-xs text-gray-400"},it={class:"text-xs text-gray-400"},dt={class:"px-3 py-2"},ct={class:"px-3 py-2"},pt={class:"text-green-400 font-medium"},xt={class:"text-xs text-gray-400 mt-1"},ut={class:"text-xs text-gray-400"},gt={key:0,class:"text-xs text-gray-500 mt-1"},_t={class:"px-3 py-2"},yt={key:0,class:"text-yellow-400 font-medium"},mt={key:1,class:"text-gray-500"},vt={class:"px-3 py-2"},bt={key:0,class:"text-blue-400 font-medium"},ft={key:1,class:"text-gray-500"},ht={key:2,class:"text-xs text-gray-400"},wt={class:"px-3 py-2"},kt={class:"text-xs"},Mt={class:"text-xs text-gray-400 mt-2"},Ct={class:"px-3 py-2 text-gray-300 text-sm"},Tt=F({__name:"ArticleBuyersView",setup(b){const m=L(),c=parseInt(m.params.id),p=_(!1),r=_(""),i=_(null),x=_([]),u=_(0),d=s=>({published:"已发布",draft:"草稿",archived:"已归档"})[s]||s,g=s=>({published:"bg-green-900 text-green-300",draft:"bg-gray-700 text-gray-300",archived:"bg-red-900 text-red-300"})[s]||"bg-gray-700 text-gray-300",V=s=>({premium:"高级内容",basic:"基础内容",full:"完整内容"})[s]||s,I=s=>({premium:"bg-yellow-600 text-yellow-100",basic:"bg-blue-600 text-blue-100",full:"bg-green-600 text-green-100"})[s]||"bg-gray-600 text-gray-100",B=s=>({premium:"高级配额",basic:"基础配额",vip:"VIP配额",recharge:"充值配额"})[s]||s||"未知",D=s=>({0:"普通用户",1:"初级VIP",2:"VIP Pro"})[s]||"未知",q=s=>({0:"bg-gray-700 text-gray-300",1:"bg-blue-700 text-blue-300",2:"bg-purple-700 text-purple-300"})[s]||"bg-gray-700 text-gray-300",f=s=>({现金支付:"💰 现金支付",积分支付:"🔵 积分支付",基础配额:"📄 基础配额",高级配额:"⭐ 高级配额",混合支付:"🔄 混合支付",自动赠送:"🎁 自动赠送",cash:"💰 现金支付",quota:"📄 配额使用",points:"🔵 积分支付",mixed:"🔄 混合支付",grant:"🎁 自动赠送",balance:"💳 余额支付",unknown:"❓ 未知"})[s]||s,h=s=>({现金支付:"bg-green-700 text-green-300",积分支付:"bg-blue-700 text-blue-300",基础配额:"bg-yellow-700 text-yellow-300",高级配额:"bg-orange-700 text-orange-300",混合支付:"bg-purple-700 text-purple-300",自动赠送:"bg-gray-700 text-gray-300",cash:"bg-green-700 text-green-300",quota:"bg-yellow-700 text-yellow-300",points:"bg-blue-700 text-blue-300",mixed:"bg-purple-700 text-purple-300",grant:"bg-gray-700 text-gray-300",balance:"bg-teal-700 text-teal-300",unknown:"bg-gray-700 text-gray-300"})[s]||"bg-gray-700 text-gray-300",w=s=>s?new Date(s).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"未知",k=()=>C(this,null,function*(){p.value=!0,r.value="";try{const s=yield Q.getBuyers(c);s.success?(i.value=s.article,x.value=s.buyers||[],u.value=s.total_count||0):r.value=s.error||"加载购买者数据失败"}catch(s){console.error("加载购买者失败:",s),r.value="网络错误，请稍后重试"}finally{p.value=!1}});return P(()=>{c?k():r.value="无效的文章ID"}),(s,e)=>{var M;const N=S("router-link");return n(),o("div",G,[t("div",H,[t("h1",J,l(((M=i.value)==null?void 0:M.title)||"文章购买者列表"),1),z(N,{to:"/articles",class:"admin-btn-secondary px-4 py-2 rounded-lg"},{default:A(()=>e[0]||(e[0]=[t("i",{class:"fas fa-arrow-left mr-2"},null,-1),T("返回文章列表 ",-1)])),_:1,__:[0]})]),i.value?(n(),o("div",K,[t("div",O,[t("div",null,[e[1]||(e[1]=t("span",{class:"text-gray-400"},"ID:",-1)),t("span",U,l(i.value.id),1)]),t("div",null,[e[2]||(e[2]=t("span",{class:"text-gray-400"},"状态:",-1)),t("span",{class:y(["ml-2 px-2 py-1 rounded-full text-xs",g(i.value.status)])},l(d(i.value.status)),3)]),t("div",null,[e[3]||(e[3]=t("span",{class:"text-gray-400"},"创建时间:",-1)),t("span",W,l(w(i.value.created_at)),1)])])])):v("",!0),t("div",X,[e[11]||(e[11]=t("h2",{class:"text-lg font-bold text-white mb-4"},"购买者列表",-1)),p.value?(n(),o("div",Y,e[4]||(e[4]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500"},null,-1),t("p",{class:"mt-2 text-gray-400"},"正在加载购买者数据...",-1)]))):r.value?(n(),o("div",Z,[t("div",$,[e[5]||(e[5]=t("i",{class:"fas fa-exclamation-triangle text-2xl mb-2"},null,-1)),t("p",tt,l(r.value),1)]),t("button",{onClick:k,class:"admin-btn-primary px-4 py-2 rounded-lg"},e[6]||(e[6]=[t("i",{class:"fas fa-redo mr-2"},null,-1),T("重新加载 ",-1)]))])):x.value.length===0?(n(),o("div",et,e[7]||(e[7]=[t("div",{class:"text-gray-400 mb-4"},[t("i",{class:"fas fa-users text-4xl mb-4"}),t("p",{class:"text-lg"},"暂无购买者"),t("p",{class:"text-sm"},"该文章还没有用户购买")],-1)]))):(n(),o("div",st,[t("table",at,[e[10]||(e[10]=t("thead",null,[t("tr",null,[t("th",{class:"px-3 py-2 text-left"},"用户信息"),t("th",{class:"px-3 py-2 text-left"},"内容类型"),t("th",{class:"px-3 py-2 text-left"},"支付信息"),t("th",{class:"px-3 py-2 text-left"},"配额消耗"),t("th",{class:"px-3 py-2 text-left"},"积分消耗"),t("th",{class:"px-3 py-2 text-left"},"用户状态"),t("th",{class:"px-3 py-2 text-left"},"购买时间")])],-1)),t("tbody",lt,[(n(!0),o(j,null,E(x.value,a=>(n(),o("tr",{key:a.user_id,class:"hover:bg-gray-800 transition-colors"},[t("td",ot,[t("div",nt,l(a.username),1),t("div",rt,"ID: "+l(a.user_id),1),t("div",it,l(a.email),1)]),t("td",dt,[t("span",{class:y(["px-2 py-1 rounded text-xs font-medium",I(a.content_type)])},l(V(a.content_type)),3)]),t("td",ct,[t("div",pt,[t("span",{class:y(["px-2 py-1 rounded text-xs",h(a.cost_type)])},l(f(a.cost_type)),3)]),t("div",xt," 总价值: ¥"+l(a.cost_amount.toFixed(2)),1),t("div",ut," 实付: ¥"+l(a.amount_paid.toFixed(2)),1),a.transaction_id?(n(),o("div",gt," 单号: "+l(a.transaction_id),1)):v("",!0)]),t("td",_t,[a.quota_used>0?(n(),o("div",yt," -"+l(a.quota_used)+" "+l(B(a.quota_type)),1)):(n(),o("div",mt,"无配额消耗"))]),t("td",vt,[a.points_used>0?(n(),o("div",bt," -"+l(a.points_used)+" 积分 ",1)):(n(),o("div",ft,"无积分消耗")),a.points_balance_before!==null?(n(),o("div",ht," 余额: "+l(a.points_balance_before)+" → "+l(a.points_balance_after),1)):v("",!0)]),t("td",wt,[t("div",kt,[t("span",{class:y(["px-2 py-1 rounded text-xs",q(a.vip_level_at_purchase)])},l(D(a.vip_level_at_purchase)),3),e[8]||(e[8]=t("div",{class:"text-xs text-gray-400 mt-1"}," 购买时等级 ",-1))]),t("div",Mt,[e[9]||(e[9]=t("div",{class:"font-medium text-white"},"支付构成:",-1)),t("span",{class:y(["px-2 py-1 rounded text-xs",h(a.cost_type)])},l(f(a.cost_type)),3)])]),t("td",Ct,l(w(a.purchase_time)),1)]))),128))])])]))])])}}}),qt=R(Tt,[["__scopeId","data-v-0c80ec18"]]);export{qt as default};
