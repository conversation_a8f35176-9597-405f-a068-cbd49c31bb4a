var T=(n,p,g)=>new Promise((h,m)=>{var w=l=>{try{u(g.next(l))}catch(o){m(o)}},y=l=>{try{u(g.throw(l))}catch(o){m(o)}},u=l=>l.done?h(l.value):Promise.resolve(l.value).then(w,y);u((g=g.apply(n,p)).next())});import{h as d,d as O,i as P,m as C,k as R,c,a as t,g as _,t as r,p as v,v as M,s as z,b as I,e as U,F as $,l as N,E as b,n as V,o as x}from"./index-4iV_uVFP.js";const B={getTransactions(n){return d.get("/points/transactions",{params:n})},getUserTransactions(n,p){return d.get(`/points/users/${n}/transactions`,{params:p})},adjustPoints(n){return d.post("/points/adjust",n)},batchAdjustPoints(n){return d.post("/points/batch-adjust",n)},getStats(){return d.get("/points/stats")},getUserBalance(n){return d.get(`/points/users/${n}/balance`)},getLeaderboard(n=10){return d.get("/points/leaderboard",{params:{limit:n}})},exportTransactions(n){return d.get("/points/transactions/export",{params:n,responseType:"blob"})},getRules(){return d.get("/points/rules")},updateRules(n){return d.put("/points/rules",n)},getTrend(n="7d"){return d.get("/points/trend",{params:{period:n}})},reverseTransaction(n,p){return d.post(`/points/transactions/${n}/reverse`,{reason:p})}},q={class:"points-management"},G={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},H={class:"admin-card p-4 bg-gradient-to-br from-blue-900 to-indigo-900 border border-blue-700"},J={class:"flex items-center justify-between"},K={class:"text-2xl font-bold text-white"},Q={class:"admin-card p-4 bg-gradient-to-br from-green-900 to-emerald-900 border border-green-700"},W={class:"flex items-center justify-between"},X={class:"text-2xl font-bold text-white"},Y={class:"admin-card p-4 bg-gradient-to-br from-yellow-900 to-amber-900 border border-yellow-700"},Z={class:"flex items-center justify-between"},tt={class:"text-2xl font-bold text-white"},et={class:"admin-card p-4 bg-gradient-to-br from-purple-900 to-indigo-900 border border-purple-700"},st={class:"flex items-center justify-between"},ot={class:"text-2xl font-bold text-white"},at={class:"admin-card p-4 mb-6"},nt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},rt={class:"admin-card p-4 mb-6"},lt={class:"overflow-x-auto"},dt={class:"table-admin w-full"},it={id:"transactions-table-body"},pt={class:"px-4 py-3 border-t border-gray-700"},ut={class:"px-4 py-3 border-t border-gray-700"},ct={class:"font-medium text-white"},xt={class:"text-sm text-gray-400"},gt={class:"px-4 py-3 border-t border-gray-700"},ft={class:"text-xs text-gray-400"},bt={class:"px-4 py-3 border-t border-gray-700"},mt={class:"px-4 py-3 border-t border-gray-700 max-w-xs truncate"},yt={class:"px-4 py-3 border-t border-gray-700 text-sm text-gray-400"},_t={key:0},vt={key:0,class:"pagination mt-4"},ht=["disabled"],wt={class:"page-numbers"},jt=["onClick"],kt=["disabled"],Mt=O({__name:"PointListView",setup(n){const p=a=>new Date(a).toLocaleDateString(),g=a=>({earn:"px-2 py-1 rounded-full text-xs bg-green-900 text-green-300",spend:"px-2 py-1 rounded-full text-xs bg-red-900 text-red-300",admin_adjust:"px-2 py-1 rounded-full text-xs bg-blue-900 text-blue-300",referral:"px-2 py-1 rounded-full text-xs bg-purple-900 text-purple-300",daily_bonus:"px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300"})[a]||"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300",h=a=>({earn:"获得积分",spend:"消费积分",admin_adjust:"管理员调整",referral:"推荐奖励",daily_bonus:"每日奖励"})[a]||a,m=()=>{o.page=1,f()},w=()=>{o.user="",o.type="",o.date_from="",o.date_to="",o.page=1,f()},y=P(!1),u=P([]),l=C({total_points_issued:0,total_points_consumed:0,today_transactions:0,active_users:0,current_balance:0,avg_daily_points:0}),o=C({user:"",type:"",date_from:"",date_to:"",page:1,per_page:20}),i=C({page:1,pages:1,total:0,per_page:20,has_prev:!1,has_next:!1,prev_num:null,next_num:null}),D=()=>T(this,null,function*(){try{const a=yield B.getStats();a.success?Object.assign(l,a.data):b.error(a.message||"加载统计数据失败")}catch(a){console.error("加载积分统计失败:",a),b.error("加载统计数据失败")}}),f=()=>T(this,null,function*(){y.value=!0;try{const a={page:o.page,per_page:o.per_page,user:o.user||void 0,transaction_type:o.type||void 0,date_from:o.date_from||void 0,date_to:o.date_to||void 0},e=yield B.getTransactions(a);e.success?(u.value=e.data.items||[],Object.assign(i,{page:e.data.current_page||1,pages:e.data.pages||1,total:e.data.total||0,per_page:e.data.per_page||20,has_prev:e.data.has_prev||!1,has_next:e.data.has_next||!1,prev_num:e.data.prev_num||null,next_num:e.data.next_num||null})):b.error(e.message||"加载交易记录失败")}catch(a){console.error("加载积分交易失败:",a),b.error("加载交易记录失败")}finally{y.value=!1}}),F=()=>{D(),f()},L=()=>{b.info("积分调整功能开发中...")},A=()=>{const a=[],e=i.page,s=i.pages,S=Math.max(1,e-2),E=Math.min(s,e+2);for(let k=S;k<=E;k++)a.push(k);return a},j=a=>{a&&a!==i.page&&(o.page=a,i.page=a,f())};return R(()=>{console.log("积分管理页面已加载"),D(),f()}),(a,e)=>(x(),c("div",q,[t("div",{class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},[e[8]||(e[8]=t("h1",{class:"text-2xl font-bold text-white"},"积分管理",-1)),t("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[t("button",{onClick:L,class:"admin-btn-primary px-4 py-2 rounded-lg text-center"},e[6]||(e[6]=[t("i",{class:"fas fa-plus-minus mr-2"},null,-1),_("调整积分 ",-1)])),t("button",{onClick:F,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},e[7]||(e[7]=[t("i",{class:"fas fa-sync-alt mr-2"},null,-1),_("刷新列表 ",-1)]))])]),t("div",G,[t("div",H,[t("div",J,[t("div",null,[e[9]||(e[9]=t("h2",{class:"text-gray-300 mb-1"},"总积分发放",-1)),t("p",K,r(l.total_points_issued||0),1)]),e[10]||(e[10]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-blue-800 text-blue-200"},[t("i",{class:"fas fa-coins text-xl"})],-1))])]),t("div",Q,[t("div",W,[t("div",null,[e[11]||(e[11]=t("h2",{class:"text-gray-300 mb-1"},"总积分消费",-1)),t("p",X,r(l.total_points_consumed||0),1)]),e[12]||(e[12]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-green-800 text-green-200"},[t("i",{class:"fas fa-shopping-cart text-xl"})],-1))])]),t("div",Y,[t("div",Z,[t("div",null,[e[13]||(e[13]=t("h2",{class:"text-gray-300 mb-1"},"有积分用户",-1)),t("p",tt,r(l.active_users||0),1)]),e[14]||(e[14]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-yellow-800 text-yellow-200"},[t("i",{class:"fas fa-users text-xl"})],-1))])]),t("div",et,[t("div",st,[t("div",null,[e[15]||(e[15]=t("h2",{class:"text-gray-300 mb-1"},"今日交易",-1)),t("p",ot,r(l.today_transactions||0),1)]),e[16]||(e[16]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-purple-800 text-purple-200"},[t("i",{class:"fas fa-calendar-day text-xl"})],-1))])])]),t("div",at,[t("div",nt,[t("div",null,[e[17]||(e[17]=t("label",{for:"filter-user",class:"block mb-2 text-gray-300"},"用户搜索",-1)),v(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>o.user=s),type:"text",id:"filter-user",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"用户名或邮箱"},null,512),[[M,o.user]])]),t("div",null,[e[19]||(e[19]=t("label",{for:"filter-type",class:"block mb-2 text-gray-300"},"交易类型",-1)),v(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>o.type=s),id:"filter-type",class:"form-input w-full px-3 py-2 rounded-lg"},e[18]||(e[18]=[I('<option value="">全部类型</option><option value="earn">获得积分</option><option value="spend">消费积分</option><option value="admin_adjust">管理员调整</option><option value="referral">推荐奖励</option><option value="daily_bonus">每日奖励</option>',6)]),512),[[z,o.type]])]),t("div",null,[e[20]||(e[20]=t("label",{for:"filter-date-from",class:"block mb-2 text-gray-300"},"开始日期",-1)),v(t("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>o.date_from=s),type:"date",id:"filter-date-from",class:"form-input w-full px-3 py-2 rounded-lg"},null,512),[[M,o.date_from]])]),t("div",null,[e[21]||(e[21]=t("label",{for:"filter-date-to",class:"block mb-2 text-gray-300"},"结束日期",-1)),v(t("input",{"onUpdate:modelValue":e[3]||(e[3]=s=>o.date_to=s),type:"date",id:"filter-date-to",class:"form-input w-full px-3 py-2 rounded-lg"},null,512),[[M,o.date_to]])])]),t("div",{class:"mt-4 flex space-x-3"},[t("button",{onClick:m,id:"apply-filter-btn",class:"admin-btn-primary px-4 py-2 rounded-lg"},e[22]||(e[22]=[t("i",{class:"fas fa-search mr-2"},null,-1),_("筛选 ",-1)])),t("button",{onClick:w,id:"clear-filter-btn",class:"admin-btn-secondary px-4 py-2 rounded-lg"},e[23]||(e[23]=[t("i",{class:"fas fa-times mr-2"},null,-1),_("清除 ",-1)]))])]),t("div",rt,[t("div",lt,[t("table",dt,[e[25]||(e[25]=t("thead",null,[t("tr",null,[t("th",{class:"px-4 py-2 text-left"},"ID"),t("th",{class:"px-4 py-2 text-left"},"用户"),t("th",{class:"px-4 py-2 text-left"},"积分变动"),t("th",{class:"px-4 py-2 text-left"},"交易类型"),t("th",{class:"px-4 py-2 text-left"},"描述"),t("th",{class:"px-4 py-2 text-left"},"时间")])],-1)),t("tbody",it,[(x(!0),c($,null,N(u.value,s=>(x(),c("tr",{key:s.id,class:"hover:bg-gray-700/50 transition-colors"},[t("td",pt,r(s.id),1),t("td",ut,[t("div",null,[t("div",ct,r(s.username),1),t("div",xt,r(s.email),1)])]),t("td",gt,[t("span",{class:V([s.points_change>0?"text-green-400":"text-red-400","font-mono font-bold"])},r(s.points_change>0?"+":"")+r(s.points_change),3),t("div",ft," 余额: "+r(s.balance_before)+" → "+r(s.balance_after),1)]),t("td",bt,[t("span",{class:V(g(s.source_type))},r(h(s.source_type)),3)]),t("td",mt,r(s.description),1),t("td",yt,r(p(s.created_at)),1)]))),128)),u.value.length===0?(x(),c("tr",_t,e[24]||(e[24]=[t("td",{colspan:"6",class:"px-4 py-8 text-center text-gray-400"},[t("i",{class:"fas fa-coins text-4xl mb-4"}),t("p",null,"暂无积分交易记录")],-1)]))):U("",!0)])])]),i.pages>1?(x(),c("div",vt,[t("button",{class:"btn-secondary",disabled:!i.has_prev,onClick:e[4]||(e[4]=s=>j(i.prev_num))}," 上一页 ",8,ht),t("div",wt,[(x(!0),c($,null,N(A(),s=>(x(),c("button",{key:s,class:V(["page-btn",s===i.page?"active":""]),onClick:S=>j(s)},r(s),11,jt))),128))]),t("button",{class:"btn-secondary",disabled:!i.has_next,onClick:e[5]||(e[5]=s=>j(i.next_num))}," 下一页 ",8,kt)])):U("",!0)])]))}});export{Mt as default};
