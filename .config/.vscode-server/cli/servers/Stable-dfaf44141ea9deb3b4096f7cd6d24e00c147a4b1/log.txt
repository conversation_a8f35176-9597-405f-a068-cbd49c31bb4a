*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[01:05:17] 




[01:05:17] Extension host agent started.
[01:05:18] [<unknown>][8b18d678][ManagementConnection] New connection established.
[01:05:18] [<unknown>][7529817e][ExtensionHostConnection] New connection established.
[01:05:18] [<unknown>][7529817e][ExtensionHostConnection] <1295> Launched Extension Host Process.
[01:05:18] ComputeTargetPlatform: linux-x64
[01:05:30] [File Watcher ('parcel')] Unexpected error: inotify_add_watch on '/home/<USER>/workspace/.config/.vscode-server/data/logs/20250805T130020' failed: No such file or directory (EUNKNOWN) (path: /home/<USER>/workspace)
[01:05:30] [File Watcher (universal)] inotify_add_watch on '/home/<USER>/workspace/.config/.vscode-server/data/logs/20250805T130020' failed: No such file or directory
[01:05:39] ComputeTargetPlatform: linux-x64
[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr> 
<--- Last few GCs --->

[1295:0x7b38000]    47222 ms: Mark-Compact 1732.4 (2095.7) -> 1731.9 (2095.9) MB, pooled: 8 MB, 183.49 / 0.01 ms  (average mu = 0.213, current mu = 0.018) allocation failure; scavenge might not succeed
[1295:0x7b38000]    47428 ms: Mark-Compact 1732.3 (2096.1) -> 1732.0 (2096.1) MB, pooled: 9 MB, 204.73 / 0.22 ms  (average mu = 0.112, current mu = 0.007) allocation failure; scavenge might not succeed


<--- JS stacktrace --->

FATAL ERROR: Reached heap limit Allocation failed - JavaScript heap out of memory
----- Native stack trace -----


[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr>  1: 0xe09a56 node::OOMErrorHandler(char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr>  2: 0x11ba160 v8::Utils::ReportOOMFailure(v8::internal::Isolate*, char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]
 3: 0x11ba437 v8::internal::V8::FatalProcessOutOfMemory(v8::internal::Isolate*, char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]
 4: 0x13e7e65  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]
 5: 0x14016f9 v8::internal::Heap::CollectGarbage(v8::internal::AllocationSpace, v8::internal::GarbageCollectionReason, v8::GCCallbackFlags) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]
 6: 0x13d5da8 v8::internal::HeapAllocator::AllocateRawWithLightRetrySlowPath(int, v8::internal::AllocationType, v8::internal::AllocationOrigin, v8::internal::AllocationAlignment) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr>  7: 0x13d6cd5 v8::internal::HeapAllocator::AllocateRawWithRetryOrFailSlowPath(int, v8::internal::AllocationType, v8::internal::AllocationOrigin, v8::internal::AllocationAlignment) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr>  8: 0x13af0ce v8::internal::Factory::AllocateRaw(int, v8::internal::AllocationType, v8::internal::AllocationAlignment) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr>  9: 0x139e1c4 v8::internal::FactoryBase<v8::internal::Factory>::AllocateRawWithImmortalMap(int, v8::internal::AllocationType, v8::internal::Tagged<v8::internal::Map>, v8::internal::AllocationAlignment) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr> 10: 0x13a00cf v8::internal::FactoryBase<v8::internal::Factory>::NewRawTwoByteString(int, v8::internal::AllocationType) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr> 11: 0x13baaaa v8::internal::Factory::NewStringFromUtf8(v8::base::Vector<char const>, v8::internal::AllocationType) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr> 12: 0x11d667d v8::String::NewFromUtf8(v8::Isolate*, char const*, v8::NewStringType, int) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr> 13: 0x10c6c49  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr> 14: 0xf28790  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:05] [<unknown>][7529817e][ExtensionHostConnection] <1295><stderr> 15: 0x7ff211dcf745 

[01:06:19] [<unknown>][7529817e][ExtensionHostConnection] <1295> Extension Host Process exited with code: null, signal: SIGABRT.
Cancelling previous shutdown timeout
[01:06:19] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[01:06:19] Last EH closed, waiting before shutting down
[01:06:25] [<unknown>][7529817e][ExtensionHostConnection] Unknown reconnection token (seen before).
[01:06:27] [<unknown>][84942aab][ExtensionHostConnection] New connection established.
[01:06:27] [<unknown>][84942aab][ExtensionHostConnection] <1619> Launched Extension Host Process.
[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr> 
<--- Last few GCs --->

[1619:0x744f000]    29992 ms: Mark-Compact (reduce) 2045.4 (2082.5) -> 2044.5 (2078.6) MB, pooled: 0 MB, 130.03 / 0.00 ms  (+ 3.2 ms in 0 steps since start of marking, biggest step 0.0 ms, walltime since start of marking 138 ms) (average mu = 0.179, curre[1619:0x744f000]    30068 ms: Mark-Compact 2044.9 (2079.0) -> 2044.9 (2082.0) MB, pooled: 0 MB, 75.59 / 0.01 ms  (average mu = 0.115, current mu = 0.007) allocation failure; scavenge might not succeed


<--- JS stacktrace --->

FATAL ERROR: Reached heap limit Allocation failed - JavaScript heap out of memory
----- Native stack trace -----


[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr>  1: 0xe09a56 node::OOMErrorHandler(char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr>  2: 0x11ba160 v8::Utils::ReportOOMFailure(v8::internal::Isolate*, char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr>  3: 0x11ba437 v8::internal::V8::FatalProcessOutOfMemory(v8::internal::Isolate*, char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr>  4: 0x13e7e65  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr>  5: 0x14016f9 v8::internal::Heap::CollectGarbage(v8::internal::AllocationSpace, v8::internal::GarbageCollectionReason, v8::GCCallbackFlags) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr>  6: 0x13d5da8 v8::internal::HeapAllocator::AllocateRawWithLightRetrySlowPath(int, v8::internal::AllocationType, v8::internal::AllocationOrigin, v8::internal::AllocationAlignment) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr>  7: 0x13d6cd5 v8::internal::HeapAllocator::AllocateRawWithRetryOrFailSlowPath(int, v8::internal::AllocationType, v8::internal::AllocationOrigin, v8::internal::AllocationAlignment) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr>  8: 0x13af0ce v8::internal::Factory::AllocateRaw(int, v8::internal::AllocationType, v8::internal::AllocationAlignment) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr>  9: 0x139e1c4 v8::internal::FactoryBase<v8::internal::Factory>::AllocateRawWithImmortalMap(int, v8::internal::AllocationType, v8::internal::Tagged<v8::internal::Map>, v8::internal::AllocationAlignment) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr> 10: 0x13a00cf v8::internal::FactoryBase<v8::internal::Factory>::NewRawTwoByteString(int, v8::internal::AllocationType) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr> 11: 0x1516b97 v8::internal::JsonParser<unsigned short>::MakeString(v8::internal::JsonString const&, v8::internal::Handle<v8::internal::String>) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr> 12: 0x151d52e  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr> 13: 0x151f9dd v8::internal::JsonParser<unsigned short>::ParseJson(v8::internal::Handle<v8::internal::Object>) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[01:06:57] [<unknown>][84942aab][ExtensionHostConnection] <1619><stderr> 14: 0x12372cc v8::internal::Builtin_JsonParse(int, unsigned long*, v8::internal::Isolate*) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]
15: 0x7fc333eac3b6 

[01:07:08] [<unknown>][84942aab][ExtensionHostConnection] <1619> Extension Host Process exited with code: null, signal: SIGABRT.
Cancelling previous shutdown timeout
[01:07:08] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[01:07:08] Last EH closed, waiting before shutting down
[01:07:17] [<unknown>][84942aab][ExtensionHostConnection] Unknown reconnection token (seen before).
[01:07:18] [<unknown>][7357b113][ExtensionHostConnection] New connection established.
[01:07:18] [<unknown>][7357b113][ExtensionHostConnection] <1857> Launched Extension Host Process.
New EH opened, aborting shutdown
[01:12:08] New EH opened, aborting shutdown
[13:05:43] Getting Manifest... rooveterinaryinc.roo-cline
[13:05:43] Installing extension: rooveterinaryinc.roo-cline {
  productVersion: { version: '1.101.0', date: '2025-06-11T15:00:50.123Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[13:05:47] Extension signature verification result for rooveterinaryinc.roo-cline: Success. Internal Code: 0. Executed: true. Duration: 2645ms.
[13:05:54] Extracted extension to file:///home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8: rooveterinaryinc.roo-cline
[13:05:54] Renamed to /home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8
[13:05:55] Marked extension as removed rooveterinaryinc.roo-cline-3.25.7
[13:05:55] Extension installed successfully: rooveterinaryinc.roo-cline file:///home/<USER>/.vscode-server/extensions/extensions.json
[21:27:48] [<unknown>][7357b113][ExtensionHostConnection] <1857><stderr> 
<--- Last few GCs --->

[1857:0x6e75000] 73229642 ms: Mark-Compact 1953.0 (2091.6) -> 1951.1 (2089.8) MB, pooled: 9 MB, 140.05 / 0.01 ms  (average mu = 0.111, current mu = 0.043) allocation failure; scavenge might not succeed
[1857:0x6e75000] 73229782 ms: Mark-Compact 1954.8 (2093.3) -> 1952.8 (2091.5) MB, pooled: 9 MB, 134.72 / 0.01 ms  (average mu = 0.079, current mu = 0.041) allocation failure; scavenge might not succeed


<--- JS stacktrace --->

FATAL ERROR: Ineffective mark-compacts near heap limit Allocation failed - JavaScript heap out of memory
----- Native stack trace -----


[21:27:48] [<unknown>][7357b113][ExtensionHostConnection] <1857><stderr>  1: 0xe09a56 node::OOMErrorHandler(char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]
 2: 0x11ba160 v8::Utils::ReportOOMFailure(v8::internal::Isolate*, char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]
 3: 0x11ba437 v8::internal::V8::FatalProcessOutOfMemory(v8::internal::Isolate*, char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]
 4: 0x13e7e65  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]
 5: 0x13e7e93  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]
 6: 0x1400f6a  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:27:48] [<unknown>][7357b113][ExtensionHostConnection] <1857><stderr>  7: 0x1404138  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:27:48] [<unknown>][7357b113][ExtensionHostConnection] <1857><stderr>  8: 0x1c69e01  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:27:57] [<unknown>][7357b113][ExtensionHostConnection] <1857> Extension Host Process exited with code: null, signal: SIGABRT.
Last EH closed, waiting before shutting down
[21:27:57] Last EH closed, waiting before shutting down
[21:28:08] [<unknown>][7357b113][ExtensionHostConnection] Unknown reconnection token (seen before).
[21:28:09] [<unknown>][dc6bfdec][ExtensionHostConnection] New connection established.
[21:28:09] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150> Launched Extension Host Process.
[21:28:43] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150><stderr> 
<--- Last few GCs --->

[18150:0x863d000]    33769 ms: Mark-Compact 1801.7 (2091.7) -> 1798.8 (2089.0) MB, pooled: 9 MB, 158.75 / 0.01 ms  (average mu = 0.114, current mu = 0.055) allocation failure; scavenge might not succeed
[18150:0x863d000]    33940 ms: Mark-Compact 1802.4 (2092.5) -> 1800.6 (2090.7) MB, pooled: 9 MB, 165.23 / 0.01 ms  (average mu = 0.072, current mu = 0.031) allocation failure; scavenge might not succeed


<--- JS stacktrace --->

FATAL ERROR: Ineffective mark-compacts near heap limit Allocation failed - JavaScript heap out of memory
----- Native stack trace -----


[21:28:43] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150><stderr>  1: 0xe09a56 node::OOMErrorHandler(char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:28:43] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150><stderr>  2: 0x11ba160 v8::Utils::ReportOOMFailure(v8::internal::Isolate*, char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:28:43] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150><stderr>  3: 0x11ba437 v8::internal::V8::FatalProcessOutOfMemory(v8::internal::Isolate*, char const*, v8::OOMDetails const&) [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:28:43] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150><stderr>  4: 0x13e7e65  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:28:43] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150><stderr>  5: 0x13e7e93  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:28:43] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150><stderr>  6: 0x1400f6a  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:28:43] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150><stderr>  7: 0x1404138  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:28:43] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150><stderr>  8: 0x1c69e01  [/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node]

[21:28:51] [<unknown>][dc6bfdec][ExtensionHostConnection] <18150> Extension Host Process exited with code: null, signal: SIGABRT.
Cancelling previous shutdown timeout
[21:28:51] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[21:28:51] Last EH closed, waiting before shutting down
[21:29:03] [<unknown>][dc6bfdec][ExtensionHostConnection] Unknown reconnection token (seen before).
[21:29:04] [<unknown>][d689e580][ExtensionHostConnection] New connection established.
[21:29:04] [<unknown>][d689e580][ExtensionHostConnection] <18390> Launched Extension Host Process.
