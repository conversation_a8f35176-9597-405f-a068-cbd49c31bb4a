{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/users/UserListView.vue"}, "originalCode": "<template>\n  <div class=\"user-management\">\n    <!-- 页面头部区域 - 完全复刻原版Flask -->\n    <div class=\"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0\">\n      <h1 class=\"text-2xl font-bold text-white\">用户管理</h1>\n      <div class=\"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3\">\n        <button @click=\"refreshList\" class=\"admin-btn-secondary px-4 py-2 rounded-lg text-center\">\n          <i class=\"fas fa-sync-alt mr-2\"></i>刷新列表\n        </button>\n        <button @click=\"handleExport\" class=\"admin-btn-primary px-4 py-2 rounded-lg text-center\">\n          <i class=\"fas fa-download mr-2\"></i>导出数据\n        </button>\n      </div>\n    </div>\n\n    <!-- 用户统计卡片区域 - 完全复刻原版Flask -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <div class=\"admin-card p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"p-3 rounded-full bg-blue-500 bg-opacity-20\">\n            <i class=\"fas fa-users text-blue-400 text-xl\"></i>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-gray-400 text-sm\">总用户数</p>\n            <p class=\"text-2xl font-bold text-white\">{{ statistics.total_users || '-' }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"admin-card p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"p-3 rounded-full bg-yellow-500 bg-opacity-20\">\n            <i class=\"fas fa-crown text-yellow-400 text-xl\"></i>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-gray-400 text-sm\">VIP用户</p>\n            <p class=\"text-2xl font-bold text-white\">{{ statistics.vip_users || '-' }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"admin-card p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"p-3 rounded-full bg-green-500 bg-opacity-20\">\n            <i class=\"fas fa-check-circle text-green-400 text-xl\"></i>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-gray-400 text-sm\">已验证邮箱</p>\n            <p class=\"text-2xl font-bold text-white\">{{ statistics.verified_users || '-' }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"admin-card p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"p-3 rounded-full bg-purple-500 bg-opacity-20\">\n            <i class=\"fas fa-calendar-day text-purple-400 text-xl\"></i>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-gray-400 text-sm\">今日新增</p>\n            <p class=\"text-2xl font-bold text-white\">{{ statistics.today_users || '-' }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 过滤和搜索 - 完全复刻原版Flask -->\n    <div class=\"admin-card p-4 mb-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <!-- 搜索框 -->\n        <div>\n          <label for=\"search\" class=\"block mb-2 text-gray-300\">搜索用户</label>\n          <input\n            v-model=\"filters.search\"\n            type=\"text\"\n            id=\"search\"\n            class=\"form-input w-full px-3 py-2 rounded-lg\"\n            placeholder=\"用户名或邮箱...\"\n            @keyup.enter=\"searchUsers\"\n          />\n        </div>\n\n        <!-- 角色过滤 -->\n        <div>\n          <label for=\"role-filter\" class=\"block mb-2 text-gray-300\">角色过滤</label>\n          <select v-model=\"filters.role\" id=\"role-filter\" class=\"form-input w-full px-3 py-2 rounded-lg\">\n            <option value=\"\">全部角色</option>\n            <option value=\"user\">普通用户</option>\n            <option value=\"admin\">管理员</option>\n            <option value=\"editor\">编辑</option>\n          </select>\n        </div>\n\n        <!-- VIP状态过滤 -->\n        <div>\n          <label for=\"vip-filter\" class=\"block mb-2 text-gray-300\">VIP状态</label>\n          <select v-model=\"filters.vip_level\" id=\"vip-filter\" class=\"form-input w-full px-3 py-2 rounded-lg\">\n            <option value=\"\">全部状态</option>\n            <option value=\"0\">普通用户</option>\n            <option value=\"1\">VIP</option>\n            <option value=\"2\">VIP Pro</option>\n          </select>\n        </div>\n\n        <!-- 搜索按钮 -->\n        <div class=\"flex items-end\">\n          <button @click=\"searchUsers\" id=\"search-btn\" class=\"admin-btn-primary px-4 py-2 rounded-lg w-full\">\n            <i class=\"fas fa-search mr-2\"></i>搜索\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 用户表格 - 完全复刻原版Flask -->\n    <div class=\"admin-card p-4 mb-6 overflow-x-auto\">\n      <div v-if=\"loading\" id=\"loading\" class=\"text-center py-8\">\n        <i class=\"fas fa-spinner fa-spin text-2xl text-gray-400\"></i>\n        <p class=\"text-gray-400 mt-2\">加载中...</p>\n      </div>\n\n      <div v-else-if=\"users.length > 0\" id=\"users-table\">\n        <table class=\"table-admin w-full\">\n          <thead>\n            <tr>\n              <th class=\"px-4 py-2 text-left\">ID</th>\n              <th class=\"px-4 py-2 text-left\">用户信息</th>\n              <th class=\"px-4 py-2 text-left\">角色</th>\n              <th class=\"px-4 py-2 text-left\">VIP状态</th>\n              <th class=\"px-4 py-2 text-left\">配额使用</th>\n              <th class=\"px-4 py-2 text-left\">余额</th>\n              <th class=\"px-4 py-2 text-left\">注册时间</th>\n              <th class=\"px-4 py-2 text-left\">操作</th>\n            </tr>\n          </thead>\n          <tbody id=\"users-tbody\">\n            <tr\n              v-for=\"user in users\"\n              :key=\"user.id\"\n              class=\"hover:bg-gray-700/50 transition-colors\"\n            >\n              <!-- ID -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">{{ user.id }}</td>\n\n              <!-- 用户信息 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <div class=\"user-info\">\n                  <div class=\"user-name text-white font-medium\">{{ user.username }}</div>\n                  <div class=\"user-email text-gray-400 text-sm\">{{ user.email || '无邮箱' }}</div>\n                  <div :class=\"['email-status text-xs', user.email_verified ? 'text-green-400' : 'text-red-400']\">\n                    {{ user.email_verified ? '✓ 已验证' : '✗ 未验证' }}\n                  </div>\n                </div>\n              </td>\n\n              <!-- 角色 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <span :class=\"['px-2 py-1 rounded-full text-xs', user.role === 'admin' ? 'bg-red-900 text-red-300' : 'bg-gray-700 text-gray-300']\">\n                  {{ user.role === 'admin' ? '管理员' : '普通用户' }}\n                </span>\n              </td>\n\n              <!-- VIP状态 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <div class=\"vip-info\">\n                  <span :class=\"getVipBadgeClass(user.finance?.vip_level, user.finance?.vip_expire_at)\">\n                    {{ getVipStatusText(user.finance?.vip_level, user.finance?.vip_expire_at) }}\n                  </span>\n                  <div v-if=\"user.finance?.vip_expire_at && user.finance?.vip_level > 0\" class=\"vip-expire text-xs text-gray-400 mt-1\">\n                    到期: {{ formatDate(user.finance.vip_expire_at) }}\n                  </div>\n                </div>\n              </td>\n\n              <!-- 配额使用 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <div class=\"quota-info text-sm\">\n                  <!-- 管理员显示无限制 -->\n                  <div v-if=\"user.role === 'admin' || user.role === 'superadmin'\">\n                    <div class=\"text-yellow-400\">基础: 无限制</div>\n                    <div class=\"text-purple-400\">高级: 无限制</div>\n                  </div>\n                  <!-- 普通用户显示实际配额 -->\n                  <div v-else>\n                    <div>\n                      基础: {{ user.finance?.basic_quota_used || 0 }}/{{ user.finance?.basic_quota_total || 0 }}\n                      <span class=\"text-gray-400\">(剩余: {{ (user.finance?.basic_quota_total || 0) - (user.finance?.basic_quota_used || 0) }})</span>\n                    </div>\n                    <!-- 显示高级配额（如果用户有的话，不管是否过期） -->\n                    <div v-if=\"(user.finance?.premium_quota_total || 0) > 0\">\n                      高级: {{ user.finance?.premium_quota_used || 0 }}/{{ user.finance?.premium_quota_total || 0 }}\n                      <span class=\"text-gray-400\">(剩余: {{ (user.finance?.premium_quota_total || 0) - (user.finance?.premium_quota_used || 0) }})</span>\n                    </div>\n                  </div>\n                </div>\n              </td>\n\n              <!-- 余额 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <span class=\"balance text-green-400 font-mono\">¥{{ (user.finance?.balance || 0).toFixed(2) }}</span>\n              </td>\n\n              <!-- 注册时间 -->\n              <td class=\"px-4 py-3 border-t border-gray-700 text-sm text-gray-400\">\n                {{ formatDate(user.created_at) }}\n              </td>\n\n              <!-- 操作按钮组 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <div class=\"action-buttons flex space-x-1\">\n                  <button\n                    @click=\"viewUserFinance(user)\"\n                    class=\"admin-btn-secondary px-2 py-1 rounded text-sm\"\n                    title=\"财务详情\"\n                  >\n                    <i class=\"fas fa-chart-line\"></i>\n                  </button>\n                  <button\n                    @click=\"editUser(user)\"\n                    class=\"admin-btn-primary px-2 py-1 rounded text-sm\"\n                    title=\"编辑\"\n                  >\n                    <i class=\"fas fa-edit\"></i>\n                  </button>\n                  <button\n                    @click=\"manageVip(user)\"\n                    class=\"admin-btn-secondary px-2 py-1 rounded text-sm\"\n                    title=\"VIP管理\"\n                  >\n                    <i class=\"fas fa-crown\"></i>\n                  </button>\n                  <button\n                    @click=\"resetPassword(user)\"\n                    class=\"admin-btn-warning px-2 py-1 rounded text-sm\"\n                    title=\"重置密码\"\n                  >\n                    <i class=\"fas fa-key\"></i>\n                  </button>\n                  <button\n                    @click=\"sendMessage(user)\"\n                    class=\"admin-btn-info px-2 py-1 rounded text-sm\"\n                    title=\"发送私信\"\n                  >\n                    <i class=\"fas fa-envelope\"></i>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n\n      <div v-else id=\"no-data\" class=\"text-center py-8\">\n        <i class=\"fas fa-users text-4xl text-gray-600 mb-4\"></i>\n        <p class=\"text-gray-400\">暂无用户数据</p>\n      </div>\n\n      <!-- 分页 -->\n      <div v-if=\"pagination.pages > 1\" class=\"pagination\">\n        <button\n          class=\"btn-secondary\"\n          :disabled=\"!pagination.has_prev\"\n          @click=\"handlePageChange(pagination.prev_num)\"\n        >\n          上一页\n        </button>\n        <div class=\"page-numbers\">\n          <button\n            v-for=\"page in getPageNumbers()\"\n            :key=\"page\"\n            :class=\"['page-btn', page === pagination.page ? 'active' : '']\"\n            @click=\"handlePageChange(page)\"\n          >\n            {{ page }}\n          </button>\n        </div>\n        <button\n          class=\"btn-secondary\"\n          :disabled=\"!pagination.has_next\"\n          @click=\"handlePageChange(pagination.next_num)\"\n        >\n          下一页\n        </button>\n      </div>\n    </div>\n\n    <!-- 编辑用户对话框 -->\n    <el-dialog\n      v-model=\"showEditDialog\"\n      title=\"编辑用户\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n    >\n      <el-form :model=\"editForm\" label-width=\"100px\">\n        <el-form-item label=\"用户名\">\n          <el-input v-model=\"editForm.username\" placeholder=\"请输入用户名\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\">\n          <el-input v-model=\"editForm.email\" placeholder=\"请输入邮箱\" />\n        </el-form-item>\n        <el-form-item label=\"手机号\">\n          <el-input v-model=\"editForm.phone\" placeholder=\"请输入手机号（为手机端准备）\" />\n        </el-form-item>\n        <el-form-item label=\"角色\">\n          <el-select v-model=\"editForm.role\" placeholder=\"请选择角色\">\n            <el-option label=\"普通用户\" value=\"user\" />\n            <el-option label=\"管理员\" value=\"admin\" />\n            <el-option label=\"编辑\" value=\"editor\" />\n            <el-option label=\"客服\" value=\"customer_service\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"邮箱验证\">\n          <el-switch v-model=\"editForm.email_verified\" />\n          <span class=\"ml-2 text-sm text-gray-400\">\n            未验证用户无法查看首页文章列表\n          </span>\n        </el-form-item>\n        <el-form-item label=\"账户状态\">\n          <el-switch v-model=\"editForm.is_active\" />\n          <span class=\"ml-2 text-sm text-gray-400\">\n            禁用后用户无法登录\n          </span>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showEditDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitEditUser\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- VIP管理对话框 -->\n    <el-dialog\n      v-model=\"showVipDialog\"\n      title=\"VIP管理\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n    >\n      <div class=\"mb-4 p-4 bg-gray-800 rounded-lg\">\n        <h4 class=\"text-white mb-2\">当前状态</h4>\n        <p class=\"text-gray-300\">\n          用户：{{ vipForm.username }} |\n          当前VIP等级：{{ getVipStatusText(vipForm.current_vip_level, vipForm.current_expire_date) }}\n        </p>\n      </div>\n\n      <el-form :model=\"vipForm\" label-width=\"100px\">\n        <el-form-item label=\"VIP等级\">\n          <el-select v-model=\"vipForm.vip_level\" placeholder=\"请选择VIP等级\">\n            <el-option label=\"普通用户\" :value=\"0\" />\n            <el-option label=\"VIP\" :value=\"1\" />\n            <el-option label=\"VIP Pro\" :value=\"2\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"到期时间\" v-if=\"vipForm.vip_level > 0\">\n          <el-date-picker\n            v-model=\"vipForm.expire_date\"\n            type=\"date\"\n            placeholder=\"选择到期时间\"\n            format=\"YYYY-MM-DD\"\n            value-format=\"YYYY-MM-DD\"\n          />\n          <div class=\"text-sm text-gray-400 mt-1\">\n            留空表示永不过期\n          </div>\n        </el-form-item>\n        <el-form-item label=\"变更原因\">\n          <el-select v-model=\"vipForm.reason\" placeholder=\"请选择变更原因\">\n            <el-option label=\"管理员调整\" value=\"admin_change\" />\n            <el-option label=\"购买升级\" value=\"purchase\" />\n            <el-option label=\"到期降级\" value=\"expire\" />\n            <el-option label=\"违规处罚\" value=\"violation\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n\n      <div class=\"mt-4 p-4 bg-yellow-900 bg-opacity-30 rounded-lg\">\n        <h4 class=\"text-yellow-400 mb-2\">⚠️ 重要提示</h4>\n        <ul class=\"text-yellow-300 text-sm space-y-1\">\n          <li>• VIP到期后会自动降级为普通用户</li>\n          <li>• 配额使用记录会保存，但使用量会重置</li>\n          <li>• 降级不会影响已购买的文章访问权限</li>\n        </ul>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showVipDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitVipManage\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 私信对话框 -->\n    <el-dialog\n      v-model=\"showMessageDialog\"\n      title=\"发送私信\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n    >\n      <div class=\"mb-4 p-4 bg-gray-800 rounded-lg\">\n        <h4 class=\"text-white mb-2\">收信人</h4>\n        <p class=\"text-gray-300\">{{ messageForm.username }}</p>\n      </div>\n\n      <el-form :model=\"messageForm\" label-width=\"80px\">\n        <el-form-item label=\"私信内容\">\n          <el-input\n            v-model=\"messageForm.message\"\n            type=\"textarea\"\n            :rows=\"6\"\n            placeholder=\"请输入要发送的私信内容...\"\n            maxlength=\"500\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-form>\n\n      <div class=\"mt-4 p-4 bg-blue-900 bg-opacity-30 rounded-lg\">\n        <h4 class=\"text-blue-400 mb-2\">💡 私信功能说明</h4>\n        <ul class=\"text-blue-300 text-sm space-y-1\">\n          <li>• 私信将发送到用户的消息中心</li>\n          <li>• 用户登录后可在个人中心查看</li>\n          <li>• 支持系统通知和邮件提醒</li>\n        </ul>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showMessageDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitMessage\">发送</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { usersApi } from '@/api/users'\n\nconst router = useRouter()\n\n// 响应式数据\nconst users = ref([])\nconst loading = ref(false)\n\n// 编辑用户相关\nconst showEditDialog = ref(false)\nconst editForm = ref({\n  id: null,\n  username: '',\n  email: '',\n  role: 'user',\n  email_verified: false,\n  phone: '',\n  is_active: true\n})\n\nconst statistics = reactive({\n  total_users: 0,\n  vip_users: 0,\n  active_users: 0,\n  today_users: 0\n})\n\nconst filters = reactive({\n  search: '',\n  role: '',\n  vip_level: '',\n  page: 1,\n  per_page: 20\n})\n\nconst pagination = reactive({\n  page: 1,\n  pages: 1,\n  total: 0,\n  per_page: 20,\n  has_prev: false,\n  has_next: false,\n  prev_num: null,\n  next_num: null\n})\n\n// 方法\nconst formatDate = (date: string) => {\n  return new Date(date).toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit'\n  })\n}\n\nconst getVipStatusText = (vipLevel: number, expireAt?: string) => {\n  if (!vipLevel || vipLevel === 0) return '普通用户'\n\n  // 检查是否过期\n  if (expireAt) {\n    const expireDate = new Date(expireAt)\n    const now = new Date()\n    if (expireDate <= now) {\n      return '已过期'\n    }\n  }\n\n  if (vipLevel === 2) return 'VIP Pro'\n  if (vipLevel === 1) return 'VIP'\n  return '普通用户'\n}\n\nconst getVipBadgeClass = (vipLevel: number, expireAt?: string) => {\n  // 检查是否过期\n  if (vipLevel > 0 && expireAt) {\n    const expireDate = new Date(expireAt)\n    const now = new Date()\n    if (expireDate <= now) {\n      return 'px-2 py-1 rounded-full text-xs bg-red-900 text-red-300'  // 过期状态：红色\n    }\n  }\n\n  if (!vipLevel || vipLevel === 0) {\n    return 'px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300'  // 普通用户：灰色\n  }\n  if (vipLevel === 2) {\n    return 'px-2 py-1 rounded-full text-xs bg-purple-900 text-purple-300'  // VIP Pro：紫色\n  }\n  if (vipLevel === 1) {\n    return 'px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300'  // VIP：黄色\n  }\n  return 'px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300'\n}\n\n\n\nconst getPageNumbers = () => {\n  const pages = []\n  const current = pagination.page\n  const total = pagination.pages\n\n  // 简单的分页逻辑，显示当前页前后2页\n  const start = Math.max(1, current - 2)\n  const end = Math.min(total, current + 2)\n\n  for (let i = start; i <= end; i++) {\n    pages.push(i)\n  }\n\n  return pages\n}\n\n// 加载用户列表\nconst loadUsers = async () => {\n  loading.value = true\n  try {\n    const params = {\n      page: filters.page,\n      per_page: filters.per_page,\n      search: filters.search || undefined,\n      role: filters.role || undefined,\n      vip_level: filters.vip_level || undefined\n    }\n\n    const response = await usersApi.getList(params)\n\n    if (response.success) {\n      // 后端返回的格式：{success: true, users: [...], total: 100, pages: 5, current_page: 1}\n      users.value = response.users || []\n      pagination.page = response.current_page || 1\n      pagination.pages = response.pages || 1\n      pagination.total = response.total || 0\n      pagination.per_page = params.per_page || 20\n      pagination.has_prev = pagination.page > 1\n      pagination.has_next = pagination.page < pagination.pages\n    } else {\n      ElMessage.error(response.message || '加载用户列表失败')\n    }\n  } catch (error) {\n    console.error('加载用户失败:', error)\n    ElMessage.error('加载用户列表失败')\n  } finally {\n    loading.value = false\n  }\n}\n\n// 加载统计数据\nconst loadStatistics = async () => {\n  try {\n    const response = await usersApi.getStats()\n\n    if (response.success) {\n      Object.assign(statistics, response.data)\n    }\n  } catch (error) {\n    console.error('加载统计数据失败:', error)\n  }\n}\n\n// 刷新列表\nconst refreshList = () => {\n  loadUsers()\n  loadStatistics()\n}\n\n// 搜索用户\nconst searchUsers = () => {\n  filters.page = 1\n  loadUsers()\n}\n\n// 导出数据\nconst handleExport = async () => {\n  try {\n    ElMessage.info('正在导出用户数据...')\n    const response = await usersApi.exportData(filters)\n\n    if (response.success) {\n      // 创建下载链接\n      const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\n      const url = window.URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = url\n      link.download = `用户数据_${new Date().toISOString().split('T')[0]}.xlsx`\n      link.click()\n      window.URL.revokeObjectURL(url)\n\n      ElMessage.success('导出成功')\n    } else {\n      ElMessage.error(response.message || '导出失败')\n    }\n  } catch (error) {\n    console.error('导出失败:', error)\n    ElMessage.error('导出失败')\n  }\n}\n\n// 查看用户财务详情\nconst viewUserFinance = (user: any) => {\n  router.push(`/users/${user.id}/finance`)\n}\n\n// 编辑用户\nconst editUser = (user: any) => {\n  // 设置编辑表单数据\n  editForm.value = {\n    id: user.id,\n    username: user.username,\n    email: user.email,\n    role: user.role,\n    email_verified: user.email_verified,\n    phone: user.phone || '',\n    is_active: user.is_active !== false\n  }\n  showEditDialog.value = true\n}\n\n// VIP管理相关\nconst showVipDialog = ref(false)\nconst vipForm = ref({\n  id: null,\n  username: '',\n  current_vip_level: 0,\n  current_expire_date: '',\n  vip_level: 0,\n  expire_date: '',\n  reason: 'admin_change'\n})\n\n// 私信相关\nconst showMessageDialog = ref(false)\nconst messageForm = ref({\n  id: null,\n  username: '',\n  message: ''\n})\n\n// VIP管理\nconst manageVip = (user: any) => {\n  // 设置VIP管理表单数据\n  const currentVipLevel = user.finance?.vip_level || 0\n  const expireDate = user.finance?.vip_expire_at ?\n    new Date(user.finance.vip_expire_at).toISOString().split('T')[0] : ''\n\n  vipForm.value = {\n    id: user.id,\n    username: user.username,\n    current_vip_level: currentVipLevel,\n    current_expire_date: user.finance?.vip_expire_at || '',\n    vip_level: currentVipLevel,\n    expire_date: expireDate,\n    reason: 'admin_change'\n  }\n  showVipDialog.value = true\n}\n\n// 发送私信\nconst sendMessage = (user: any) => {\n  messageForm.value = {\n    id: user.id,\n    username: user.username,\n    message: ''\n  }\n  showMessageDialog.value = true\n}\n\n// 重置密码\nconst resetPassword = async (user: any) => {\n  try {\n    await ElMessageBox.confirm(\n      `确定要重置用户\"${user.username}\"的密码吗？`,\n      '重置密码确认',\n      {\n        confirmButtonText: '确定重置',\n        cancelButtonText: '取消',\n        type: 'warning',\n      }\n    )\n\n    const response = await usersApi.resetPassword(user.id)\n\n    if (response.success) {\n      ElMessage.success('密码重置成功')\n    } else {\n      ElMessage.error(response.message || '密码重置失败')\n    }\n  } catch (error) {\n    if (error !== 'cancel') {\n      console.error('密码重置失败:', error)\n      ElMessage.error('密码重置失败')\n    }\n  }\n}\n\nconst handlePageChange = (page: number) => {\n  if (page && page !== pagination.page) {\n    filters.page = page\n    pagination.page = page\n    loadUsers()\n  }\n}\n\n// 提交编辑用户\nconst submitEditUser = async () => {\n  try {\n    const response = await usersApi.update(editForm.value.id, {\n      username: editForm.value.username,\n      email: editForm.value.email,\n      role: editForm.value.role,\n      email_verified: editForm.value.email_verified,\n      phone: editForm.value.phone,\n      is_active: editForm.value.is_active\n    })\n\n    if (response.success) {\n      ElMessage.success('用户信息更新成功')\n      showEditDialog.value = false\n      loadUsers() // 刷新列表\n    } else {\n      ElMessage.error(response.message || '更新失败')\n    }\n  } catch (error) {\n    console.error('更新用户失败:', error)\n    ElMessage.error('更新用户失败')\n  }\n}\n\n// 提交VIP管理\nconst submitVipManage = async () => {\n  try {\n    const response = await usersApi.manageVip(vipForm.value.id, {\n      vip_level: vipForm.value.vip_level,\n      expire_date: vipForm.value.expire_date || null,\n      reason: vipForm.value.reason\n    })\n\n    if (response.success) {\n      ElMessage.success('VIP状态更新成功')\n      showVipDialog.value = false\n      loadUsers() // 刷新列表\n    } else {\n      ElMessage.error(response.message || 'VIP状态更新失败')\n    }\n  } catch (error) {\n    console.error('VIP状态更新失败:', error)\n    ElMessage.error('VIP状态更新失败')\n  }\n}\n\n// 提交私信\nconst submitMessage = async () => {\n  if (!messageForm.value.message.trim()) {\n    ElMessage.warning('请输入私信内容')\n    return\n  }\n\n  try {\n    const response = await usersApi.sendMessage(messageForm.value.id, messageForm.value.message)\n\n    if (response.success) {\n      ElMessage.success('私信发送成功')\n      showMessageDialog.value = false\n      messageForm.value.message = ''\n    } else {\n      ElMessage.error(response.message || '私信发送失败')\n    }\n  } catch (error) {\n    console.error('私信发送失败:', error)\n    ElMessage.error('私信发送失败')\n  }\n}\n\n// 初始化\nonMounted(() => {\n  loadUsers()\n  loadStatistics()\n})\n</script>\n\n<style scoped>\n/* 深色主题样式 - 完全按照原Flask管理后台 */\n.user-management {\n  color: #e0e0e0;\n}\n\n/* 页面标题 */\n.page-header {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1.5rem;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .page-header {\n    flex-direction: row;\n    align-items: center;\n    gap: 0;\n  }\n}\n\n.page-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #ffffff;\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n@media (min-width: 768px) {\n  .header-actions {\n    flex-direction: row;\n    gap: 0.75rem;\n  }\n}\n\n/* 统计网格 */\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n@media (min-width: 768px) {\n  .stats-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n.stat-card {\n  background-color: #374151;\n  border: 1px solid #4B5563;\n  border-radius: 0.5rem;\n  padding: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.stat-icon {\n  padding: 0.75rem;\n  border-radius: 50%;\n  font-size: 1.25rem;\n}\n\n.stat-icon .fa-users {\n  background-color: rgba(59, 130, 246, 0.2);\n}\n\n.stat-icon .fa-crown {\n  background-color: rgba(245, 158, 11, 0.2);\n}\n\n.stat-icon .fa-check-circle {\n  background-color: rgba(16, 185, 129, 0.2);\n}\n\n.stat-icon .fa-calendar-day {\n  background-color: rgba(139, 92, 246, 0.2);\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #ffffff;\n  margin: 0;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  color: #9ca3af;\n  margin: 0;\n}\n\n/* 筛选卡片 */\n.filter-card {\n  background-color: #374151;\n  border: 1px solid #4B5563;\n  border-radius: 0.5rem;\n  padding: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.filter-form {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .filter-form {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group label {\n  color: #d1d5db;\n  font-size: 0.875rem;\n  margin-bottom: 0.5rem;\n}\n\n.form-input, .form-select {\n  background-color: #1f2937;\n  border: 1px solid #374151;\n  border-radius: 0.5rem;\n  padding: 0.5rem 0.75rem;\n  color: #e0e0e0;\n  font-size: 0.875rem;\n}\n\n.form-input:focus, .form-select:focus {\n  outline: none;\n  border-color: #f59e0b;\n  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);\n}\n\n.form-actions {\n  display: flex;\n  align-items: end;\n}\n\n/* 按钮样式 - 按照原Flask样式 */\n.btn-primary {\n  background-color: #f59e0b;\n  color: #1f2937;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-primary:hover {\n  background-color: #d97706;\n}\n\n.btn-secondary {\n  background-color: #6b7280;\n  color: #d1d5db;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-secondary:hover {\n  background-color: #4b5563;\n  color: #fff;\n}\n\n.btn-secondary:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.btn-small {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n}\n\n/* 数据卡片 */\n.data-card {\n  background-color: #374151;\n  border: 1px solid #4B5563;\n  border-radius: 0.5rem;\n  padding: 1rem;\n  margin-bottom: 1.5rem;\n  overflow-x: auto;\n}\n\n.loading-container, .no-data-container {\n  text-align: center;\n  padding: 2rem;\n}\n\n/* 表格样式 */\n.table-container {\n  overflow-x: auto;\n}\n\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.data-table th,\n.data-table td {\n  padding: 0.75rem 1rem;\n  text-align: left;\n  border-bottom: 1px solid #4B5563;\n}\n\n.data-table th {\n  background-color: transparent;\n  color: #d1d5db;\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.data-table td {\n  color: #e0e0e0;\n}\n\n.user-row:hover {\n  background-color: rgba(75, 85, 99, 0.5);\n  transition: background-color 0.2s;\n}\n\n.user-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.user-name {\n  font-weight: 500;\n  color: #ffffff;\n}\n\n.user-email {\n  font-size: 0.875rem;\n  color: #9ca3af;\n}\n\n.email-status {\n  font-size: 0.75rem;\n}\n\n.email-status.verified {\n  color: #10b981;\n}\n\n.email-status.unverified {\n  color: #ef4444;\n}\n\n.role-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.role-admin {\n  background-color: rgba(220, 38, 38, 0.2);\n  color: #fca5a5;\n}\n\n.role-user {\n  background-color: rgba(107, 114, 128, 0.2);\n  color: #d1d5db;\n}\n\n.vip-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.vip-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.vip-normal {\n  background-color: rgba(107, 114, 128, 0.2);\n  color: #d1d5db;\n}\n\n.vip-basic {\n  background-color: rgba(16, 185, 129, 0.2);\n  color: #6ee7b7;\n}\n\n.vip-premium {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fcd34d;\n}\n\n.vip-expire {\n  font-size: 0.75rem;\n  color: #9ca3af;\n}\n\n.quota-info {\n  font-size: 0.875rem;\n}\n\n.balance {\n  color: #10b981;\n  font-weight: 500;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 0.25rem;\n}\n\n/* 分页 */\n.pagination {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 0.5rem;\n  margin-top: 1.5rem;\n}\n\n.page-numbers {\n  display: inline-flex;\n  border-radius: 0.375rem;\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n}\n\n.page-btn {\n  background-color: #374151;\n  color: #d1d5db;\n  border: 1px solid #4b5563;\n  border-left: none;\n  padding: 0.5rem 0.75rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.page-btn:first-child {\n  border-left: 1px solid #4b5563;\n  border-top-left-radius: 0.375rem;\n  border-bottom-left-radius: 0.375rem;\n}\n\n.page-btn:last-child {\n  border-top-right-radius: 0.375rem;\n  border-bottom-right-radius: 0.375rem;\n}\n\n.page-btn:hover {\n  background-color: #4b5563;\n  color: #fff;\n}\n\n.page-btn.active {\n  background-color: #f59e0b;\n  color: #1f2937;\n  border-color: #f59e0b;\n}\n\n/* 响应式 */\n@media (max-width: 768px) {\n  .data-table th:nth-child(n+6),\n  .data-table td:nth-child(n+6) {\n    display: none;\n  }\n}\n\n@media (max-width: 640px) {\n  .data-table th:nth-child(n+4),\n  .data-table td:nth-child(n+4) {\n    display: none;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"user-management\">\n    <!-- 页面头部区域 - 完全复刻原版Flask -->\n    <div class=\"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0\">\n      <h1 class=\"text-2xl font-bold text-white\">用户管理</h1>\n      <div class=\"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3\">\n        <button @click=\"refreshList\" class=\"admin-btn-secondary px-4 py-2 rounded-lg text-center\">\n          <i class=\"fas fa-sync-alt mr-2\"></i>刷新列表\n        </button>\n        <button @click=\"handleExport\" class=\"admin-btn-primary px-4 py-2 rounded-lg text-center\">\n          <i class=\"fas fa-download mr-2\"></i>导出数据\n        </button>\n      </div>\n    </div>\n\n    <!-- 用户统计卡片区域 - 完全复刻原版Flask -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <div class=\"admin-card p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"p-3 rounded-full bg-blue-500 bg-opacity-20\">\n            <i class=\"fas fa-users text-blue-400 text-xl\"></i>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-gray-400 text-sm\">总用户数</p>\n            <p class=\"text-2xl font-bold text-white\">{{ statistics.total_users || '-' }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"admin-card p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"p-3 rounded-full bg-yellow-500 bg-opacity-20\">\n            <i class=\"fas fa-crown text-yellow-400 text-xl\"></i>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-gray-400 text-sm\">VIP用户</p>\n            <p class=\"text-2xl font-bold text-white\">{{ statistics.vip_users || '-' }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"admin-card p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"p-3 rounded-full bg-green-500 bg-opacity-20\">\n            <i class=\"fas fa-check-circle text-green-400 text-xl\"></i>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-gray-400 text-sm\">已验证邮箱</p>\n            <p class=\"text-2xl font-bold text-white\">{{ statistics.verified_users || '-' }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"admin-card p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"p-3 rounded-full bg-purple-500 bg-opacity-20\">\n            <i class=\"fas fa-calendar-day text-purple-400 text-xl\"></i>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-gray-400 text-sm\">今日新增</p>\n            <p class=\"text-2xl font-bold text-white\">{{ statistics.today_users || '-' }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 过滤和搜索 - 完全复刻原版Flask -->\n    <div class=\"admin-card p-4 mb-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <!-- 搜索框 -->\n        <div>\n          <label for=\"search\" class=\"block mb-2 text-gray-300\">搜索用户</label>\n          <input\n            v-model=\"filters.search\"\n            type=\"text\"\n            id=\"search\"\n            class=\"form-input w-full px-3 py-2 rounded-lg\"\n            placeholder=\"用户名或邮箱...\"\n            @keyup.enter=\"searchUsers\"\n          />\n        </div>\n\n        <!-- 角色过滤 -->\n        <div>\n          <label for=\"role-filter\" class=\"block mb-2 text-gray-300\">角色过滤</label>\n          <select v-model=\"filters.role\" id=\"role-filter\" class=\"form-input w-full px-3 py-2 rounded-lg\">\n            <option value=\"\">全部角色</option>\n            <option value=\"user\">普通用户</option>\n            <option value=\"admin\">管理员</option>\n            <option value=\"editor\">编辑</option>\n          </select>\n        </div>\n\n        <!-- VIP状态过滤 -->\n        <div>\n          <label for=\"vip-filter\" class=\"block mb-2 text-gray-300\">VIP状态</label>\n          <select v-model=\"filters.vip_level\" id=\"vip-filter\" class=\"form-input w-full px-3 py-2 rounded-lg\">\n            <option value=\"\">全部状态</option>\n            <option value=\"0\">普通用户</option>\n            <option value=\"1\">VIP</option>\n            <option value=\"2\">VIP Pro</option>\n          </select>\n        </div>\n\n        <!-- 搜索按钮 -->\n        <div class=\"flex items-end\">\n          <button @click=\"searchUsers\" id=\"search-btn\" class=\"admin-btn-primary px-4 py-2 rounded-lg w-full\">\n            <i class=\"fas fa-search mr-2\"></i>搜索\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 用户表格 - 完全复刻原版Flask -->\n    <div class=\"admin-card p-4 mb-6 overflow-x-auto\">\n      <div v-if=\"loading\" id=\"loading\" class=\"text-center py-8\">\n        <i class=\"fas fa-spinner fa-spin text-2xl text-gray-400\"></i>\n        <p class=\"text-gray-400 mt-2\">加载中...</p>\n      </div>\n\n      <div v-else-if=\"users.length > 0\" id=\"users-table\">\n        <table class=\"table-admin w-full\">\n          <thead>\n            <tr>\n              <th class=\"px-4 py-2 text-left\">ID</th>\n              <th class=\"px-4 py-2 text-left\">用户信息</th>\n              <th class=\"px-4 py-2 text-left\">角色</th>\n              <th class=\"px-4 py-2 text-left\">VIP状态</th>\n              <th class=\"px-4 py-2 text-left\">配额使用</th>\n              <th class=\"px-4 py-2 text-left\">余额</th>\n              <th class=\"px-4 py-2 text-left\">注册时间</th>\n              <th class=\"px-4 py-2 text-left\">操作</th>\n            </tr>\n          </thead>\n          <tbody id=\"users-tbody\">\n            <tr\n              v-for=\"user in users\"\n              :key=\"user.id\"\n              class=\"hover:bg-gray-700/50 transition-colors\"\n            >\n              <!-- ID -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">{{ user.id }}</td>\n\n              <!-- 用户信息 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <div class=\"user-info\">\n                  <div class=\"user-name text-white font-medium\">{{ user.username }}</div>\n                  <div class=\"user-email text-gray-400 text-sm\">{{ user.email || '无邮箱' }}</div>\n                  <div :class=\"['email-status text-xs', user.email_verified ? 'text-green-400' : 'text-red-400']\">\n                    {{ user.email_verified ? '✓ 已验证' : '✗ 未验证' }}\n                  </div>\n                </div>\n              </td>\n\n              <!-- 角色 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <span :class=\"['px-2 py-1 rounded-full text-xs', user.role === 'admin' ? 'bg-red-900 text-red-300' : 'bg-gray-700 text-gray-300']\">\n                  {{ user.role === 'admin' ? '管理员' : '普通用户' }}\n                </span>\n              </td>\n\n              <!-- VIP状态 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <div class=\"vip-info\">\n                  <span :class=\"getVipBadgeClass(user.finance?.vip_level, user.finance?.vip_expire_at)\">\n                    {{ getVipStatusText(user.finance?.vip_level, user.finance?.vip_expire_at) }}\n                  </span>\n                  <div v-if=\"user.finance?.vip_expire_at && user.finance?.vip_level > 0\" class=\"vip-expire text-xs text-gray-400 mt-1\">\n                    到期: {{ formatDate(user.finance.vip_expire_at) }}\n                  </div>\n                </div>\n              </td>\n\n              <!-- 配额使用 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <div class=\"quota-info text-sm\">\n                  <!-- 管理员显示无限制 -->\n                  <div v-if=\"user.role === 'admin' || user.role === 'superadmin'\">\n                    <div class=\"text-yellow-400\">基础: 无限制</div>\n                    <div class=\"text-purple-400\">高级: 无限制</div>\n                  </div>\n                  <!-- 普通用户显示实际配额 -->\n                  <div v-else>\n                    <div>\n                      基础: {{ user.finance?.basic_quota_used || 0 }}/{{ user.finance?.basic_quota_total || 0 }}\n                      <span class=\"text-gray-400\">(剩余: {{ (user.finance?.basic_quota_total || 0) - (user.finance?.basic_quota_used || 0) }})</span>\n                    </div>\n                    <!-- 显示高级配额（如果用户有的话，不管是否过期） -->\n                    <div v-if=\"(user.finance?.premium_quota_total || 0) > 0\">\n                      高级: {{ user.finance?.premium_quota_used || 0 }}/{{ user.finance?.premium_quota_total || 0 }}\n                      <span class=\"text-gray-400\">(剩余: {{ (user.finance?.premium_quota_total || 0) - (user.finance?.premium_quota_used || 0) }})</span>\n                    </div>\n                  </div>\n                </div>\n              </td>\n\n              <!-- 余额 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <span class=\"balance text-green-400 font-mono\">¥{{ (user.finance?.balance || 0).toFixed(2) }}</span>\n              </td>\n\n              <!-- 注册时间 -->\n              <td class=\"px-4 py-3 border-t border-gray-700 text-sm text-gray-400\">\n                {{ formatDate(user.created_at) }}\n              </td>\n\n              <!-- 操作按钮组 -->\n              <td class=\"px-4 py-3 border-t border-gray-700\">\n                <div class=\"action-buttons flex space-x-1\">\n                  <button\n                    @click=\"viewUserFinance(user)\"\n                    class=\"admin-btn-secondary px-2 py-1 rounded text-sm\"\n                    title=\"财务详情\"\n                  >\n                    <i class=\"fas fa-chart-line\"></i>\n                  </button>\n                  <button\n                    @click=\"editUser(user)\"\n                    class=\"admin-btn-primary px-2 py-1 rounded text-sm\"\n                    title=\"编辑\"\n                  >\n                    <i class=\"fas fa-edit\"></i>\n                  </button>\n                  <button\n                    @click=\"manageVip(user)\"\n                    class=\"admin-btn-secondary px-2 py-1 rounded text-sm\"\n                    title=\"VIP管理\"\n                  >\n                    <i class=\"fas fa-crown\"></i>\n                  </button>\n                  <button\n                    @click=\"resetPassword(user)\"\n                    class=\"admin-btn-warning px-2 py-1 rounded text-sm\"\n                    title=\"重置密码\"\n                  >\n                    <i class=\"fas fa-key\"></i>\n                  </button>\n                  <button\n                    @click=\"sendMessage(user)\"\n                    class=\"admin-btn-info px-2 py-1 rounded text-sm\"\n                    title=\"发送私信\"\n                  >\n                    <i class=\"fas fa-envelope\"></i>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n\n      <div v-else id=\"no-data\" class=\"text-center py-8\">\n        <i class=\"fas fa-users text-4xl text-gray-600 mb-4\"></i>\n        <p class=\"text-gray-400\">暂无用户数据</p>\n      </div>\n\n      <!-- 分页 -->\n      <div v-if=\"pagination.pages > 1\" class=\"pagination\">\n        <button\n          class=\"btn-secondary\"\n          :disabled=\"!pagination.has_prev\"\n          @click=\"handlePageChange(pagination.prev_num)\"\n        >\n          上一页\n        </button>\n        <div class=\"page-numbers\">\n          <button\n            v-for=\"page in getPageNumbers()\"\n            :key=\"page\"\n            :class=\"['page-btn', page === pagination.page ? 'active' : '']\"\n            @click=\"handlePageChange(page)\"\n          >\n            {{ page }}\n          </button>\n        </div>\n        <button\n          class=\"btn-secondary\"\n          :disabled=\"!pagination.has_next\"\n          @click=\"handlePageChange(pagination.next_num)\"\n        >\n          下一页\n        </button>\n      </div>\n    </div>\n\n    <!-- 编辑用户对话框 -->\n    <el-dialog\n      v-model=\"showEditDialog\"\n      title=\"编辑用户\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n    >\n      <el-form :model=\"editForm\" label-width=\"100px\">\n        <el-form-item label=\"用户名\">\n          <el-input v-model=\"editForm.username\" placeholder=\"请输入用户名\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\">\n          <el-input v-model=\"editForm.email\" placeholder=\"请输入邮箱\" />\n        </el-form-item>\n        <el-form-item label=\"手机号\">\n          <el-input v-model=\"editForm.phone\" placeholder=\"请输入手机号（为手机端准备）\" />\n        </el-form-item>\n        <el-form-item label=\"角色\">\n          <el-select v-model=\"editForm.role\" placeholder=\"请选择角色\">\n            <el-option label=\"普通用户\" value=\"user\" />\n            <el-option label=\"管理员\" value=\"admin\" />\n            <el-option label=\"编辑\" value=\"editor\" />\n            <el-option label=\"客服\" value=\"customer_service\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"邮箱验证\">\n          <el-switch v-model=\"editForm.email_verified\" />\n          <span class=\"ml-2 text-sm text-gray-400\">\n            未验证用户无法查看首页文章列表\n          </span>\n        </el-form-item>\n        <el-form-item label=\"账户状态\">\n          <el-switch v-model=\"editForm.is_active\" />\n          <span class=\"ml-2 text-sm text-gray-400\">\n            禁用后用户无法登录\n          </span>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showEditDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitEditUser\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- VIP管理对话框 -->\n    <el-dialog\n      v-model=\"showVipDialog\"\n      title=\"VIP管理\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n    >\n      <div class=\"mb-4 p-4 bg-gray-800 rounded-lg\">\n        <h4 class=\"text-white mb-2\">当前状态</h4>\n        <p class=\"text-gray-300\">\n          用户：{{ vipForm.username }} |\n          当前VIP等级：{{ getVipStatusText(vipForm.current_vip_level, vipForm.current_expire_date) }}\n        </p>\n      </div>\n\n      <el-form :model=\"vipForm\" label-width=\"100px\">\n        <el-form-item label=\"VIP等级\">\n          <el-select v-model=\"vipForm.vip_level\" placeholder=\"请选择VIP等级\">\n            <el-option label=\"普通用户\" :value=\"0\" />\n            <el-option label=\"VIP\" :value=\"1\" />\n            <el-option label=\"VIP Pro\" :value=\"2\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"到期时间\" v-if=\"vipForm.vip_level > 0\">\n          <el-date-picker\n            v-model=\"vipForm.expire_date\"\n            type=\"date\"\n            placeholder=\"选择到期时间\"\n            format=\"YYYY-MM-DD\"\n            value-format=\"YYYY-MM-DD\"\n          />\n          <div class=\"text-sm text-gray-400 mt-1\">\n            留空表示永不过期\n          </div>\n        </el-form-item>\n        <el-form-item label=\"变更原因\">\n          <el-select v-model=\"vipForm.reason\" placeholder=\"请选择变更原因\">\n            <el-option label=\"管理员调整\" value=\"admin_change\" />\n            <el-option label=\"购买升级\" value=\"purchase\" />\n            <el-option label=\"到期降级\" value=\"expire\" />\n            <el-option label=\"违规处罚\" value=\"violation\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n\n      <div class=\"mt-4 p-4 bg-yellow-900 bg-opacity-30 rounded-lg\">\n        <h4 class=\"text-yellow-400 mb-2\">⚠️ 重要提示</h4>\n        <ul class=\"text-yellow-300 text-sm space-y-1\">\n          <li>• VIP到期后会自动降级为普通用户</li>\n          <li>• 配额使用记录会保存，但使用量会重置</li>\n          <li>• 降级不会影响已购买的文章访问权限</li>\n        </ul>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showVipDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitVipManage\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 私信对话框 -->\n    <el-dialog\n      v-model=\"showMessageDialog\"\n      title=\"发送私信\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n    >\n      <div class=\"mb-4 p-4 bg-gray-800 rounded-lg\">\n        <h4 class=\"text-white mb-2\">收信人</h4>\n        <p class=\"text-gray-300\">{{ messageForm.username }}</p>\n      </div>\n\n      <el-form :model=\"messageForm\" label-width=\"80px\">\n        <el-form-item label=\"私信内容\">\n          <el-input\n            v-model=\"messageForm.message\"\n            type=\"textarea\"\n            :rows=\"6\"\n            placeholder=\"请输入要发送的私信内容...\"\n            maxlength=\"500\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-form>\n\n      <div class=\"mt-4 p-4 bg-blue-900 bg-opacity-30 rounded-lg\">\n        <h4 class=\"text-blue-400 mb-2\">💡 私信功能说明</h4>\n        <ul class=\"text-blue-300 text-sm space-y-1\">\n          <li>• 私信将发送到用户的消息中心</li>\n          <li>• 用户登录后可在个人中心查看</li>\n          <li>• 支持系统通知和邮件提醒</li>\n        </ul>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showMessageDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitMessage\">发送</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { usersApi } from '@/api/users'\n\nconst router = useRouter()\n\n// 响应式数据\nconst users = ref([])\nconst loading = ref(false)\n\n// 编辑用户相关\nconst showEditDialog = ref(false)\nconst editForm = ref({\n  id: null,\n  username: '',\n  email: '',\n  role: 'user',\n  email_verified: false,\n  phone: '',\n  is_active: true\n})\n\nconst statistics = reactive({\n  total_users: 0,\n  vip_users: 0,\n  active_users: 0,\n  today_users: 0\n})\n\nconst filters = reactive({\n  search: '',\n  role: '',\n  vip_level: '',\n  page: 1,\n  per_page: 20\n})\n\nconst pagination = reactive({\n  page: 1,\n  pages: 1,\n  total: 0,\n  per_page: 20,\n  has_prev: false,\n  has_next: false,\n  prev_num: null,\n  next_num: null\n})\n\n// 方法\nconst formatDate = (date: string) => {\n  return new Date(date).toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit'\n  })\n}\n\nconst getVipStatusText = (vipLevel: number, expireAt?: string) => {\n  if (!vipLevel || vipLevel === 0) return '普通用户'\n\n  // 检查是否过期\n  if (expireAt) {\n    const expireDate = new Date(expireAt)\n    const now = new Date()\n    if (expireDate <= now) {\n      return '已过期'\n    }\n  }\n\n  if (vipLevel === 2) return 'VIP Pro'\n  if (vipLevel === 1) return 'VIP'\n  return '普通用户'\n}\n\nconst getVipBadgeClass = (vipLevel: number, expireAt?: string) => {\n  // 检查是否过期\n  if (vipLevel > 0 && expireAt) {\n    const expireDate = new Date(expireAt)\n    const now = new Date()\n    if (expireDate <= now) {\n      return 'px-2 py-1 rounded-full text-xs bg-red-900 text-red-300'  // 过期状态：红色\n    }\n  }\n\n  if (!vipLevel || vipLevel === 0) {\n    return 'px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300'  // 普通用户：灰色\n  }\n  if (vipLevel === 2) {\n    return 'px-2 py-1 rounded-full text-xs bg-purple-900 text-purple-300'  // VIP Pro：紫色\n  }\n  if (vipLevel === 1) {\n    return 'px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300'  // VIP：黄色\n  }\n  return 'px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300'\n}\n\n\n\nconst getPageNumbers = () => {\n  const pages = []\n  const current = pagination.page\n  const total = pagination.pages\n\n  // 简单的分页逻辑，显示当前页前后2页\n  const start = Math.max(1, current - 2)\n  const end = Math.min(total, current + 2)\n\n  for (let i = start; i <= end; i++) {\n    pages.push(i)\n  }\n\n  return pages\n}\n\n// 加载用户列表\nconst loadUsers = async () => {\n  loading.value = true\n  try {\n    const params = {\n      page: filters.page,\n      per_page: filters.per_page,\n      search: filters.search || undefined,\n      role: filters.role || undefined,\n      vip_level: filters.vip_level || undefined\n    }\n\n    const response = await usersApi.getList(params)\n\n    if (response.success) {\n      // 后端返回的格式：{success: true, users: [...], total: 100, pages: 5, current_page: 1}\n      users.value = response.users || []\n      pagination.page = response.current_page || 1\n      pagination.pages = response.pages || 1\n      pagination.total = response.total || 0\n      pagination.per_page = params.per_page || 20\n      pagination.has_prev = pagination.page > 1\n      pagination.has_next = pagination.page < pagination.pages\n    } else {\n      ElMessage.error(response.message || '加载用户列表失败')\n    }\n  } catch (error) {\n    console.error('加载用户失败:', error)\n    ElMessage.error('加载用户列表失败')\n  } finally {\n    loading.value = false\n  }\n}\n\n// 加载统计数据\nconst loadStatistics = async () => {\n  try {\n    const response = await usersApi.getStats()\n\n    if (response.success) {\n      Object.assign(statistics, response.data)\n    }\n  } catch (error) {\n    console.error('加载统计数据失败:', error)\n  }\n}\n\n// 刷新列表\nconst refreshList = () => {\n  loadUsers()\n  loadStatistics()\n}\n\n// 搜索用户\nconst searchUsers = () => {\n  filters.page = 1\n  loadUsers()\n}\n\n// 导出数据\nconst handleExport = async () => {\n  try {\n    ElMessage.info('正在导出用户数据...')\n    const response = await usersApi.exportData(filters)\n\n    if (response.success) {\n      // 创建下载链接\n      const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\n      const url = window.URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = url\n      link.download = `用户数据_${new Date().toISOString().split('T')[0]}.xlsx`\n      link.click()\n      window.URL.revokeObjectURL(url)\n\n      ElMessage.success('导出成功')\n    } else {\n      ElMessage.error(response.message || '导出失败')\n    }\n  } catch (error) {\n    console.error('导出失败:', error)\n    ElMessage.error('导出失败')\n  }\n}\n\n// 查看用户财务详情\nconst viewUserFinance = (user: any) => {\n  router.push(`/users/${user.id}/finance`)\n}\n\n// 编辑用户\nconst editUser = (user: any) => {\n  // 设置编辑表单数据\n  editForm.value = {\n    id: user.id,\n    username: user.username,\n    email: user.email,\n    role: user.role,\n    email_verified: user.email_verified,\n    phone: user.phone || '',\n    is_active: user.is_active !== false\n  }\n  showEditDialog.value = true\n}\n\n// VIP管理相关\nconst showVipDialog = ref(false)\nconst vipForm = ref({\n  id: null,\n  username: '',\n  current_vip_level: 0,\n  current_expire_date: '',\n  vip_level: 0,\n  expire_date: '',\n  reason: 'admin_change'\n})\n\n// 私信相关\nconst showMessageDialog = ref(false)\nconst messageForm = ref({\n  id: null,\n  username: '',\n  message: ''\n})\n\n// VIP管理\nconst manageVip = (user: any) => {\n  // 设置VIP管理表单数据\n  const currentVipLevel = user.finance?.vip_level || 0\n  const expireDate = user.finance?.vip_expire_at ?\n    new Date(user.finance.vip_expire_at).toISOString().split('T')[0] : ''\n\n  vipForm.value = {\n    id: user.id,\n    username: user.username,\n    current_vip_level: currentVipLevel,\n    current_expire_date: user.finance?.vip_expire_at || '',\n    vip_level: currentVipLevel,\n    expire_date: expireDate,\n    reason: 'admin_change'\n  }\n  showVipDialog.value = true\n}\n\n// 发送私信\nconst sendMessage = (user: any) => {\n  messageForm.value = {\n    id: user.id,\n    username: user.username,\n    message: ''\n  }\n  showMessageDialog.value = true\n}\n\n// 重置密码\nconst resetPassword = async (user: any) => {\n  try {\n    await ElMessageBox.confirm(\n      `确定要重置用户\"${user.username}\"的密码吗？`,\n      '重置密码确认',\n      {\n        confirmButtonText: '确定重置',\n        cancelButtonText: '取消',\n        type: 'warning',\n      }\n    )\n\n    const response = await usersApi.resetPassword(user.id)\n\n    if (response.success) {\n      ElMessage.success('密码重置成功')\n    } else {\n      ElMessage.error(response.message || '密码重置失败')\n    }\n  } catch (error) {\n    if (error !== 'cancel') {\n      console.error('密码重置失败:', error)\n      ElMessage.error('密码重置失败')\n    }\n  }\n}\n\nconst handlePageChange = (page: number) => {\n  if (page && page !== pagination.page) {\n    filters.page = page\n    pagination.page = page\n    loadUsers()\n  }\n}\n\n// 提交编辑用户\nconst submitEditUser = async () => {\n  try {\n    const response = await usersApi.update(editForm.value.id, {\n      username: editForm.value.username,\n      email: editForm.value.email,\n      role: editForm.value.role,\n      email_verified: editForm.value.email_verified,\n      phone: editForm.value.phone,\n      is_active: editForm.value.is_active\n    })\n\n    if (response.success) {\n      ElMessage.success('用户信息更新成功')\n      showEditDialog.value = false\n      loadUsers() // 刷新列表\n    } else {\n      ElMessage.error(response.message || '更新失败')\n    }\n  } catch (error) {\n    console.error('更新用户失败:', error)\n    ElMessage.error('更新用户失败')\n  }\n}\n\n// 提交VIP管理\nconst submitVipManage = async () => {\n  try {\n    const response = await usersApi.manageVip(vipForm.value.id, {\n      vip_level: vipForm.value.vip_level,\n      expire_date: vipForm.value.expire_date || null,\n      reason: vipForm.value.reason\n    })\n\n    if (response.success) {\n      ElMessage.success('VIP状态更新成功')\n      showVipDialog.value = false\n      loadUsers() // 刷新列表\n    } else {\n      ElMessage.error(response.message || 'VIP状态更新失败')\n    }\n  } catch (error) {\n    console.error('VIP状态更新失败:', error)\n    ElMessage.error('VIP状态更新失败')\n  }\n}\n\n// 提交私信\nconst submitMessage = async () => {\n  if (!messageForm.value.message.trim()) {\n    ElMessage.warning('请输入私信内容')\n    return\n  }\n\n  try {\n    const response = await usersApi.sendMessage(messageForm.value.id, messageForm.value.message)\n\n    if (response.success) {\n      ElMessage.success('私信发送成功')\n      showMessageDialog.value = false\n      messageForm.value.message = ''\n    } else {\n      ElMessage.error(response.message || '私信发送失败')\n    }\n  } catch (error) {\n    console.error('私信发送失败:', error)\n    ElMessage.error('私信发送失败')\n  }\n}\n\n// 初始化\nonMounted(() => {\n  loadUsers()\n  loadStatistics()\n})\n</script>\n\n<style scoped>\n/* 深色主题样式 - 完全按照原Flask管理后台 */\n.user-management {\n  color: #e0e0e0;\n}\n\n/* 页面标题 */\n.page-header {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1.5rem;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .page-header {\n    flex-direction: row;\n    align-items: center;\n    gap: 0;\n  }\n}\n\n.page-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #ffffff;\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n@media (min-width: 768px) {\n  .header-actions {\n    flex-direction: row;\n    gap: 0.75rem;\n  }\n}\n\n/* 统计网格 */\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n@media (min-width: 768px) {\n  .stats-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n.stat-card {\n  background-color: #374151;\n  border: 1px solid #4B5563;\n  border-radius: 0.5rem;\n  padding: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.stat-icon {\n  padding: 0.75rem;\n  border-radius: 50%;\n  font-size: 1.25rem;\n}\n\n.stat-icon .fa-users {\n  background-color: rgba(59, 130, 246, 0.2);\n}\n\n.stat-icon .fa-crown {\n  background-color: rgba(245, 158, 11, 0.2);\n}\n\n.stat-icon .fa-check-circle {\n  background-color: rgba(16, 185, 129, 0.2);\n}\n\n.stat-icon .fa-calendar-day {\n  background-color: rgba(139, 92, 246, 0.2);\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #ffffff;\n  margin: 0;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  color: #9ca3af;\n  margin: 0;\n}\n\n/* 筛选卡片 */\n.filter-card {\n  background-color: #374151;\n  border: 1px solid #4B5563;\n  border-radius: 0.5rem;\n  padding: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.filter-form {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .filter-form {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group label {\n  color: #d1d5db;\n  font-size: 0.875rem;\n  margin-bottom: 0.5rem;\n}\n\n.form-input, .form-select {\n  background-color: #1f2937;\n  border: 1px solid #374151;\n  border-radius: 0.5rem;\n  padding: 0.5rem 0.75rem;\n  color: #e0e0e0;\n  font-size: 0.875rem;\n}\n\n.form-input:focus, .form-select:focus {\n  outline: none;\n  border-color: #f59e0b;\n  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);\n}\n\n.form-actions {\n  display: flex;\n  align-items: end;\n}\n\n/* 按钮样式 - 按照原Flask样式 */\n.btn-primary {\n  background-color: #f59e0b;\n  color: #1f2937;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-primary:hover {\n  background-color: #d97706;\n}\n\n.btn-secondary {\n  background-color: #6b7280;\n  color: #d1d5db;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-secondary:hover {\n  background-color: #4b5563;\n  color: #fff;\n}\n\n.btn-secondary:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.btn-small {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n}\n\n/* 数据卡片 */\n.data-card {\n  background-color: #374151;\n  border: 1px solid #4B5563;\n  border-radius: 0.5rem;\n  padding: 1rem;\n  margin-bottom: 1.5rem;\n  overflow-x: auto;\n}\n\n.loading-container, .no-data-container {\n  text-align: center;\n  padding: 2rem;\n}\n\n/* 表格样式 */\n.table-container {\n  overflow-x: auto;\n}\n\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.data-table th,\n.data-table td {\n  padding: 0.75rem 1rem;\n  text-align: left;\n  border-bottom: 1px solid #4B5563;\n}\n\n.data-table th {\n  background-color: transparent;\n  color: #d1d5db;\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.data-table td {\n  color: #e0e0e0;\n}\n\n.user-row:hover {\n  background-color: rgba(75, 85, 99, 0.5);\n  transition: background-color 0.2s;\n}\n\n.user-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.user-name {\n  font-weight: 500;\n  color: #ffffff;\n}\n\n.user-email {\n  font-size: 0.875rem;\n  color: #9ca3af;\n}\n\n.email-status {\n  font-size: 0.75rem;\n}\n\n.email-status.verified {\n  color: #10b981;\n}\n\n.email-status.unverified {\n  color: #ef4444;\n}\n\n.role-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.role-admin {\n  background-color: rgba(220, 38, 38, 0.2);\n  color: #fca5a5;\n}\n\n.role-user {\n  background-color: rgba(107, 114, 128, 0.2);\n  color: #d1d5db;\n}\n\n.vip-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.vip-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.vip-normal {\n  background-color: rgba(107, 114, 128, 0.2);\n  color: #d1d5db;\n}\n\n.vip-basic {\n  background-color: rgba(16, 185, 129, 0.2);\n  color: #6ee7b7;\n}\n\n.vip-premium {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fcd34d;\n}\n\n.vip-expire {\n  font-size: 0.75rem;\n  color: #9ca3af;\n}\n\n.quota-info {\n  font-size: 0.875rem;\n}\n\n.balance {\n  color: #10b981;\n  font-weight: 500;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 0.25rem;\n}\n\n/* 分页 */\n.pagination {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 0.5rem;\n  margin-top: 1.5rem;\n}\n\n.page-numbers {\n  display: inline-flex;\n  border-radius: 0.375rem;\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n}\n\n.page-btn {\n  background-color: #374151;\n  color: #d1d5db;\n  border: 1px solid #4b5563;\n  border-left: none;\n  padding: 0.5rem 0.75rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.page-btn:first-child {\n  border-left: 1px solid #4b5563;\n  border-top-left-radius: 0.375rem;\n  border-bottom-left-radius: 0.375rem;\n}\n\n.page-btn:last-child {\n  border-top-right-radius: 0.375rem;\n  border-bottom-right-radius: 0.375rem;\n}\n\n.page-btn:hover {\n  background-color: #4b5563;\n  color: #fff;\n}\n\n.page-btn.active {\n  background-color: #f59e0b;\n  color: #1f2937;\n  border-color: #f59e0b;\n}\n\n/* 响应式 */\n@media (max-width: 768px) {\n  .data-table th:nth-child(n+6),\n  .data-table td:nth-child(n+6) {\n    display: none;\n  }\n}\n\n@media (max-width: 640px) {\n  .data-table th:nth-child(n+4),\n  .data-table td:nth-child(n+4) {\n    display: none;\n  }\n}\n</style>\n"}