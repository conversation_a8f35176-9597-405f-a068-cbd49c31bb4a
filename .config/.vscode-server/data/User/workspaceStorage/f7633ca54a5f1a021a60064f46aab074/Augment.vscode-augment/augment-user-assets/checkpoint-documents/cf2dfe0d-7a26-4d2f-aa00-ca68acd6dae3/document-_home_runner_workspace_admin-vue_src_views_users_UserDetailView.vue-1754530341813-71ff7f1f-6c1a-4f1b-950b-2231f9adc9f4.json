{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/users/UserDetailView.vue"}, "originalCode": "<template>\n  <div class=\"user-detail\">\n\n\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">用户财务详情 - {{ user.username || '加载中...' }}</h1>\n      <div class=\"header-actions\">\n        <button class=\"btn-secondary\" @click=\"goBack\">\n          <i class=\"fas fa-arrow-left mr-2\"></i>返回用户列表\n        </button>\n      </div>\n    </div>\n\n\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <i class=\"fas fa-spinner fa-spin text-2xl text-gray-400\"></i>\n      <p class=\"text-gray-400 mt-2\">加载中...</p>\n    </div>\n\n    <div v-else-if=\"error\" class=\"error-container\">\n      <i class=\"fas fa-exclamation-triangle text-4xl text-red-400 mb-4\"></i>\n      <p class=\"text-red-400\">{{ error }}</p>\n      <button class=\"btn-primary mt-4\" @click=\"loadUserDetail\">重试</button>\n    </div>\n\n    <div v-else class=\"space-y-6\">\n\n\n      <!-- 用户基本信息 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">用户信息</h2>\n\n        <div class=\"user-info-grid\">\n          <div class=\"info-item\">\n            <label>用户名</label>\n            <p>{{ user.username || 'testuser' }}</p>\n          </div>\n          <div class=\"info-item\">\n            <label>邮箱</label>\n            <p>{{ user.email || '<EMAIL>' }}</p>\n          </div>\n          <div class=\"info-item\">\n            <label>注册时间</label>\n            <p>{{ user.created_at ? formatDateTime(user.created_at) : '2024/1/1 08:00:00' }}</p>\n          </div>\n          <div class=\"info-item\">\n            <label>VIP等级</label>\n            <div class=\"vip-info\">\n              <span v-if=\"user.finance?.vip_level\" :class=\"['vip-badge', getVipClass(user.finance.vip_level)]\">\n                {{ getVipStatus(user.finance.vip_level) }}\n              </span>\n              <span v-else class=\"vip-badge vip-free\">普通用户</span>\n\n              <!-- VIP到期时间 -->\n              <div v-if=\"user.finance?.vip_level > 0 && user.finance?.vip_expire_at\" class=\"vip-expire-info\">\n                <small class=\"text-gray-400\">\n                  到期时间: {{ formatDateTime(user.finance.vip_expire_at) }}\n                </small>\n              </div>\n\n              <!-- 配额使用情况 -->\n              <div v-if=\"user.finance\" class=\"quota-info mt-2\">\n                <div class=\"quota-item\">\n                  <small class=\"text-gray-400\">\n                    基础配额: {{ user.finance.basic_quota - user.finance.basic_quota_used || 0 }}/{{ user.finance.basic_quota || 0 }}\n                  </small>\n                </div>\n                <div class=\"quota-item\">\n                  <small class=\"text-gray-400\">\n                    高级配额: {{ user.finance.premium_quota - user.finance.premium_quota_used || 0 }}/{{ user.finance.premium_quota || 0 }}\n                  </small>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 财务统计概览 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">财务统计概览</h2>\n\n        <div class=\"stats-grid\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-green-400\">¥{{ ((financeStats.total_cash_used || 0) / 100).toFixed(2) }}</div>\n            <div class=\"stat-label\">现金支出总额</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-blue-400\">{{ financeStats.total_purchases || 0 }}</div>\n            <div class=\"stat-label\">购买次数</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-purple-400\">{{ financeStats.basic_purchases || 0 }}</div>\n            <div class=\"stat-label\">基础内容</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-pink-400\">{{ financeStats.premium_purchases || 0 }}</div>\n            <div class=\"stat-label\">高级内容</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-cyan-400\">{{ financeStats.total_quota_used || 0 }}个</div>\n            <div class=\"stat-label\">配额使用总量</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-orange-400\">{{ financeStats.total_points_used || 0 }}分</div>\n            <div class=\"stat-label\">积分使用总量</div>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 支付方式统计 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">支付方式统计</h2>\n\n        <div class=\"payment-stats-grid\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-green-400\">¥{{ ((financeStats.total_cash_used || 0) / 100).toFixed(2) }}</div>\n            <div class=\"stat-label\">现金使用总额</div>\n            <div class=\"stat-sublabel\">{{ financeStats.cash_payments || 0 }}次支付</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-cyan-400\">{{ financeStats.total_quota_used || 0 }}个</div>\n            <div class=\"stat-label\">配额使用总量</div>\n            <div class=\"stat-sublabel\">{{ financeStats.quota_payments || 0 }}次支付</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-orange-400\">{{ financeStats.total_points_used || 0 }}分</div>\n            <div class=\"stat-label\">积分使用总量</div>\n            <div class=\"stat-sublabel\">{{ financeStats.points_payments || 0 }}次支付</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 配额详细统计 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">配额使用详情</h2>\n\n        <div class=\"quota-details-grid\">\n          <div class=\"quota-detail-item\">\n            <div class=\"quota-type\">基础配额</div>\n            <div class=\"quota-progress\">\n              <div class=\"quota-bar\">\n                <div class=\"quota-used\" :style=\"{ width: getQuotaPercentage(financeStats.basic_quota_used, financeStats.basic_quota_total) + '%' }\"></div>\n              </div>\n              <div class=\"quota-text\">\n                {{ financeStats.basic_quota_used || 0 }} / {{ financeStats.basic_quota_total || 0 }}\n                (剩余: {{ financeStats.basic_quota_remaining || 0 }})\n              </div>\n            </div>\n          </div>\n\n          <div class=\"quota-detail-item\">\n            <div class=\"quota-type\">高级配额</div>\n            <div class=\"quota-progress\">\n              <div class=\"quota-bar\">\n                <div class=\"quota-used premium\" :style=\"{ width: getQuotaPercentage(financeStats.premium_quota_used, financeStats.premium_quota_total) + '%' }\"></div>\n              </div>\n              <div class=\"quota-text\">\n                {{ financeStats.premium_quota_used || 0 }} / {{ financeStats.premium_quota_total || 0 }}\n                (剩余: {{ financeStats.premium_quota_remaining || 0 }})\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 月度统计 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">月度财务统计</h2>\n\n        <div class=\"table-container\">\n          <table class=\"data-table\">\n            <thead>\n              <tr>\n                <th>月份</th>\n                <th>购买金额</th>\n                <th>消费金额</th>\n                <th>净收支</th>\n              </tr>\n            </thead>\n            <tbody>\n              <template v-if=\"monthlyData && monthlyData.length > 0\">\n                <tr v-for=\"(month, index) in monthlyData.slice(0, 12)\" :key=\"`month-${index}`\">\n                  <td>{{ month.month || '无' }}</td>\n                  <td class=\"text-green-400\">¥{{ ((month.purchases || 0) / 100).toFixed(2) }}</td>\n                  <td class=\"text-red-400\">¥{{ ((month.consumption || 0) / 100).toFixed(2) }}</td>\n                  <td :class=\"(month.net || 0) >= 0 ? 'text-green-400' : 'text-red-400'\">\n                    ¥{{ ((month.net || 0) / 100).toFixed(2) }}\n                  </td>\n                </tr>\n              </template>\n              <template v-else>\n                <tr>\n                  <td colspan=\"4\" class=\"text-center text-gray-400\">暂无月度数据</td>\n                </tr>\n              </template>\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n\n\n      <!-- 筛选器 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">财务记录筛选</h2>\n\n        <div class=\"filter-form\">\n          <div class=\"form-group\">\n            <label>记录类型</label>\n            <select v-model=\"filters.record_type\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部记录</option>\n              <option value=\"purchase\">购买记录</option>\n              <option value=\"consumption\">消费记录</option>\n              <option value=\"quota\">配额使用</option>\n              <option value=\"points\">积分记录</option>\n              <option value=\"subscription\">订阅记录</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label>时间范围</label>\n            <select v-model=\"filters.time_range\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部时间</option>\n              <option value=\"today\">今天</option>\n              <option value=\"week\">最近一周</option>\n              <option value=\"month\">最近一月</option>\n              <option value=\"quarter\">最近三月</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label>内容类型</label>\n            <select v-model=\"filters.content_type\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部类型</option>\n              <option value=\"basic\">基础内容</option>\n              <option value=\"premium\">高级内容</option>\n              <option value=\"vip\">VIP服务</option>\n              <option value=\"recharge\">充值</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 详细财务流水 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">详细财务流水（原始单据）- 共{{ transactions.length }}条记录</h2>\n\n\n\n        <div v-if=\"loading\" class=\"loading-container\">\n          <i class=\"fas fa-spinner fa-spin text-2xl text-gray-400\"></i>\n          <p class=\"text-gray-400 mt-2\">加载中...</p>\n        </div>\n\n        <div v-else-if=\"transactions.length === 0\" class=\"no-data-container\">\n          <i class=\"fas fa-receipt text-4xl text-gray-600 mb-4\"></i>\n          <p class=\"text-gray-400\">暂无财务记录</p>\n        </div>\n\n        <div v-else class=\"table-container\">\n          <table class=\"data-table\">\n            <thead>\n              <tr>\n                <th>时间</th>\n                <th>类型</th>\n                <th>描述</th>\n                <th>支付详情</th>\n                <th>内容类型</th>\n                <th>现金变动</th>\n                <th>配额变动</th>\n                <th>积分变动</th>\n                <th>现金余额</th>\n                <th>配额余额</th>\n                <th>积分余额</th>\n                <th>交易ID</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr v-for=\"transaction in transactions\" :key=\"`${transaction.type}-${transaction.id}`\">\n                <td class=\"text-sm\">{{ formatDateTime(transaction.date) }}</td>\n                <td>\n                  <span :class=\"['type-badge', `type-${transaction.type}`]\">\n                    {{ getTransactionTypeText(transaction.type) }}\n                  </span>\n                </td>\n                <td class=\"text-sm\">{{ transaction.description }}</td>\n                <td class=\"text-sm\">\n                  <span v-if=\"transaction.payment_details\" class=\"payment-details\">\n                    {{ transaction.payment_details }}\n                  </span>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <td>\n                  <span v-if=\"transaction.content_type\" :class=\"['content-badge', `content-${transaction.content_type}`]\">\n                    {{ getContentTypeText(transaction.content_type) }}\n                  </span>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <td :class=\"getAmountClass(transaction.amount)\">\n                  <span v-if=\"transaction.amount !== 0\">\n                    {{ transaction.amount > 0 ? '+' : '' }}¥{{ (Math.abs(transaction.amount) / 100).toFixed(2) }}\n                  </span>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <td :class=\"getQuotaChangeClass(transaction.quota_change)\">\n                  <span v-if=\"transaction.quota_change\">\n                    {{ transaction.quota_change > 0 ? '+' : '' }}{{ transaction.quota_change }}\n                    <span class=\"text-xs\">({{ transaction.quota_type }})</span>\n                  </span>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <td :class=\"getPointsChangeClass(transaction.points_change)\">\n                  <span v-if=\"transaction.points_change\">\n                    {{ transaction.points_change > 0 ? '+' : '' }}{{ transaction.points_change }}\n                  </span>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <!-- 现金余额 -->\n                <td class=\"text-gray-300\">\n                  <div v-if=\"transaction.balance_before !== null || transaction.balance_after !== null\">\n                    <div class=\"text-xs\">前: ¥{{ ((transaction.balance_before || 0) / 100).toFixed(2) }}</div>\n                    <div class=\"text-xs\">后: ¥{{ ((transaction.balance_after || 0) / 100).toFixed(2) }}</div>\n                  </div>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n\n                <!-- 配额余额 -->\n                <td class=\"text-cyan-300\">\n                  <div v-if=\"transaction.quota_before !== null || transaction.quota_after !== null\">\n                    <div class=\"text-xs\">前: {{ transaction.quota_before || 0 }}</div>\n                    <div class=\"text-xs\">后: {{ transaction.quota_after || 0 }}</div>\n                  </div>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n\n                <!-- 积分余额 -->\n                <td class=\"text-orange-300\">\n                  <div v-if=\"transaction.points_before !== null || transaction.points_after !== null\">\n                    <div class=\"text-xs\">前: {{ transaction.points_before || 0 }}</div>\n                    <div class=\"text-xs\">后: {{ transaction.points_after || 0 }}</div>\n                  </div>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <td class=\"text-xs text-gray-400\">{{ transaction.transaction_id || '-' }}</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n\n        <!-- 分页 -->\n        <div v-if=\"transactionPagination.pages > 1\" class=\"pagination\">\n          <button\n            class=\"btn-secondary\"\n            :disabled=\"!transactionPagination.has_prev\"\n            @click=\"handleTransactionPageChange(transactionPagination.prev_num)\"\n          >\n            上一页\n          </button>\n          <div class=\"page-numbers\">\n            <button\n              v-for=\"page in getTransactionPageNumbers()\"\n              :key=\"page\"\n              :class=\"['page-btn', page === transactionPagination.page ? 'active' : '']\"\n              @click=\"handleTransactionPageChange(page)\"\n            >\n              {{ page }}\n            </button>\n          </div>\n          <button\n            class=\"btn-secondary\"\n            :disabled=\"!transactionPagination.has_next\"\n            @click=\"handleTransactionPageChange(transactionPagination.next_num)\"\n          >\n            下一页\n          </button>\n        </div>\n      </div>\n\n      <!-- 筛选器 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">财务记录筛选</h2>\n\n        <div class=\"filter-form\">\n          <div class=\"form-group\">\n            <label>记录类型</label>\n            <select v-model=\"filters.record_type\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部记录</option>\n              <option value=\"purchase\">购买记录</option>\n              <option value=\"consumption\">消费记录</option>\n              <option value=\"quota\">配额使用</option>\n              <option value=\"points\">积分记录</option>\n              <option value=\"subscription\">订阅记录</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label>时间范围</label>\n            <select v-model=\"filters.time_range\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部时间</option>\n              <option value=\"today\">今天</option>\n              <option value=\"week\">最近一周</option>\n              <option value=\"month\">最近一月</option>\n              <option value=\"quarter\">最近三月</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label>内容类型</label>\n            <select v-model=\"filters.content_type\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部类型</option>\n              <option value=\"basic\">基础内容</option>\n              <option value=\"premium\">高级内容</option>\n              <option value=\"vip\">VIP服务</option>\n              <option value=\"recharge\">充值</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { usersApi } from '@/api/users'\n\nconst route = useRoute()\nconst router = useRouter()\n\n// 响应式数据\nconst loading = ref(true)\nconst error = ref('')\nconst user = ref({})\nconst financeStats = reactive({\n  total_spent: 0,\n  total_purchases: 0,\n  basic_purchases: 0,\n  premium_purchases: 0,\n  total_consumption: 0,\n  current_balance: 0,\n  cash_payments: 0,\n  quota_payments: 0,\n  points_payments: 0\n})\nconst monthlyData = ref([])\nconst transactions = ref([])\n\n// 调试信息\nconst debugInfo = reactive({\n  loadTransactionsCalled: false,\n  loadUserDetailCalled: false,\n  mountedCalled: false,\n  mockDataLength: 0\n})\n\nconst filters = reactive({\n  record_type: '',\n  time_range: '',\n  content_type: '',\n  page: 1,\n  per_page: 20\n})\n\nconst transactionPagination = reactive({\n  page: 1,\n  pages: 1,\n  total: 0,\n  per_page: 20,\n  has_prev: false,\n  has_next: false,\n  prev_num: null,\n  next_num: null\n})\n\n// 方法\nconst formatDateTime = (date: string) => {\n  if (!date) return '无'\n  try {\n    const dateObj = new Date(date)\n    if (isNaN(dateObj.getTime())) return '无效日期'\n    return dateObj.toLocaleString('zh-CN')\n  } catch (error) {\n    console.error('formatDateTime error:', error)\n    return '格式错误'\n  }\n}\n\nconst getVipStatus = (vipLevel: number) => {\n  try {\n    if (!vipLevel || vipLevel === 0) return '普通用户'\n    if (vipLevel === 2) return 'VIP Pro'\n    if (vipLevel === 1) return 'VIP'\n    return '普通用户'\n  } catch (error) {\n    console.error('getVipStatus error:', error)\n    return '未知'\n  }\n}\n\n// 计算配额使用百分比\nconst getQuotaPercentage = (used: number, total: number) => {\n  if (!total || total === 0) return 0\n  return Math.min((used / total) * 100, 100)\n}\n\nconst getVipClass = (vipLevel: number) => {\n  try {\n    if (!vipLevel || vipLevel === 0) return 'vip-free'\n    if (vipLevel === 2) return 'vip-premium'\n    return 'vip-basic'\n  } catch (error) {\n    console.error('getVipClass error:', error)\n    return 'vip-free'\n  }\n}\n\nconst getTransactionTypeText = (type: string) => {\n  const typeMap = {\n    purchase: '购买',\n    consumption: '消费',\n    quota_consumption: '配额消费',\n    quota_purchase: '配额购买',\n    points_earned: '积分获得',\n    points_consumption: '积分消费',\n    points_used: '积分消费',\n    subscription: '订阅',\n    recharge: '充值',\n    refund: '退款',\n    quota_reset: '配额重置',\n    vip_upgrade: 'VIP升级',\n    vip_expire: 'VIP到期'\n  }\n  return typeMap[type] || type\n}\n\nconst getContentTypeText = (type: string) => {\n  const typeMap = {\n    basic: '基础内容',\n    premium: '高级内容',\n    vip: 'VIP服务',\n    recharge: '充值',\n    subscription: '订阅服务',\n    quota_pack: '配额包'\n  }\n  return typeMap[type] || type\n}\n\nconst getPaymentMethodText = (method: string) => {\n  const methodMap = {\n    quota: '配额',\n    points: '积分',\n    alipay: '支付宝',\n    wechat: '微信',\n    mock: '模拟支付',\n    stripe: 'Stripe',\n    balance: '余额'\n  }\n  return methodMap[method] || method\n}\n\nconst getCostTypeText = (type: string) => {\n  const typeMap = {\n    quota: '配额',\n    points: '积分',\n    balance: '余额'\n  }\n  return typeMap[type] || type\n}\n\nconst getAmountClass = (amount: number) => {\n  if (amount > 0) return 'text-green-400'\n  if (amount < 0) return 'text-red-400'\n  return 'text-gray-400'\n}\n\nconst getQuotaChangeClass = (change: number) => {\n  if (change > 0) return 'text-green-400'\n  if (change < 0) return 'text-red-400'\n  return 'text-gray-400'\n}\n\nconst getPointsChangeClass = (change: number) => {\n  if (change > 0) return 'text-green-400'\n  if (change < 0) return 'text-red-400'\n  return 'text-gray-400'\n}\n\nconst getTransactionPageNumbers = () => {\n  const pages = []\n  const current = transactionPagination.page\n  const total = transactionPagination.pages\n\n  const start = Math.max(1, current - 2)\n  const end = Math.min(total, current + 2)\n\n  for (let i = start; i <= end; i++) {\n    pages.push(i)\n  }\n\n  return pages\n}\n\nconst loadUserDetail = async () => {\n  loading.value = true\n  error.value = ''\n\n  try {\n    const userId = route.params.id\n\n    // 真实API调用\n    try {\n      console.log('🔍 开始调用用户财务详情API:', `/admin/api/users/${userId}/finance`)\n\n      const response = await fetch(`/admin/api/users/${userId}/finance`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include'  // 包含认证信息\n      })\n\n      console.log('📡 API响应状态:', response.status, response.statusText)\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      console.log('📊 API返回数据:', data)\n\n      if (data.success) {\n        user.value = data.user\n        Object.assign(financeStats, data.finance_stats)\n        monthlyData.value = data.monthly_data\n\n        console.log('✅ 用户财务数据加载成功')\n        // 单独加载交易记录\n        await loadTransactions()\n        return\n      } else {\n        throw new Error(data.error || '获取用户财务详情失败')\n      }\n    } catch (apiError) {\n      console.error('❌ API调用失败:', apiError)\n      error.value = `API调用失败: ${apiError.message}`\n\n      // 不降级到模拟数据，显示错误信息\n      return\n    }\n\n  } catch (err) {\n    error.value = '加载用户详情失败: ' + err.message\n  } finally {\n    loading.value = false\n    console.log('🐛 loadUserDetail 完成，loading:', loading.value, 'error:', error.value)\n  }\n}\n\nconst loadTransactions = async () => {\n  try {\n    debugInfo.loadTransactionsCalled = true\n    console.log('🐛 loadTransactions 被调用了!')\n\n    const userId = route.params.id\n\n    // 构建查询参数\n    const params = new URLSearchParams({\n      page: filters.page.toString(),\n      per_page: filters.per_page.toString(),\n      record_type: filters.record_type || '',\n      time_range: filters.time_range || '',\n      content_type: filters.content_type || ''\n    })\n\n    console.log('🐛 查询参数:', params.toString())\n\n    // 真实API调用\n    try {\n      const response = await fetch(`/admin/api/users/${userId}/transactions?${params.toString()}`)\n      const data = await response.json()\n\n      if (data.success) {\n        transactions.value = data.data.transactions || []\n        Object.assign(transactionPagination, {\n          page: data.data.pagination.page || 1,\n          pages: data.data.pagination.pages || 1,\n          total: data.data.pagination.total || 0,\n          per_page: data.data.pagination.per_page || 20,\n          has_prev: data.data.pagination.has_prev || false,\n          has_next: data.data.pagination.has_next || false,\n          prev_num: data.data.pagination.prev_num || null,\n          next_num: data.data.pagination.next_num || null\n        })\n        return\n      } else {\n        throw new Error(data.error || '获取交易记录失败')\n      }\n    } catch (apiError) {\n      console.warn('API调用失败，使用模拟数据:', apiError)\n    }\n\n    // 模拟详细的交易数据 - 包含所有类型的交易记录\n    const mockTransactions = [\n      // 1. 购买记录 - 充值\n      {\n        id: 1,\n        type: 'purchase',\n        date: '2024-01-01T10:00:00Z',\n        description: '支付宝充值',\n        article_title: null,\n        content_type: 'recharge',\n        payment_method: 'alipay',\n        amount: 20000, // +200元\n        quota_change: 0,\n        quota_type: null,\n        points_change: 200, // 充值送积分\n        balance_before: 0,\n        balance_after: 20000,\n        transaction_id: 'ALI_20240101_001'\n      },\n      // 购买记录 - VIP\n      {\n        id: 2,\n        type: 'purchase',\n        date: '2024-01-02T09:15:00Z',\n        description: '购买VIP1个月',\n        article_title: null,\n        content_type: 'vip',\n        payment_method: 'balance',\n        amount: -12800, // -128元\n        quota_change: 0,\n        quota_type: null,\n        points_change: 0,\n        balance_before: 20000,\n        balance_after: 7200,\n        transaction_id: 'VIP_20240102_001'\n      },\n      // 购买记录 - 文章\n      {\n        id: 3,\n        type: 'purchase',\n        date: '2024-01-03T14:20:00Z',\n        description: '购买文章: 塔罗牌高级解读技巧',\n        article_title: '塔罗牌高级解读技巧',\n        content_type: 'premium',\n        payment_method: 'balance',\n        amount: -1000, // -10元\n        quota_change: 0,\n        quota_type: null,\n        points_change: 10, // 购买送积分\n        balance_before: 7200,\n        balance_after: 6200,\n        transaction_id: 'ART_20240103_001'\n      },\n      // 消费记录 - 配额消费\n      {\n        id: 4,\n        type: 'consumption',\n        date: '2024-01-04T16:45:00Z',\n        description: '消费: 爱情塔罗牌阵详解',\n        article_title: '爱情塔罗牌阵详解',\n        content_type: 'premium',\n        payment_method: null,\n        cost_type: 'quota',\n        amount: 0, // 配额消费不影响余额\n        quota_change: -1,\n        quota_type: 'premium',\n        points_change: 0,\n        balance_before: 6200,\n        balance_after: 6200,\n        transaction_id: null\n      },\n      // 消费记录 - 余额消费\n      {\n        id: 5,\n        type: 'consumption',\n        date: '2024-01-05T11:30:00Z',\n        description: '消费: 塔罗牌入门指南',\n        article_title: '塔罗牌入门指南',\n        content_type: 'basic',\n        payment_method: null,\n        cost_type: 'balance',\n        amount: -500, // -5元\n        quota_change: 0,\n        quota_type: null,\n        points_change: 0,\n        balance_before: 6200,\n        balance_after: 5700,\n        transaction_id: null\n      },\n      // 消费记录 - 积分消费\n      {\n        id: 6,\n        type: 'consumption',\n        date: '2024-01-06T13:15:00Z',\n        description: '消费: 塔罗牌牌意解析',\n        article_title: '塔罗牌牌意解析',\n        content_type: 'basic',\n        payment_method: null,\n        cost_type: 'points',\n        amount: 0, // 积分消费不影响余额\n        quota_change: 0,\n        quota_type: null,\n        points_change: -50,\n        balance_before: 5700,\n        balance_after: 5700,\n        transaction_id: null\n      },\n      // 积分记录 - 签到奖励\n      {\n        id: 7,\n        type: 'points_earned',\n        date: '2024-01-07T08:00:00Z',\n        description: '每日签到奖励',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: 10,\n        balance_before: 5700,\n        balance_after: 5700,\n        transaction_id: null\n      },\n      // 配额记录 - 配额重置\n      {\n        id: 8,\n        type: 'quota_reset',\n        date: '2024-01-08T00:00:00Z',\n        description: 'VIP配额月度重置',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 10, // 重置配额\n        quota_type: 'basic',\n        points_change: 0,\n        balance_before: 5700,\n        balance_after: 5700,\n        transaction_id: null\n      },\n      // 退款记录\n      {\n        id: 9,\n        type: 'refund',\n        date: '2024-01-09T15:30:00Z',\n        description: '文章购买退款: 塔罗牌高级解读技巧',\n        article_title: '塔罗牌高级解读技巧',\n        content_type: 'premium',\n        payment_method: 'balance',\n        amount: 1000, // +10元退款\n        quota_change: 0,\n        quota_type: null,\n        points_change: -10, // 扣回赠送积分\n        balance_before: 5700,\n        balance_after: 6700,\n        transaction_id: 'REF_20240109_001'\n      },\n      // 10. 订阅记录\n      {\n        id: 10,\n        type: 'subscription',\n        date: '2024-01-10T12:00:00Z',\n        description: '订阅塔罗师Alice',\n        article_title: null,\n        content_type: 'subscription',\n        payment_method: 'balance',\n        amount: -2000, // -20元\n        quota_change: 0,\n        quota_type: null,\n        points_change: 20,\n        balance_before: 6700,\n        balance_after: 4700,\n        transaction_id: 'SUB_20240110_001'\n      },\n      // 11. 积分消费 - 兑换配额\n      {\n        id: 11,\n        type: 'points_consumption',\n        date: '2024-01-11T09:30:00Z',\n        description: '积分兑换高级配额',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: 'points',\n        amount: 0,\n        quota_change: 5,\n        quota_type: 'premium',\n        points_change: -500, // 消费500积分\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 12. 积分获得 - 每日签到\n      {\n        id: 12,\n        type: 'points_earned',\n        date: '2024-01-12T08:00:00Z',\n        description: '每日签到奖励',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: 10,\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 13. 积分获得 - 推荐奖励\n      {\n        id: 13,\n        type: 'points_earned',\n        date: '2024-01-13T14:20:00Z',\n        description: '推荐新用户奖励',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: 100, // 推荐奖励100积分\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 14. 配额消费 - 查看高级内容\n      {\n        id: 14,\n        type: 'quota_consumption',\n        date: '2024-01-14T16:45:00Z',\n        description: '查看高级塔罗解读',\n        article_title: '深度塔罗牌解读：爱情运势',\n        content_type: 'premium',\n        payment_method: null,\n        cost_type: 'quota',\n        amount: 0,\n        quota_change: -1,\n        quota_type: 'premium',\n        points_change: 0,\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 15. 配额获得 - VIP月度重置\n      {\n        id: 15,\n        type: 'quota_reset',\n        date: '2024-02-01T00:00:00Z',\n        description: 'VIP配额月度重置',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 20, // 重置20个高级配额\n        quota_type: 'premium',\n        points_change: 0,\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 16. 积分消费 - 购买文章\n      {\n        id: 16,\n        type: 'points_consumption',\n        date: '2024-02-02T11:15:00Z',\n        description: '积分购买文章',\n        article_title: '塔罗牌占卜技巧大全',\n        content_type: 'basic',\n        payment_method: null,\n        cost_type: 'points',\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: -80, // 消费80积分\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 17. 积分获得 - 评论奖励\n      {\n        id: 17,\n        type: 'points_earned',\n        date: '2024-02-03T15:30:00Z',\n        description: '优质评论奖励',\n        article_title: '塔罗牌占卜技巧大全',\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: 20, // 评论奖励20积分\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 18. 配额消费 - 基础内容\n      {\n        id: 18,\n        type: 'quota_consumption',\n        date: '2024-02-04T10:20:00Z',\n        description: '查看基础塔罗内容',\n        article_title: '塔罗牌基础知识入门',\n        content_type: 'basic',\n        payment_method: null,\n        cost_type: 'quota',\n        amount: 0,\n        quota_change: -1,\n        quota_type: 'basic',\n        points_change: 0,\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 19. 积分获得 - 活动奖励\n      {\n        id: 19,\n        type: 'points_earned',\n        date: '2024-02-05T18:00:00Z',\n        description: '春节活动奖励',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: 200, // 活动奖励200积分\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: 'EVENT_CNY_2024'\n      },\n      // 20. 配额购买 - 单独购买配额\n      {\n        id: 20,\n        type: 'quota_purchase',\n        date: '2024-02-06T13:45:00Z',\n        description: '购买高级配额包',\n        article_title: null,\n        content_type: 'quota_pack',\n        payment_method: 'balance',\n        amount: -1500, // -15元\n        quota_change: 10,\n        quota_type: 'premium',\n        points_change: 15, // 购买送积分\n        balance_before: 4700,\n        balance_after: 3200,\n        transaction_id: 'QUOTA_20240206_001'\n      }\n    ]\n\n    console.log('🐛 模拟数据长度:', mockTransactions.length)\n    debugInfo.mockDataLength = mockTransactions.length\n\n    // 根据筛选条件过滤数据\n    let filteredTransactions = mockTransactions\n\n    if (filters.record_type) {\n      filteredTransactions = filteredTransactions.filter(t => t.type === filters.record_type)\n    }\n\n    if (filters.content_type) {\n      filteredTransactions = filteredTransactions.filter(t => t.content_type === filters.content_type)\n    }\n\n    console.log('🐛 过滤后数据长度:', filteredTransactions.length)\n\n    // 模拟分页\n    const total = filteredTransactions.length\n    const pages = Math.ceil(total / filters.per_page)\n    const start = (filters.page - 1) * filters.per_page\n    const end = start + filters.per_page\n    const paginatedTransactions = filteredTransactions.slice(start, end)\n\n    console.log('🐛 分页后数据长度:', paginatedTransactions.length)\n    console.log('🐛 第一条记录:', paginatedTransactions[0])\n\n    transactions.value = paginatedTransactions\n    console.log('🐛 transactions.value 设置完成，长度:', transactions.value.length)\n    Object.assign(transactionPagination, {\n      page: filters.page,\n      pages: pages,\n      total: total,\n      per_page: filters.per_page,\n      has_prev: filters.page > 1,\n      has_next: filters.page < pages,\n      prev_num: filters.page > 1 ? filters.page - 1 : null,\n      next_num: filters.page < pages ? filters.page + 1 : null\n    })\n\n  } catch (err) {\n    console.error('加载交易记录失败:', err)\n  }\n}\n\nconst handleTransactionPageChange = (page: number) => {\n  if (page && page !== filters.page) {\n    filters.page = page\n    loadTransactions()\n  }\n}\n\nconst goBack = () => {\n  router.push('/users')\n}\n\nconst getCurrentTime = () => {\n  return new Date().toLocaleString()\n}\n\n\n\n// 初始化\nonMounted(() => {\n  loadUserDetail()\n  loadTransactions()\n})\n</script>\n\n<style scoped>\n/* 深色主题样式 */\n.user-detail {\n  color: #e0e0e0;\n  padding: 1.5rem;\n  background-color: #111827;\n  min-height: 100vh;\n}\n\n/* 页面标题 */\n.page-header {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1.5rem;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .page-header {\n    flex-direction: row;\n    align-items: center;\n    gap: 0;\n  }\n}\n\n.page-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #ffffff;\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 0.75rem;\n}\n\n/* 按钮样式 */\n.btn-primary {\n  background-color: #f59e0b;\n  color: #1f2937;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-primary:hover {\n  background-color: #d97706;\n}\n\n.btn-secondary {\n  background-color: #6b7280;\n  color: #d1d5db;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-secondary:hover {\n  background-color: #4b5563;\n  color: #fff;\n}\n\n/* 加载和错误状态 */\n.loading-container, .error-container, .no-data-container {\n  text-align: center;\n  padding: 2rem;\n}\n\n/* 信息卡片 */\n.info-card {\n  background-color: #374151;\n  border: 1px solid #4B5563;\n  border-radius: 0.5rem;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.card-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #f59e0b;\n  margin: 0 0 1rem 0;\n}\n\n/* 用户信息网格 */\n.user-info-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .user-info-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.info-item label {\n  color: #d1d5db;\n  font-size: 0.875rem;\n  margin-bottom: 0.25rem;\n}\n\n.info-item p {\n  color: #ffffff;\n  font-weight: 500;\n  margin: 0;\n}\n\n/* VIP徽章 */\n.vip-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 0.25rem;\n  font-size: 0.75rem;\n  font-weight: 500;\n  display: inline-block;\n}\n\n.vip-free {\n  background-color: rgba(107, 114, 128, 0.2);\n  color: #d1d5db;\n}\n\n.vip-basic {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fcd34d;\n}\n\n.vip-premium {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fcd34d;\n}\n\n/* 统计网格 */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .stats-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (min-width: 1024px) {\n  .stats-grid {\n    grid-template-columns: repeat(6, 1fr);\n  }\n}\n\n.payment-stats-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .payment-stats-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 0.25rem;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  color: #9ca3af;\n}\n\n/* 表格样式 */\n.table-container {\n  overflow-x: auto;\n}\n\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.data-table th,\n.data-table td {\n  padding: 0.75rem 1rem;\n  text-align: left;\n  border-bottom: 1px solid #4B5563;\n}\n\n.data-table th {\n  background-color: transparent;\n  color: #d1d5db;\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.data-table td {\n  color: #e0e0e0;\n}\n\n/* 徽章样式 */\n.type-badge, .content-badge, .payment-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 0.25rem;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n/* 交易类型徽章 */\n.type-purchase {\n  background-color: rgba(16, 185, 129, 0.2);\n  color: #6ee7b7;\n}\n\n.type-consumption {\n  background-color: rgba(220, 38, 38, 0.2);\n  color: #fca5a5;\n}\n\n.type-points_earned {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fcd34d;\n}\n\n.type-points_used,\n.type-points_consumption {\n  background-color: rgba(251, 146, 60, 0.2);\n  color: #fdba74;\n}\n\n.type-quota_consumption {\n  background-color: rgba(239, 68, 68, 0.2);\n  color: #f87171;\n}\n\n.type-quota_purchase {\n  background-color: rgba(34, 197, 94, 0.2);\n  color: #4ade80;\n}\n\n.type-subscription {\n  background-color: rgba(139, 92, 246, 0.2);\n  color: #c4b5fd;\n}\n\n.type-refund {\n  background-color: rgba(34, 197, 94, 0.2);\n  color: #86efac;\n}\n\n.type-quota_reset {\n  background-color: rgba(59, 130, 246, 0.2);\n  color: #93c5fd;\n}\n\n.type-vip_upgrade {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fcd34d;\n}\n\n.type-vip_expire {\n  background-color: rgba(107, 114, 128, 0.2);\n  color: #d1d5db;\n}\n\n.content-basic {\n  background-color: rgba(59, 130, 246, 0.2);\n  color: #93c5fd;\n}\n\n.content-premium {\n  background-color: rgba(139, 92, 246, 0.2);\n  color: #c4b5fd;\n}\n\n.payment-quota {\n  background-color: rgba(6, 182, 212, 0.2);\n  color: #67e8f9;\n}\n\n.payment-points {\n  background-color: rgba(251, 146, 60, 0.2);\n  color: #fdba74;\n}\n\n.payment-balance {\n  background-color: rgba(34, 197, 94, 0.2);\n  color: #86efac;\n}\n\n.payment-alipay {\n  background-color: rgba(59, 130, 246, 0.2);\n  color: #93c5fd;\n}\n\n.payment-wechat {\n  background-color: rgba(34, 197, 94, 0.2);\n  color: #86efac;\n}\n\n.payment-mock, .payment-stripe {\n  background-color: rgba(139, 92, 246, 0.2);\n  color: #c4b5fd;\n}\n\n.payment-other {\n  background-color: rgba(107, 114, 128, 0.2);\n  color: #d1d5db;\n}\n\n/* 筛选表单 */\n.filter-form {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .filter-form {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group label {\n  color: #d1d5db;\n  font-size: 0.875rem;\n  margin-bottom: 0.5rem;\n}\n\n.form-select {\n  background-color: #1f2937;\n  border: 1px solid #374151;\n  border-radius: 0.5rem;\n  padding: 0.5rem 0.75rem;\n  color: #e0e0e0;\n  font-size: 0.875rem;\n}\n\n.form-select:focus {\n  outline: none;\n  border-color: #f59e0b;\n  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);\n}\n\n/* 文章链接 */\n.article-link {\n  color: #60a5fa;\n  text-decoration: underline;\n  cursor: pointer;\n}\n\n.article-link:hover {\n  color: #93c5fd;\n}\n\n.loading-container,\n.no-data-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n/* VIP信息样式 */\n.vip-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.vip-expire-info {\n  margin-top: 0.25rem;\n}\n\n.quota-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.quota-item {\n  font-size: 0.75rem;\n}\n\n/* 配额详细统计样式 */\n.quota-details-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n}\n\n.quota-detail-item {\n  background: rgba(55, 65, 81, 0.5);\n  padding: 1rem;\n  border-radius: 0.5rem;\n  border: 1px solid #4B5563;\n}\n\n.quota-type {\n  font-weight: 600;\n  color: #E5E7EB;\n  margin-bottom: 0.5rem;\n}\n\n.quota-progress {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.quota-bar {\n  width: 100%;\n  height: 8px;\n  background: #374151;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.quota-used {\n  height: 100%;\n  background: #06B6D4;\n  transition: width 0.3s ease;\n}\n\n.quota-used.premium {\n  background: #F59E0B;\n}\n\n.quota-text {\n  font-size: 0.875rem;\n  color: #9CA3AF;\n}\n\n/* 支付方式统计样式 */\n.stat-sublabel {\n  font-size: 0.75rem;\n  color: #6B7280;\n  margin-top: 0.25rem;\n}\n\n.payment-details {\n  font-size: 0.875rem;\n  color: #E5E7EB;\n}\n\n/* 间距 */\n.space-y-6 > * + * {\n  margin-top: 1.5rem;\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"user-detail\">\n\n\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">用户财务详情 - {{ user.username || '加载中...' }}</h1>\n      <div class=\"header-actions\">\n        <button class=\"btn-secondary\" @click=\"goBack\">\n          <i class=\"fas fa-arrow-left mr-2\"></i>返回用户列表\n        </button>\n      </div>\n    </div>\n\n\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <i class=\"fas fa-spinner fa-spin text-2xl text-gray-400\"></i>\n      <p class=\"text-gray-400 mt-2\">加载中...</p>\n    </div>\n\n    <div v-else-if=\"error\" class=\"error-container\">\n      <i class=\"fas fa-exclamation-triangle text-4xl text-red-400 mb-4\"></i>\n      <p class=\"text-red-400\">{{ error }}</p>\n      <button class=\"btn-primary mt-4\" @click=\"loadUserDetail\">重试</button>\n    </div>\n\n    <div v-else class=\"space-y-6\">\n\n\n      <!-- 用户基本信息 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">用户信息</h2>\n\n        <div class=\"user-info-grid\">\n          <div class=\"info-item\">\n            <label>用户名</label>\n            <p>{{ user.username || 'testuser' }}</p>\n          </div>\n          <div class=\"info-item\">\n            <label>邮箱</label>\n            <p>{{ user.email || '<EMAIL>' }}</p>\n          </div>\n          <div class=\"info-item\">\n            <label>注册时间</label>\n            <p>{{ user.created_at ? formatDateTime(user.created_at) : '2024/1/1 08:00:00' }}</p>\n          </div>\n          <div class=\"info-item\">\n            <label>VIP等级</label>\n            <div class=\"vip-info\">\n              <span v-if=\"user.finance?.vip_level\" :class=\"['vip-badge', getVipClass(user.finance.vip_level)]\">\n                {{ getVipStatus(user.finance.vip_level) }}\n              </span>\n              <span v-else class=\"vip-badge vip-free\">普通用户</span>\n\n              <!-- VIP到期时间 -->\n              <div v-if=\"user.finance?.vip_level > 0 && user.finance?.vip_expire_at\" class=\"vip-expire-info\">\n                <small class=\"text-gray-400\">\n                  到期时间: {{ formatDateTime(user.finance.vip_expire_at) }}\n                </small>\n              </div>\n\n              <!-- 配额使用情况 -->\n              <div v-if=\"user.finance\" class=\"quota-info mt-2\">\n                <div class=\"quota-item\">\n                  <small class=\"text-gray-400\">\n                    基础配额: {{ user.finance.basic_quota - user.finance.basic_quota_used || 0 }}/{{ user.finance.basic_quota || 0 }}\n                  </small>\n                </div>\n                <div class=\"quota-item\">\n                  <small class=\"text-gray-400\">\n                    高级配额: {{ user.finance.premium_quota - user.finance.premium_quota_used || 0 }}/{{ user.finance.premium_quota || 0 }}\n                  </small>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 财务统计概览 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">财务统计概览</h2>\n\n        <div class=\"stats-grid\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-green-400\">¥{{ ((financeStats.total_cash_used || 0) / 100).toFixed(2) }}</div>\n            <div class=\"stat-label\">现金支出总额</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-blue-400\">{{ financeStats.total_purchases || 0 }}</div>\n            <div class=\"stat-label\">购买次数</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-purple-400\">{{ financeStats.basic_purchases || 0 }}</div>\n            <div class=\"stat-label\">基础内容</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-pink-400\">{{ financeStats.premium_purchases || 0 }}</div>\n            <div class=\"stat-label\">高级内容</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-cyan-400\">{{ financeStats.total_quota_used || 0 }}个</div>\n            <div class=\"stat-label\">配额使用总量</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-orange-400\">{{ financeStats.total_points_used || 0 }}分</div>\n            <div class=\"stat-label\">积分使用总量</div>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 支付方式统计 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">支付方式统计</h2>\n\n        <div class=\"payment-stats-grid\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-green-400\">¥{{ ((financeStats.total_cash_used || 0) / 100).toFixed(2) }}</div>\n            <div class=\"stat-label\">现金使用总额</div>\n            <div class=\"stat-sublabel\">{{ financeStats.cash_payments || 0 }}次支付</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-cyan-400\">{{ financeStats.total_quota_used || 0 }}个</div>\n            <div class=\"stat-label\">配额使用总量</div>\n            <div class=\"stat-sublabel\">{{ financeStats.quota_payments || 0 }}次支付</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value text-orange-400\">{{ financeStats.total_points_used || 0 }}分</div>\n            <div class=\"stat-label\">积分使用总量</div>\n            <div class=\"stat-sublabel\">{{ financeStats.points_payments || 0 }}次支付</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 配额详细统计 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">配额使用详情</h2>\n\n        <div class=\"quota-details-grid\">\n          <div class=\"quota-detail-item\">\n            <div class=\"quota-type\">基础配额</div>\n            <div class=\"quota-progress\">\n              <div class=\"quota-bar\">\n                <div class=\"quota-used\" :style=\"{ width: getQuotaPercentage(financeStats.basic_quota_used, financeStats.basic_quota_total) + '%' }\"></div>\n              </div>\n              <div class=\"quota-text\">\n                {{ financeStats.basic_quota_used || 0 }} / {{ financeStats.basic_quota_total || 0 }}\n                (剩余: {{ financeStats.basic_quota_remaining || 0 }})\n              </div>\n            </div>\n          </div>\n\n          <div class=\"quota-detail-item\">\n            <div class=\"quota-type\">高级配额</div>\n            <div class=\"quota-progress\">\n              <div class=\"quota-bar\">\n                <div class=\"quota-used premium\" :style=\"{ width: getQuotaPercentage(financeStats.premium_quota_used, financeStats.premium_quota_total) + '%' }\"></div>\n              </div>\n              <div class=\"quota-text\">\n                {{ financeStats.premium_quota_used || 0 }} / {{ financeStats.premium_quota_total || 0 }}\n                (剩余: {{ financeStats.premium_quota_remaining || 0 }})\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 月度统计 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">月度财务统计</h2>\n\n        <div class=\"table-container\">\n          <table class=\"data-table\">\n            <thead>\n              <tr>\n                <th>月份</th>\n                <th>购买金额</th>\n                <th>消费金额</th>\n                <th>净收支</th>\n              </tr>\n            </thead>\n            <tbody>\n              <template v-if=\"monthlyData && monthlyData.length > 0\">\n                <tr v-for=\"(month, index) in monthlyData.slice(0, 12)\" :key=\"`month-${index}`\">\n                  <td>{{ month.month || '无' }}</td>\n                  <td class=\"text-green-400\">¥{{ ((month.purchases || 0) / 100).toFixed(2) }}</td>\n                  <td class=\"text-red-400\">¥{{ ((month.consumption || 0) / 100).toFixed(2) }}</td>\n                  <td :class=\"(month.net || 0) >= 0 ? 'text-green-400' : 'text-red-400'\">\n                    ¥{{ ((month.net || 0) / 100).toFixed(2) }}\n                  </td>\n                </tr>\n              </template>\n              <template v-else>\n                <tr>\n                  <td colspan=\"4\" class=\"text-center text-gray-400\">暂无月度数据</td>\n                </tr>\n              </template>\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n\n\n      <!-- 筛选器 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">财务记录筛选</h2>\n\n        <div class=\"filter-form\">\n          <div class=\"form-group\">\n            <label>记录类型</label>\n            <select v-model=\"filters.record_type\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部记录</option>\n              <option value=\"purchase\">购买记录</option>\n              <option value=\"consumption\">消费记录</option>\n              <option value=\"quota\">配额使用</option>\n              <option value=\"points\">积分记录</option>\n              <option value=\"subscription\">订阅记录</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label>时间范围</label>\n            <select v-model=\"filters.time_range\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部时间</option>\n              <option value=\"today\">今天</option>\n              <option value=\"week\">最近一周</option>\n              <option value=\"month\">最近一月</option>\n              <option value=\"quarter\">最近三月</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label>内容类型</label>\n            <select v-model=\"filters.content_type\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部类型</option>\n              <option value=\"basic\">基础内容</option>\n              <option value=\"premium\">高级内容</option>\n              <option value=\"vip\">VIP服务</option>\n              <option value=\"recharge\">充值</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 详细财务流水 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">详细财务流水（原始单据）- 共{{ transactions.length }}条记录</h2>\n\n\n\n        <div v-if=\"loading\" class=\"loading-container\">\n          <i class=\"fas fa-spinner fa-spin text-2xl text-gray-400\"></i>\n          <p class=\"text-gray-400 mt-2\">加载中...</p>\n        </div>\n\n        <div v-else-if=\"transactions.length === 0\" class=\"no-data-container\">\n          <i class=\"fas fa-receipt text-4xl text-gray-600 mb-4\"></i>\n          <p class=\"text-gray-400\">暂无财务记录</p>\n        </div>\n\n        <div v-else class=\"table-container\">\n          <table class=\"data-table\">\n            <thead>\n              <tr>\n                <th>时间</th>\n                <th>类型</th>\n                <th>描述</th>\n                <th>支付详情</th>\n                <th>内容类型</th>\n                <th>现金变动</th>\n                <th>配额变动</th>\n                <th>积分变动</th>\n                <th>现金余额</th>\n                <th>配额余额</th>\n                <th>积分余额</th>\n                <th>交易ID</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr v-for=\"transaction in transactions\" :key=\"`${transaction.type}-${transaction.id}`\">\n                <td class=\"text-sm\">{{ formatDateTime(transaction.date) }}</td>\n                <td>\n                  <span :class=\"['type-badge', `type-${transaction.type}`]\">\n                    {{ getTransactionTypeText(transaction.type) }}\n                  </span>\n                </td>\n                <td class=\"text-sm\">{{ transaction.description }}</td>\n                <td class=\"text-sm\">\n                  <span v-if=\"transaction.payment_details\" class=\"payment-details\">\n                    {{ transaction.payment_details }}\n                  </span>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <td>\n                  <span v-if=\"transaction.content_type\" :class=\"['content-badge', `content-${transaction.content_type}`]\">\n                    {{ getContentTypeText(transaction.content_type) }}\n                  </span>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <td :class=\"getAmountClass(transaction.amount)\">\n                  <span v-if=\"transaction.amount !== 0\">\n                    {{ transaction.amount > 0 ? '+' : '' }}¥{{ (Math.abs(transaction.amount) / 100).toFixed(2) }}\n                  </span>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <td :class=\"getQuotaChangeClass(transaction.quota_change)\">\n                  <span v-if=\"transaction.quota_change\">\n                    {{ transaction.quota_change > 0 ? '+' : '' }}{{ transaction.quota_change }}\n                    <span class=\"text-xs\">\n                      ({{ transaction.quota_type === 'basic' ? '基础配额' :\n                           transaction.quota_type === 'premium' ? '高级配额' :\n                           transaction.quota_type || '配额' }})\n                    </span>\n                  </span>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <td :class=\"getPointsChangeClass(transaction.points_change)\">\n                  <span v-if=\"transaction.points_change\">\n                    {{ transaction.points_change > 0 ? '+' : '' }}{{ transaction.points_change }}\n                  </span>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <!-- 现金余额 -->\n                <td class=\"text-gray-300\">\n                  <div v-if=\"transaction.balance_before !== null || transaction.balance_after !== null\">\n                    <div class=\"text-xs\">前: ¥{{ ((transaction.balance_before || 0) / 100).toFixed(2) }}</div>\n                    <div class=\"text-xs\">后: ¥{{ ((transaction.balance_after || 0) / 100).toFixed(2) }}</div>\n                  </div>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n\n                <!-- 配额余额 -->\n                <td class=\"text-cyan-300\">\n                  <div v-if=\"transaction.quota_before !== null || transaction.quota_after !== null\">\n                    <div class=\"text-xs\">前: {{ transaction.quota_before || 0 }}</div>\n                    <div class=\"text-xs\">后: {{ transaction.quota_after || 0 }}</div>\n                  </div>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n\n                <!-- 积分余额 -->\n                <td class=\"text-orange-300\">\n                  <div v-if=\"transaction.points_before !== null || transaction.points_after !== null\">\n                    <div class=\"text-xs\">前: {{ transaction.points_before || 0 }}</div>\n                    <div class=\"text-xs\">后: {{ transaction.points_after || 0 }}</div>\n                  </div>\n                  <span v-else class=\"text-gray-400\">-</span>\n                </td>\n                <td class=\"text-xs text-gray-400\">{{ transaction.transaction_id || '-' }}</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n\n        <!-- 分页 -->\n        <div v-if=\"transactionPagination.pages > 1\" class=\"pagination\">\n          <button\n            class=\"btn-secondary\"\n            :disabled=\"!transactionPagination.has_prev\"\n            @click=\"handleTransactionPageChange(transactionPagination.prev_num)\"\n          >\n            上一页\n          </button>\n          <div class=\"page-numbers\">\n            <button\n              v-for=\"page in getTransactionPageNumbers()\"\n              :key=\"page\"\n              :class=\"['page-btn', page === transactionPagination.page ? 'active' : '']\"\n              @click=\"handleTransactionPageChange(page)\"\n            >\n              {{ page }}\n            </button>\n          </div>\n          <button\n            class=\"btn-secondary\"\n            :disabled=\"!transactionPagination.has_next\"\n            @click=\"handleTransactionPageChange(transactionPagination.next_num)\"\n          >\n            下一页\n          </button>\n        </div>\n      </div>\n\n      <!-- 筛选器 -->\n      <div class=\"info-card\">\n        <h2 class=\"card-title\">财务记录筛选</h2>\n\n        <div class=\"filter-form\">\n          <div class=\"form-group\">\n            <label>记录类型</label>\n            <select v-model=\"filters.record_type\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部记录</option>\n              <option value=\"purchase\">购买记录</option>\n              <option value=\"consumption\">消费记录</option>\n              <option value=\"quota\">配额使用</option>\n              <option value=\"points\">积分记录</option>\n              <option value=\"subscription\">订阅记录</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label>时间范围</label>\n            <select v-model=\"filters.time_range\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部时间</option>\n              <option value=\"today\">今天</option>\n              <option value=\"week\">最近一周</option>\n              <option value=\"month\">最近一月</option>\n              <option value=\"quarter\">最近三月</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label>内容类型</label>\n            <select v-model=\"filters.content_type\" class=\"form-select\" @change=\"loadTransactions\">\n              <option value=\"\">全部类型</option>\n              <option value=\"basic\">基础内容</option>\n              <option value=\"premium\">高级内容</option>\n              <option value=\"vip\">VIP服务</option>\n              <option value=\"recharge\">充值</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { usersApi } from '@/api/users'\n\nconst route = useRoute()\nconst router = useRouter()\n\n// 响应式数据\nconst loading = ref(true)\nconst error = ref('')\nconst user = ref({})\nconst financeStats = reactive({\n  total_spent: 0,\n  total_purchases: 0,\n  basic_purchases: 0,\n  premium_purchases: 0,\n  total_consumption: 0,\n  current_balance: 0,\n  cash_payments: 0,\n  quota_payments: 0,\n  points_payments: 0\n})\nconst monthlyData = ref([])\nconst transactions = ref([])\n\n// 调试信息\nconst debugInfo = reactive({\n  loadTransactionsCalled: false,\n  loadUserDetailCalled: false,\n  mountedCalled: false,\n  mockDataLength: 0\n})\n\nconst filters = reactive({\n  record_type: '',\n  time_range: '',\n  content_type: '',\n  page: 1,\n  per_page: 20\n})\n\nconst transactionPagination = reactive({\n  page: 1,\n  pages: 1,\n  total: 0,\n  per_page: 20,\n  has_prev: false,\n  has_next: false,\n  prev_num: null,\n  next_num: null\n})\n\n// 方法\nconst formatDateTime = (date: string) => {\n  if (!date) return '无'\n  try {\n    const dateObj = new Date(date)\n    if (isNaN(dateObj.getTime())) return '无效日期'\n    return dateObj.toLocaleString('zh-CN')\n  } catch (error) {\n    console.error('formatDateTime error:', error)\n    return '格式错误'\n  }\n}\n\nconst getVipStatus = (vipLevel: number) => {\n  try {\n    if (!vipLevel || vipLevel === 0) return '普通用户'\n    if (vipLevel === 2) return 'VIP Pro'\n    if (vipLevel === 1) return 'VIP'\n    return '普通用户'\n  } catch (error) {\n    console.error('getVipStatus error:', error)\n    return '未知'\n  }\n}\n\n// 计算配额使用百分比\nconst getQuotaPercentage = (used: number, total: number) => {\n  if (!total || total === 0) return 0\n  return Math.min((used / total) * 100, 100)\n}\n\nconst getVipClass = (vipLevel: number) => {\n  try {\n    if (!vipLevel || vipLevel === 0) return 'vip-free'\n    if (vipLevel === 2) return 'vip-premium'\n    return 'vip-basic'\n  } catch (error) {\n    console.error('getVipClass error:', error)\n    return 'vip-free'\n  }\n}\n\nconst getTransactionTypeText = (type: string) => {\n  const typeMap = {\n    purchase: '购买',\n    consumption: '消费',\n    quota_consumption: '配额消费',\n    quota_purchase: '配额购买',\n    points_earned: '积分获得',\n    points_consumption: '积分消费',\n    points_used: '积分消费',\n    subscription: '订阅',\n    recharge: '充值',\n    refund: '退款',\n    quota_reset: '配额重置',\n    vip_upgrade: 'VIP升级',\n    vip_expire: 'VIP到期'\n  }\n  return typeMap[type] || type\n}\n\nconst getContentTypeText = (type: string) => {\n  const typeMap = {\n    basic: '基础内容',\n    premium: '高级内容',\n    vip: 'VIP服务',\n    recharge: '充值',\n    subscription: '订阅服务',\n    quota_pack: '配额包'\n  }\n  return typeMap[type] || type\n}\n\nconst getPaymentMethodText = (method: string) => {\n  const methodMap = {\n    quota: '配额',\n    points: '积分',\n    alipay: '支付宝',\n    wechat: '微信',\n    mock: '模拟支付',\n    stripe: 'Stripe',\n    balance: '余额'\n  }\n  return methodMap[method] || method\n}\n\nconst getCostTypeText = (type: string) => {\n  const typeMap = {\n    quota: '配额',\n    points: '积分',\n    balance: '余额'\n  }\n  return typeMap[type] || type\n}\n\nconst getAmountClass = (amount: number) => {\n  if (amount > 0) return 'text-green-400'\n  if (amount < 0) return 'text-red-400'\n  return 'text-gray-400'\n}\n\nconst getQuotaChangeClass = (change: number) => {\n  if (change > 0) return 'text-green-400'\n  if (change < 0) return 'text-red-400'\n  return 'text-gray-400'\n}\n\nconst getPointsChangeClass = (change: number) => {\n  if (change > 0) return 'text-green-400'\n  if (change < 0) return 'text-red-400'\n  return 'text-gray-400'\n}\n\nconst getTransactionPageNumbers = () => {\n  const pages = []\n  const current = transactionPagination.page\n  const total = transactionPagination.pages\n\n  const start = Math.max(1, current - 2)\n  const end = Math.min(total, current + 2)\n\n  for (let i = start; i <= end; i++) {\n    pages.push(i)\n  }\n\n  return pages\n}\n\nconst loadUserDetail = async () => {\n  loading.value = true\n  error.value = ''\n\n  try {\n    const userId = route.params.id\n\n    // 真实API调用\n    try {\n      console.log('🔍 开始调用用户财务详情API:', `/admin/api/users/${userId}/finance`)\n\n      const response = await fetch(`/admin/api/users/${userId}/finance`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include'  // 包含认证信息\n      })\n\n      console.log('📡 API响应状态:', response.status, response.statusText)\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      console.log('📊 API返回数据:', data)\n\n      if (data.success) {\n        user.value = data.user\n        Object.assign(financeStats, data.finance_stats)\n        monthlyData.value = data.monthly_data\n\n        console.log('✅ 用户财务数据加载成功')\n        // 单独加载交易记录\n        await loadTransactions()\n        return\n      } else {\n        throw new Error(data.error || '获取用户财务详情失败')\n      }\n    } catch (apiError) {\n      console.error('❌ API调用失败:', apiError)\n      error.value = `API调用失败: ${apiError.message}`\n\n      // 不降级到模拟数据，显示错误信息\n      return\n    }\n\n  } catch (err) {\n    error.value = '加载用户详情失败: ' + err.message\n  } finally {\n    loading.value = false\n    console.log('🐛 loadUserDetail 完成，loading:', loading.value, 'error:', error.value)\n  }\n}\n\nconst loadTransactions = async () => {\n  try {\n    debugInfo.loadTransactionsCalled = true\n    console.log('🐛 loadTransactions 被调用了!')\n\n    const userId = route.params.id\n\n    // 构建查询参数\n    const params = new URLSearchParams({\n      page: filters.page.toString(),\n      per_page: filters.per_page.toString(),\n      record_type: filters.record_type || '',\n      time_range: filters.time_range || '',\n      content_type: filters.content_type || ''\n    })\n\n    console.log('🐛 查询参数:', params.toString())\n\n    // 真实API调用\n    try {\n      const response = await fetch(`/admin/api/users/${userId}/transactions?${params.toString()}`)\n      const data = await response.json()\n\n      if (data.success) {\n        transactions.value = data.data.transactions || []\n        Object.assign(transactionPagination, {\n          page: data.data.pagination.page || 1,\n          pages: data.data.pagination.pages || 1,\n          total: data.data.pagination.total || 0,\n          per_page: data.data.pagination.per_page || 20,\n          has_prev: data.data.pagination.has_prev || false,\n          has_next: data.data.pagination.has_next || false,\n          prev_num: data.data.pagination.prev_num || null,\n          next_num: data.data.pagination.next_num || null\n        })\n        return\n      } else {\n        throw new Error(data.error || '获取交易记录失败')\n      }\n    } catch (apiError) {\n      console.warn('API调用失败，使用模拟数据:', apiError)\n    }\n\n    // 模拟详细的交易数据 - 包含所有类型的交易记录\n    const mockTransactions = [\n      // 1. 购买记录 - 充值\n      {\n        id: 1,\n        type: 'purchase',\n        date: '2024-01-01T10:00:00Z',\n        description: '支付宝充值',\n        article_title: null,\n        content_type: 'recharge',\n        payment_method: 'alipay',\n        amount: 20000, // +200元\n        quota_change: 0,\n        quota_type: null,\n        points_change: 200, // 充值送积分\n        balance_before: 0,\n        balance_after: 20000,\n        transaction_id: 'ALI_20240101_001'\n      },\n      // 购买记录 - VIP\n      {\n        id: 2,\n        type: 'purchase',\n        date: '2024-01-02T09:15:00Z',\n        description: '购买VIP1个月',\n        article_title: null,\n        content_type: 'vip',\n        payment_method: 'balance',\n        amount: -12800, // -128元\n        quota_change: 0,\n        quota_type: null,\n        points_change: 0,\n        balance_before: 20000,\n        balance_after: 7200,\n        transaction_id: 'VIP_20240102_001'\n      },\n      // 购买记录 - 文章\n      {\n        id: 3,\n        type: 'purchase',\n        date: '2024-01-03T14:20:00Z',\n        description: '购买文章: 塔罗牌高级解读技巧',\n        article_title: '塔罗牌高级解读技巧',\n        content_type: 'premium',\n        payment_method: 'balance',\n        amount: -1000, // -10元\n        quota_change: 0,\n        quota_type: null,\n        points_change: 10, // 购买送积分\n        balance_before: 7200,\n        balance_after: 6200,\n        transaction_id: 'ART_20240103_001'\n      },\n      // 消费记录 - 配额消费\n      {\n        id: 4,\n        type: 'consumption',\n        date: '2024-01-04T16:45:00Z',\n        description: '消费: 爱情塔罗牌阵详解',\n        article_title: '爱情塔罗牌阵详解',\n        content_type: 'premium',\n        payment_method: null,\n        cost_type: 'quota',\n        amount: 0, // 配额消费不影响余额\n        quota_change: -1,\n        quota_type: 'premium',\n        points_change: 0,\n        balance_before: 6200,\n        balance_after: 6200,\n        transaction_id: null\n      },\n      // 消费记录 - 余额消费\n      {\n        id: 5,\n        type: 'consumption',\n        date: '2024-01-05T11:30:00Z',\n        description: '消费: 塔罗牌入门指南',\n        article_title: '塔罗牌入门指南',\n        content_type: 'basic',\n        payment_method: null,\n        cost_type: 'balance',\n        amount: -500, // -5元\n        quota_change: 0,\n        quota_type: null,\n        points_change: 0,\n        balance_before: 6200,\n        balance_after: 5700,\n        transaction_id: null\n      },\n      // 消费记录 - 积分消费\n      {\n        id: 6,\n        type: 'consumption',\n        date: '2024-01-06T13:15:00Z',\n        description: '消费: 塔罗牌牌意解析',\n        article_title: '塔罗牌牌意解析',\n        content_type: 'basic',\n        payment_method: null,\n        cost_type: 'points',\n        amount: 0, // 积分消费不影响余额\n        quota_change: 0,\n        quota_type: null,\n        points_change: -50,\n        balance_before: 5700,\n        balance_after: 5700,\n        transaction_id: null\n      },\n      // 积分记录 - 签到奖励\n      {\n        id: 7,\n        type: 'points_earned',\n        date: '2024-01-07T08:00:00Z',\n        description: '每日签到奖励',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: 10,\n        balance_before: 5700,\n        balance_after: 5700,\n        transaction_id: null\n      },\n      // 配额记录 - 配额重置\n      {\n        id: 8,\n        type: 'quota_reset',\n        date: '2024-01-08T00:00:00Z',\n        description: 'VIP配额月度重置',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 10, // 重置配额\n        quota_type: 'basic',\n        points_change: 0,\n        balance_before: 5700,\n        balance_after: 5700,\n        transaction_id: null\n      },\n      // 退款记录\n      {\n        id: 9,\n        type: 'refund',\n        date: '2024-01-09T15:30:00Z',\n        description: '文章购买退款: 塔罗牌高级解读技巧',\n        article_title: '塔罗牌高级解读技巧',\n        content_type: 'premium',\n        payment_method: 'balance',\n        amount: 1000, // +10元退款\n        quota_change: 0,\n        quota_type: null,\n        points_change: -10, // 扣回赠送积分\n        balance_before: 5700,\n        balance_after: 6700,\n        transaction_id: 'REF_20240109_001'\n      },\n      // 10. 订阅记录\n      {\n        id: 10,\n        type: 'subscription',\n        date: '2024-01-10T12:00:00Z',\n        description: '订阅塔罗师Alice',\n        article_title: null,\n        content_type: 'subscription',\n        payment_method: 'balance',\n        amount: -2000, // -20元\n        quota_change: 0,\n        quota_type: null,\n        points_change: 20,\n        balance_before: 6700,\n        balance_after: 4700,\n        transaction_id: 'SUB_20240110_001'\n      },\n      // 11. 积分消费 - 兑换配额\n      {\n        id: 11,\n        type: 'points_consumption',\n        date: '2024-01-11T09:30:00Z',\n        description: '积分兑换高级配额',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: 'points',\n        amount: 0,\n        quota_change: 5,\n        quota_type: 'premium',\n        points_change: -500, // 消费500积分\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 12. 积分获得 - 每日签到\n      {\n        id: 12,\n        type: 'points_earned',\n        date: '2024-01-12T08:00:00Z',\n        description: '每日签到奖励',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: 10,\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 13. 积分获得 - 推荐奖励\n      {\n        id: 13,\n        type: 'points_earned',\n        date: '2024-01-13T14:20:00Z',\n        description: '推荐新用户奖励',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: 100, // 推荐奖励100积分\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 14. 配额消费 - 查看高级内容\n      {\n        id: 14,\n        type: 'quota_consumption',\n        date: '2024-01-14T16:45:00Z',\n        description: '查看高级塔罗解读',\n        article_title: '深度塔罗牌解读：爱情运势',\n        content_type: 'premium',\n        payment_method: null,\n        cost_type: 'quota',\n        amount: 0,\n        quota_change: -1,\n        quota_type: 'premium',\n        points_change: 0,\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 15. 配额获得 - VIP月度重置\n      {\n        id: 15,\n        type: 'quota_reset',\n        date: '2024-02-01T00:00:00Z',\n        description: 'VIP配额月度重置',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 20, // 重置20个高级配额\n        quota_type: 'premium',\n        points_change: 0,\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 16. 积分消费 - 购买文章\n      {\n        id: 16,\n        type: 'points_consumption',\n        date: '2024-02-02T11:15:00Z',\n        description: '积分购买文章',\n        article_title: '塔罗牌占卜技巧大全',\n        content_type: 'basic',\n        payment_method: null,\n        cost_type: 'points',\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: -80, // 消费80积分\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 17. 积分获得 - 评论奖励\n      {\n        id: 17,\n        type: 'points_earned',\n        date: '2024-02-03T15:30:00Z',\n        description: '优质评论奖励',\n        article_title: '塔罗牌占卜技巧大全',\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: 20, // 评论奖励20积分\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 18. 配额消费 - 基础内容\n      {\n        id: 18,\n        type: 'quota_consumption',\n        date: '2024-02-04T10:20:00Z',\n        description: '查看基础塔罗内容',\n        article_title: '塔罗牌基础知识入门',\n        content_type: 'basic',\n        payment_method: null,\n        cost_type: 'quota',\n        amount: 0,\n        quota_change: -1,\n        quota_type: 'basic',\n        points_change: 0,\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: null\n      },\n      // 19. 积分获得 - 活动奖励\n      {\n        id: 19,\n        type: 'points_earned',\n        date: '2024-02-05T18:00:00Z',\n        description: '春节活动奖励',\n        article_title: null,\n        content_type: null,\n        payment_method: null,\n        cost_type: null,\n        amount: 0,\n        quota_change: 0,\n        quota_type: null,\n        points_change: 200, // 活动奖励200积分\n        balance_before: 4700,\n        balance_after: 4700,\n        transaction_id: 'EVENT_CNY_2024'\n      },\n      // 20. 配额购买 - 单独购买配额\n      {\n        id: 20,\n        type: 'quota_purchase',\n        date: '2024-02-06T13:45:00Z',\n        description: '购买高级配额包',\n        article_title: null,\n        content_type: 'quota_pack',\n        payment_method: 'balance',\n        amount: -1500, // -15元\n        quota_change: 10,\n        quota_type: 'premium',\n        points_change: 15, // 购买送积分\n        balance_before: 4700,\n        balance_after: 3200,\n        transaction_id: 'QUOTA_20240206_001'\n      }\n    ]\n\n    console.log('🐛 模拟数据长度:', mockTransactions.length)\n    debugInfo.mockDataLength = mockTransactions.length\n\n    // 根据筛选条件过滤数据\n    let filteredTransactions = mockTransactions\n\n    if (filters.record_type) {\n      filteredTransactions = filteredTransactions.filter(t => t.type === filters.record_type)\n    }\n\n    if (filters.content_type) {\n      filteredTransactions = filteredTransactions.filter(t => t.content_type === filters.content_type)\n    }\n\n    console.log('🐛 过滤后数据长度:', filteredTransactions.length)\n\n    // 模拟分页\n    const total = filteredTransactions.length\n    const pages = Math.ceil(total / filters.per_page)\n    const start = (filters.page - 1) * filters.per_page\n    const end = start + filters.per_page\n    const paginatedTransactions = filteredTransactions.slice(start, end)\n\n    console.log('🐛 分页后数据长度:', paginatedTransactions.length)\n    console.log('🐛 第一条记录:', paginatedTransactions[0])\n\n    transactions.value = paginatedTransactions\n    console.log('🐛 transactions.value 设置完成，长度:', transactions.value.length)\n    Object.assign(transactionPagination, {\n      page: filters.page,\n      pages: pages,\n      total: total,\n      per_page: filters.per_page,\n      has_prev: filters.page > 1,\n      has_next: filters.page < pages,\n      prev_num: filters.page > 1 ? filters.page - 1 : null,\n      next_num: filters.page < pages ? filters.page + 1 : null\n    })\n\n  } catch (err) {\n    console.error('加载交易记录失败:', err)\n  }\n}\n\nconst handleTransactionPageChange = (page: number) => {\n  if (page && page !== filters.page) {\n    filters.page = page\n    loadTransactions()\n  }\n}\n\nconst goBack = () => {\n  router.push('/users')\n}\n\nconst getCurrentTime = () => {\n  return new Date().toLocaleString()\n}\n\n\n\n// 初始化\nonMounted(() => {\n  loadUserDetail()\n  loadTransactions()\n})\n</script>\n\n<style scoped>\n/* 深色主题样式 */\n.user-detail {\n  color: #e0e0e0;\n  padding: 1.5rem;\n  background-color: #111827;\n  min-height: 100vh;\n}\n\n/* 页面标题 */\n.page-header {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1.5rem;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .page-header {\n    flex-direction: row;\n    align-items: center;\n    gap: 0;\n  }\n}\n\n.page-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #ffffff;\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 0.75rem;\n}\n\n/* 按钮样式 */\n.btn-primary {\n  background-color: #f59e0b;\n  color: #1f2937;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-primary:hover {\n  background-color: #d97706;\n}\n\n.btn-secondary {\n  background-color: #6b7280;\n  color: #d1d5db;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-secondary:hover {\n  background-color: #4b5563;\n  color: #fff;\n}\n\n/* 加载和错误状态 */\n.loading-container, .error-container, .no-data-container {\n  text-align: center;\n  padding: 2rem;\n}\n\n/* 信息卡片 */\n.info-card {\n  background-color: #374151;\n  border: 1px solid #4B5563;\n  border-radius: 0.5rem;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.card-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #f59e0b;\n  margin: 0 0 1rem 0;\n}\n\n/* 用户信息网格 */\n.user-info-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .user-info-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.info-item label {\n  color: #d1d5db;\n  font-size: 0.875rem;\n  margin-bottom: 0.25rem;\n}\n\n.info-item p {\n  color: #ffffff;\n  font-weight: 500;\n  margin: 0;\n}\n\n/* VIP徽章 */\n.vip-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 0.25rem;\n  font-size: 0.75rem;\n  font-weight: 500;\n  display: inline-block;\n}\n\n.vip-free {\n  background-color: rgba(107, 114, 128, 0.2);\n  color: #d1d5db;\n}\n\n.vip-basic {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fcd34d;\n}\n\n.vip-premium {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fcd34d;\n}\n\n/* 统计网格 */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .stats-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (min-width: 1024px) {\n  .stats-grid {\n    grid-template-columns: repeat(6, 1fr);\n  }\n}\n\n.payment-stats-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .payment-stats-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 0.25rem;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  color: #9ca3af;\n}\n\n/* 表格样式 */\n.table-container {\n  overflow-x: auto;\n}\n\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.data-table th,\n.data-table td {\n  padding: 0.75rem 1rem;\n  text-align: left;\n  border-bottom: 1px solid #4B5563;\n}\n\n.data-table th {\n  background-color: transparent;\n  color: #d1d5db;\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.data-table td {\n  color: #e0e0e0;\n}\n\n/* 徽章样式 */\n.type-badge, .content-badge, .payment-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 0.25rem;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n/* 交易类型徽章 */\n.type-purchase {\n  background-color: rgba(16, 185, 129, 0.2);\n  color: #6ee7b7;\n}\n\n.type-consumption {\n  background-color: rgba(220, 38, 38, 0.2);\n  color: #fca5a5;\n}\n\n.type-points_earned {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fcd34d;\n}\n\n.type-points_used,\n.type-points_consumption {\n  background-color: rgba(251, 146, 60, 0.2);\n  color: #fdba74;\n}\n\n.type-quota_consumption {\n  background-color: rgba(239, 68, 68, 0.2);\n  color: #f87171;\n}\n\n.type-quota_purchase {\n  background-color: rgba(34, 197, 94, 0.2);\n  color: #4ade80;\n}\n\n.type-subscription {\n  background-color: rgba(139, 92, 246, 0.2);\n  color: #c4b5fd;\n}\n\n.type-refund {\n  background-color: rgba(34, 197, 94, 0.2);\n  color: #86efac;\n}\n\n.type-quota_reset {\n  background-color: rgba(59, 130, 246, 0.2);\n  color: #93c5fd;\n}\n\n.type-vip_upgrade {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fcd34d;\n}\n\n.type-vip_expire {\n  background-color: rgba(107, 114, 128, 0.2);\n  color: #d1d5db;\n}\n\n.content-basic {\n  background-color: rgba(59, 130, 246, 0.2);\n  color: #93c5fd;\n}\n\n.content-premium {\n  background-color: rgba(139, 92, 246, 0.2);\n  color: #c4b5fd;\n}\n\n.payment-quota {\n  background-color: rgba(6, 182, 212, 0.2);\n  color: #67e8f9;\n}\n\n.payment-points {\n  background-color: rgba(251, 146, 60, 0.2);\n  color: #fdba74;\n}\n\n.payment-balance {\n  background-color: rgba(34, 197, 94, 0.2);\n  color: #86efac;\n}\n\n.payment-alipay {\n  background-color: rgba(59, 130, 246, 0.2);\n  color: #93c5fd;\n}\n\n.payment-wechat {\n  background-color: rgba(34, 197, 94, 0.2);\n  color: #86efac;\n}\n\n.payment-mock, .payment-stripe {\n  background-color: rgba(139, 92, 246, 0.2);\n  color: #c4b5fd;\n}\n\n.payment-other {\n  background-color: rgba(107, 114, 128, 0.2);\n  color: #d1d5db;\n}\n\n/* 筛选表单 */\n.filter-form {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n}\n\n@media (min-width: 768px) {\n  .filter-form {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group label {\n  color: #d1d5db;\n  font-size: 0.875rem;\n  margin-bottom: 0.5rem;\n}\n\n.form-select {\n  background-color: #1f2937;\n  border: 1px solid #374151;\n  border-radius: 0.5rem;\n  padding: 0.5rem 0.75rem;\n  color: #e0e0e0;\n  font-size: 0.875rem;\n}\n\n.form-select:focus {\n  outline: none;\n  border-color: #f59e0b;\n  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);\n}\n\n/* 文章链接 */\n.article-link {\n  color: #60a5fa;\n  text-decoration: underline;\n  cursor: pointer;\n}\n\n.article-link:hover {\n  color: #93c5fd;\n}\n\n.loading-container,\n.no-data-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n/* VIP信息样式 */\n.vip-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.vip-expire-info {\n  margin-top: 0.25rem;\n}\n\n.quota-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.quota-item {\n  font-size: 0.75rem;\n}\n\n/* 配额详细统计样式 */\n.quota-details-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n}\n\n.quota-detail-item {\n  background: rgba(55, 65, 81, 0.5);\n  padding: 1rem;\n  border-radius: 0.5rem;\n  border: 1px solid #4B5563;\n}\n\n.quota-type {\n  font-weight: 600;\n  color: #E5E7EB;\n  margin-bottom: 0.5rem;\n}\n\n.quota-progress {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.quota-bar {\n  width: 100%;\n  height: 8px;\n  background: #374151;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.quota-used {\n  height: 100%;\n  background: #06B6D4;\n  transition: width 0.3s ease;\n}\n\n.quota-used.premium {\n  background: #F59E0B;\n}\n\n.quota-text {\n  font-size: 0.875rem;\n  color: #9CA3AF;\n}\n\n/* 支付方式统计样式 */\n.stat-sublabel {\n  font-size: 0.75rem;\n  color: #6B7280;\n  margin-top: 0.25rem;\n}\n\n.payment-details {\n  font-size: 0.875rem;\n  color: #E5E7EB;\n}\n\n/* 间距 */\n.space-y-6 > * + * {\n  margin-top: 1.5rem;\n}\n</style>\n"}