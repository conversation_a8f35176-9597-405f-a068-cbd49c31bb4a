{"/home/<USER>/workspace/": {"rootPath": "/home/<USER>/workspace", "relPath": ""}, "/home/<USER>/workspace/文档/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/"}, "/home/<USER>/workspace/文档/项目代码分析/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/项目代码分析/"}, "/home/<USER>/workspace/文档/开发日志/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/开发日志/"}, "/home/<USER>/workspace/文档/开发日志/Archived_Dev_Logs/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/开发日志/Archived_Dev_Logs/"}, "/home/<USER>/workspace/文档/前端设计/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/前端设计/"}, "/home/<USER>/workspace/文档/AI工作协议/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/AI工作协议/"}, "/home/<USER>/workspace/文档/10-历史归档/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/10-历史归档/"}, "/home/<USER>/workspace/文档/10-历史归档/开发日志归档/开发日志/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/10-历史归档/开发日志归档/开发日志/"}, "/home/<USER>/workspace/文档/10-历史归档/开发日志归档/开发日志/Archived_Dev_Logs/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/10-历史归档/开发日志归档/开发日志/Archived_Dev_Logs/"}, "/home/<USER>/workspace/文档/10-历史归档/废弃文档/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/10-历史归档/废弃文档/"}, "/home/<USER>/workspace/文档/10-历史归档/历史对话记录/08-历史对话疑难杂症/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/10-历史归档/历史对话记录/08-历史对话疑难杂症/"}, "/home/<USER>/workspace/文档/09-项目报告/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/09-项目报告/"}, "/home/<USER>/workspace/文档/09-学习笔记/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/09-学习笔记/"}, "/home/<USER>/workspace/文档/08-项目管理/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/08-项目管理/"}, "/home/<USER>/workspace/文档/08-历史对话疑难杂症/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/08-历史对话疑难杂症/"}, "/home/<USER>/workspace/文档/07-开发规范/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/07-开发规范/"}, "/home/<USER>/workspace/文档/06-部署运维/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/06-部署运维/"}, "/home/<USER>/workspace/文档/06-开发规范/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/06-开发规范/"}, "/home/<USER>/workspace/文档/05-前端开发/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/05-前端开发/"}, "/home/<USER>/workspace/文档/05-前端开发/03-技术规范/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/05-前端开发/03-技术规范/"}, "/home/<USER>/workspace/文档/05-前端开发/02-功能实现/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/05-前端开发/02-功能实现/"}, "/home/<USER>/workspace/文档/05-前端开发/01-页面分析/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/05-前端开发/01-页面分析/"}, "/home/<USER>/workspace/文档/04-API接口/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/04-API接口/"}, "/home/<USER>/workspace/文档/03-UI设计/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/"}, "/home/<USER>/workspace/文档/03-UI设计/04-主题系统/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/04-主题系统/"}, "/home/<USER>/workspace/文档/03-UI设计/03-组件设计/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/03-组件设计/"}, "/home/<USER>/workspace/文档/03-UI设计/02-页面设计/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/02-页面设计/"}, "/home/<USER>/workspace/文档/03-UI设计/02-页面设计/阅读页面设计/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/02-页面设计/阅读页面设计/"}, "/home/<USER>/workspace/文档/03-UI设计/02-页面设计/管理后台页面设计/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/02-页面设计/管理后台页面设计/"}, "/home/<USER>/workspace/文档/03-UI设计/01-设计系统/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/01-设计系统/"}, "/home/<USER>/workspace/文档/02-功能模块/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/02-功能模块/"}, "/home/<USER>/workspace/文档/01-系统架构/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/01-系统架构/"}, "/home/<USER>/workspace/文档/00-项目概览/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/00-项目概览/"}, "/home/<USER>/workspace/文档/00-总览/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/00-总览/"}, "/home/<USER>/workspace/开发日志/": {"rootPath": "/home/<USER>/workspace", "relPath": "开发日志/"}, "/home/<USER>/workspace/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "utils/"}, "/home/<USER>/workspace/utils/api/": {"rootPath": "/home/<USER>/workspace", "relPath": "utils/api/"}, "/home/<USER>/workspace/templates/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/"}, "/home/<USER>/workspace/templates/upload/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/upload/"}, "/home/<USER>/workspace/templates/tickets/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/tickets/"}, "/home/<USER>/workspace/templates/support/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/support/"}, "/home/<USER>/workspace/templates/reading/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/reading/"}, "/home/<USER>/workspace/templates/payment/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/payment/"}, "/home/<USER>/workspace/templates/finance/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/finance/"}, "/home/<USER>/workspace/templates/errors/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/errors/"}, "/home/<USER>/workspace/templates/email/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/email/"}, "/home/<USER>/workspace/templates/auth/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/auth/"}, "/home/<USER>/workspace/templates/articles/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/articles/"}, "/home/<USER>/workspace/templates/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/admin/"}, "/home/<USER>/workspace/tasks/": {"rootPath": "/home/<USER>/workspace", "relPath": "tasks/"}, "/home/<USER>/workspace/tarot_uploader/": {"rootPath": "/home/<USER>/workspace", "relPath": "tarot_uploader/"}, "/home/<USER>/workspace/tarot_uploader/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "tarot_uploader/scripts/"}, "/home/<USER>/workspace/static/": {"rootPath": "/home/<USER>/workspace", "relPath": "static/"}, "/home/<USER>/workspace/static/uploads/": {"rootPath": "/home/<USER>/workspace", "relPath": "static/uploads/"}, "/home/<USER>/workspace/static/js/": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/"}, "/home/<USER>/workspace/static/img/": {"rootPath": "/home/<USER>/workspace", "relPath": "static/img/"}, "/home/<USER>/workspace/static/css/": {"rootPath": "/home/<USER>/workspace", "relPath": "static/css/"}, "/home/<USER>/workspace/static/admin/js/": {"rootPath": "/home/<USER>/workspace", "relPath": "static/admin/js/"}, "/home/<USER>/workspace/static/admin/css/": {"rootPath": "/home/<USER>/workspace", "relPath": "static/admin/css/"}, "/home/<USER>/workspace/node_modules/nanoid/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/nanoid/"}, "/home/<USER>/workspace/node_modules/zrender/src/graphic/shape/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/graphic/shape/"}, "/home/<USER>/workspace/node_modules/speakingurl/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/speakingurl/"}, "/home/<USER>/workspace/node_modules/rxjs/src/internal/testing/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/internal/testing/"}, "/home/<USER>/workspace/node_modules/math-intrinsics/constants/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/math-intrinsics/constants/"}, "/home/<USER>/workspace/node_modules/rxjs/src/internal/operators/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/internal/operators/"}, "/home/<USER>/workspace/node_modules/memoize-one/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/memoize-one/src/"}, "/home/<USER>/workspace/node_modules/rxjs/src/internal/scheduler/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/internal/scheduler/"}, "/home/<USER>/workspace/node_modules/math-intrinsics/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/math-intrinsics/"}, "/home/<USER>/workspace/node_modules/readdirp/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/readdirp/esm/"}, "/home/<USER>/workspace/node_modules/path-browserify/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/path-browserify/test/"}, "/home/<USER>/workspace/node_modules/lodash-es/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/lodash-es/"}, "/home/<USER>/workspace/node_modules/zrender/src/graphic/helper/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/graphic/helper/"}, "/home/<USER>/workspace/node_modules/node-releases/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/node-releases/"}, "/home/<USER>/workspace/node_modules/proxy-from-env/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/proxy-from-env/"}, "/home/<USER>/workspace/node_modules/tslib/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/tslib/"}, "/home/<USER>/workspace/node_modules/superjson/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/superjson/"}, "/home/<USER>/workspace/node_modules/sass/types/value/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass/types/value/"}, "/home/<USER>/workspace/node_modules/magic-string/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/magic-string/"}, "/home/<USER>/workspace/node_modules/rxjs/src/internal/util/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/internal/util/"}, "/home/<USER>/workspace/node_modules/zrender/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/"}, "/home/<USER>/workspace/node_modules/vite/types/internal/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vite/types/internal/"}, "/home/<USER>/workspace/node_modules/zrender/src/animation/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/animation/"}, "/home/<USER>/workspace/node_modules/zrender/src/graphic/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/graphic/"}, "/home/<USER>/workspace/node_modules/muggle-string/out/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/muggle-string/out/"}, "/home/<USER>/workspace/node_modules/typescript/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/typescript/"}, "/home/<USER>/workspace/node_modules/rxjs/src/internal/observable/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/internal/observable/"}, "/home/<USER>/workspace/node_modules/supports-color/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/supports-color/"}, "/home/<USER>/workspace/node_modules/sass-embedded/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass-embedded/"}, "/home/<USER>/workspace/node_modules/tailwindcss/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/tailwindcss/"}, "/home/<USER>/workspace/node_modules/rxjs/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/"}, "/home/<USER>/workspace/node_modules/vite/misc/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vite/misc/"}, "/home/<USER>/workspace/node_modules/zrender/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/"}, "/home/<USER>/workspace/node_modules/nanoid/non-secure/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/nanoid/non-secure/"}, "/home/<USER>/workspace/node_modules/vite/node_modules/picomatch/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vite/node_modules/picomatch/"}, "/home/<USER>/workspace/node_modules/micromatch/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/micromatch/"}, "/home/<USER>/workspace/node_modules/vue-tsc/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue-tsc/"}, "/home/<USER>/workspace/node_modules/zrender/src/tool/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/tool/"}, "/home/<USER>/workspace/node_modules/rxjs/src/internal/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/internal/"}, "/home/<USER>/workspace/node_modules/sass/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass/"}, "/home/<USER>/workspace/node_modules/rxjs/ajax/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/ajax/"}, "/home/<USER>/workspace/node_modules/vue/compiler-sfc/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue/compiler-sfc/"}, "/home/<USER>/workspace/node_modules/readdirp/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/readdirp/"}, "/home/<USER>/workspace/node_modules/vue-tsc/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue-tsc/bin/"}, "/home/<USER>/workspace/node_modules/vue/jsx-runtime/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue/jsx-runtime/"}, "/home/<USER>/workspace/node_modules/tinyglobby/node_modules/picomatch/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/tinyglobby/node_modules/picomatch/"}, "/home/<USER>/workspace/node_modules/picomatch/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/picomatch/"}, "/home/<USER>/workspace/node_modules/varint/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/varint/"}, "/home/<USER>/workspace/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/"}, "/home/<USER>/workspace/node_modules/speakingurl/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/speakingurl/test/"}, "/home/<USER>/workspace/node_modules/zrender/src/debug/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/debug/"}, "/home/<USER>/workspace/node_modules/math-intrinsics/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/math-intrinsics/test/"}, "/home/<USER>/workspace/node_modules/zrender/src/svg/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/svg/"}, "/home/<USER>/workspace/node_modules/rfdc/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rfdc/"}, "/home/<USER>/workspace/node_modules/sync-message-port/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sync-message-port/"}, "/home/<USER>/workspace/node_modules/nanoid/url-alphabet/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/nanoid/url-alphabet/"}, "/home/<USER>/workspace/node_modules/zrender/src/core/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/core/"}, "/home/<USER>/workspace/node_modules/mitt/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/mitt/"}, "/home/<USER>/workspace/node_modules/node-addon-api/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/node-addon-api/"}, "/home/<USER>/workspace/node_modules/update-browserslist-db/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/update-browserslist-db/"}, "/home/<USER>/workspace/node_modules/vue/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue/"}, "/home/<USER>/workspace/node_modules/zrender/src/contain/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/contain/"}, "/home/<USER>/workspace/node_modules/nanoid/async/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/nanoid/async/"}, "/home/<USER>/workspace/node_modules/sass/types/legacy/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass/types/legacy/"}, "/home/<USER>/workspace/node_modules/normalize-wheel-es/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/normalize-wheel-es/"}, "/home/<USER>/workspace/node_modules/vue-demi/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue-demi/scripts/"}, "/home/<USER>/workspace/node_modules/sync-child-process/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sync-child-process/"}, "/home/<USER>/workspace/node_modules/path-browserify/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/path-browserify/.github/"}, "/home/<USER>/workspace/node_modules/vue-router/node_modules/@vue/devtools-api/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue-router/node_modules/@vue/devtools-api/"}, "/home/<USER>/workspace/node_modules/postcss-value-parser/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/postcss-value-parser/"}, "/home/<USER>/workspace/node_modules/normalize-range/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/normalize-range/"}, "/home/<USER>/workspace/node_modules/node-releases/data/release-schedule/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/node-releases/data/release-schedule/"}, "/home/<USER>/workspace/node_modules/rxjs/src/internal/observable/dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/internal/observable/dom/"}, "/home/<USER>/workspace/node_modules/vite/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vite/bin/"}, "/home/<USER>/workspace/node_modules/rxjs/src/ajax/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/ajax/"}, "/home/<USER>/workspace/node_modules/picocolors/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/picocolors/"}, "/home/<USER>/workspace/node_modules/rollup/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rollup/"}, "/home/<USER>/workspace/services/": {"rootPath": "/home/<USER>/workspace", "relPath": "services/"}, "/home/<USER>/workspace/node_modules/tinyglobby/node_modules/fdir/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/tinyglobby/node_modules/fdir/"}, "/home/<USER>/workspace/node_modules/rxjs/src/internal/scheduled/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/internal/scheduled/"}, "/home/<USER>/workspace/node_modules/sass-embedded-linux-x64/dart-sass/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass-embedded-linux-x64/dart-sass/"}, "/home/<USER>/workspace/node_modules/to-regex-range/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/to-regex-range/"}, "/home/<USER>/workspace/node_modules/rxjs/operators/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/operators/"}, "/home/<USER>/workspace/node_modules/rxjs/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/"}, "/home/<USER>/workspace/node_modules/vite/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vite/types/"}, "/home/<USER>/workspace/node_modules/vue/server-renderer/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue/server-renderer/"}, "/home/<USER>/workspace/node_modules/muggle-string/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/muggle-string/"}, "/home/<USER>/workspace/node_modules/vue-demi/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue-demi/"}, "/home/<USER>/workspace/node_modules/perfect-debounce/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/perfect-debounce/"}, "/home/<USER>/workspace/node_modules/rxjs/src/internal/ajax/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/internal/ajax/"}, "/home/<USER>/workspace/node_modules/lodash-unified/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/lodash-unified/"}, "/home/<USER>/workspace/node_modules/vue-router/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue-router/"}, "/home/<USER>/workspace/node_modules/sass-embedded-linux-musl-x64/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass-embedded-linux-musl-x64/"}, "/home/<USER>/workspace/node_modules/pinia/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/pinia/"}, "/home/<USER>/workspace/node_modules/tslib/modules/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/tslib/modules/"}, "/home/<USER>/workspace/r2_uploader_standalone/": {"rootPath": "/home/<USER>/workspace", "relPath": "r2_uploader_standalone/"}, "/home/<USER>/workspace/node_modules/zrender/src/mixin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/mixin/"}, "/home/<USER>/workspace/node_modules/mime-db/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/mime-db/"}, "/home/<USER>/workspace/node_modules/rxjs/src/internal/symbol/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/internal/symbol/"}, "/home/<USER>/workspace/node_modules/zrender/src/svg-legacy/helper/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/svg-legacy/helper/"}, "/home/<USER>/workspace/node_modules/vue-echarts/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue-echarts/"}, "/home/<USER>/workspace/node_modules/vscode-uri/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vscode-uri/"}, "/home/<USER>/workspace/node_modules/node-addon-api/tools/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/node-addon-api/tools/"}, "/home/<USER>/workspace/node_modules/sass/types/logger/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass/types/logger/"}, "/home/<USER>/workspace/node_modules/zrender/src/canvas/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/canvas/"}, "/home/<USER>/workspace/node_modules/path-browserify/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/path-browserify/"}, "/home/<USER>/workspace/node_modules/rxjs/fetch/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/fetch/"}, "/home/<USER>/workspace/node_modules/zrender/src/dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/dom/"}, "/home/<USER>/workspace/node_modules/mime-types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/mime-types/"}, "/home/<USER>/workspace/node_modules/source-map-js/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/source-map-js/"}, "/home/<USER>/workspace/node_modules/sass/types/util/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass/types/util/"}, "/home/<USER>/workspace/node_modules/tinyglobby/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/tinyglobby/"}, "/home/<USER>/workspace/node_modules/rxjs/src/webSocket/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/webSocket/"}, "/home/<USER>/workspace/node_modules/sass/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass/types/"}, "/home/<USER>/workspace/node_modules/memoize-one/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/memoize-one/"}, "/home/<USER>/workspace/node_modules/rxjs/src/fetch/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/fetch/"}, "/home/<USER>/workspace/node_modules/speakingurl/examples/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/speakingurl/examples/"}, "/home/<USER>/workspace/node_modules/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/"}, "/home/<USER>/workspace/node_modules/rxjs/testing/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/testing/"}, "/home/<USER>/workspace/node_modules/zrender/src/svg-legacy/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/zrender/src/svg-legacy/"}, "/home/<USER>/workspace/node_modules/vite/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vite/"}, "/home/<USER>/workspace/node_modules/speakingurl/typings/speakingurl/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/speakingurl/typings/speakingurl/"}, "/home/<USER>/workspace/node_modules/sass-embedded-linux-x64/dart-sass/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass-embedded-linux-x64/dart-sass/src/"}, "/home/<USER>/workspace/node_modules/vite/node_modules/fdir/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vite/node_modules/fdir/"}, "/home/<USER>/workspace/node_modules/typescript/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/typescript/bin/"}, "/home/<USER>/workspace/node_modules/sass-embedded-linux-musl-x64/dart-sass/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass-embedded-linux-musl-x64/dart-sass/src/"}, "/home/<USER>/workspace/node_modules/sass-embedded-linux-musl-x64/dart-sass/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass-embedded-linux-musl-x64/dart-sass/"}, "/home/<USER>/workspace/node_modules/vue-demi/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue-demi/bin/"}, "/home/<USER>/workspace/node_modules/rfdc/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rfdc/test/"}, "/home/<USER>/workspace/node_modules/vue-router/vetur/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/vue-router/vetur/"}, "/home/<USER>/workspace/node_modules/node-releases/data/processed/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/node-releases/data/processed/"}, "/home/<USER>/workspace/node_modules/rxjs/src/testing/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/testing/"}, "/home/<USER>/workspace/node_modules/rxjs/src/operators/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/src/operators/"}, "/home/<USER>/workspace/node_modules/sass-embedded-linux-x64/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/sass-embedded-linux-x64/"}, "/home/<USER>/workspace/node_modules/math-intrinsics/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/math-intrinsics/.github/"}, "/home/<USER>/workspace/node_modules/postcss/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/postcss/"}, "/home/<USER>/workspace/node_modules/rfdc/.github/workflows/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rfdc/.github/workflows/"}, "/home/<USER>/workspace/node_modules/rxjs/webSocket/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/rxjs/webSocket/"}, "/home/<USER>/workspace/node_modules/nanoid/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/nanoid/bin/"}, "/home/<USER>/workspace/node_modules/lodash/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/lodash/"}, "/home/<USER>/workspace/static/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "static/admin/"}, "/home/<USER>/workspace/文档/05-数据库设计/": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/05-数据库设计/"}, "/home/<USER>/workspace/node_modules/lodash/fp/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/lodash/fp/"}, "/home/<USER>/workspace/node_modules/has-symbols/test/shams/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/has-symbols/test/shams/"}, "/home/<USER>/workspace/node_modules/es-object-atoms/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-object-atoms/"}, "/home/<USER>/workspace/node_modules/is-extglob/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/is-extglob/"}, "/home/<USER>/workspace/node_modules/function-bind/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/function-bind/"}, "/home/<USER>/workspace/node_modules/escape-html/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/escape-html/"}, "/home/<USER>/workspace/node_modules/element-plus/theme-chalk/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/theme-chalk/"}, "/home/<USER>/workspace/node_modules/es-define-property/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-define-property/"}, "/home/<USER>/workspace/node_modules/get-intrinsic/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/get-intrinsic/"}, "/home/<USER>/workspace/node_modules/has-flag/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/has-flag/"}, "/home/<USER>/workspace/node_modules/follow-redirects/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/follow-redirects/"}, "/home/<USER>/workspace/node_modules/element-plus/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/"}, "/home/<USER>/workspace/node_modules/has-tostringtag/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/has-tostringtag/"}, "/home/<USER>/workspace/node_modules/is-glob/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/is-glob/"}, "/home/<USER>/workspace/node_modules/es-set-tostringtag/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-set-tostringtag/"}, "/home/<USER>/workspace/node_modules/es-errors/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-errors/"}, "/home/<USER>/workspace/node_modules/estree-walker/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/estree-walker/src/"}, "/home/<USER>/workspace/node_modules/has-symbols/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/has-symbols/"}, "/home/<USER>/workspace/node_modules/function-bind/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/function-bind/.github/"}, "/home/<USER>/workspace/node_modules/has-tostringtag/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/has-tostringtag/test/"}, "/home/<USER>/workspace/node_modules/fraction.js/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/fraction.js/"}, "/home/<USER>/workspace/node_modules/immutable/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/immutable/"}, "/home/<USER>/workspace/node_modules/form-data/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/form-data/"}, "/home/<USER>/workspace/node_modules/get-proto/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/get-proto/"}, "/home/<USER>/workspace/node_modules/es-set-tostringtag/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-set-tostringtag/test/"}, "/home/<USER>/workspace/node_modules/get-intrinsic/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/get-intrinsic/test/"}, "/home/<USER>/workspace/node_modules/is-number/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/is-number/"}, "/home/<USER>/workspace/node_modules/he/man/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/he/man/"}, "/home/<USER>/workspace/node_modules/estree-walker/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/estree-walker/"}, "/home/<USER>/workspace/node_modules/fill-range/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/fill-range/"}, "/home/<USER>/workspace/node_modules/esbuild/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/esbuild/"}, "/home/<USER>/workspace/node_modules/escalade/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/escalade/"}, "/home/<USER>/workspace/node_modules/he/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/he/"}, "/home/<USER>/workspace/node_modules/gopd/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/gopd/"}, "/home/<USER>/workspace/node_modules/gopd/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/gopd/.github/"}, "/home/<USER>/workspace/node_modules/is-what/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/is-what/"}, "/home/<USER>/workspace/node_modules/estree-walker/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/estree-walker/types/"}, "/home/<USER>/workspace/node_modules/entities/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/entities/"}, "/home/<USER>/workspace/node_modules/escalade/sync/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/escalade/sync/"}, "/home/<USER>/workspace/node_modules/has-tostringtag/test/shams/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/has-tostringtag/test/shams/"}, "/home/<USER>/workspace/node_modules/es-errors/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-errors/test/"}, "/home/<USER>/workspace/node_modules/hasown/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/hasown/"}, "/home/<USER>/workspace/node_modules/es-object-atoms/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-object-atoms/.github/"}, "/home/<USER>/workspace/node_modules/get-proto/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/get-proto/test/"}, "/home/<USER>/workspace/node_modules/gopd/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/gopd/test/"}, "/home/<USER>/workspace/node_modules/has-tostringtag/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/has-tostringtag/.github/"}, "/home/<USER>/workspace/node_modules/function-bind/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/function-bind/test/"}, "/home/<USER>/workspace/node_modules/get-intrinsic/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/get-intrinsic/.github/"}, "/home/<USER>/workspace/node_modules/es-object-atoms/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-object-atoms/test/"}, "/home/<USER>/workspace/node_modules/he/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/he/bin/"}, "/home/<USER>/workspace/node_modules/hookable/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/hookable/"}, "/home/<USER>/workspace/node_modules/es-errors/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-errors/.github/"}, "/home/<USER>/workspace/node_modules/has-symbols/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/has-symbols/test/"}, "/home/<USER>/workspace/node_modules/es-define-property/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-define-property/test/"}, "/home/<USER>/workspace/node_modules/es-define-property/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/es-define-property/.github/"}, "/home/<USER>/workspace/node_modules/hasown/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/hasown/.github/"}, "/home/<USER>/workspace/node_modules/get-proto/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/get-proto/.github/"}, "/home/<USER>/workspace/node_modules/has-symbols/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/has-symbols/.github/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/watermark/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/watermark/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/locale/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/locale/"}, "/home/<USER>/workspace/node_modules/element-plus/theme-chalk/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/theme-chalk/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/locale/lang/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/locale/lang/"}, "/home/<USER>/workspace/node_modules/element-plus/es/utils/vue/props/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/utils/vue/props/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-composition/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-composition/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree-select/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree-select/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/transfer/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/transfer/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree-v2/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree-v2/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree/src/model/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree/src/model/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/virtual-list/src/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/virtual-list/src/hooks/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/transfer/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/transfer/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/virtual-list/src/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/virtual-list/src/components/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-popper/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-popper/"}, "/home/<USER>/workspace/node_modules/element-plus/es/directives/trap-focus/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/directives/trap-focus/"}, "/home/<USER>/workspace/node_modules/element-plus/es/utils/vue/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/utils/vue/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/visual-hidden/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/visual-hidden/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-prevent-global/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-prevent-global/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/utils/dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/utils/dom/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-locale/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-locale/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/virtual-list/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/virtual-list/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-lockscreen/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-lockscreen/"}, "/home/<USER>/workspace/node_modules/element-plus/es/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/utils/"}, "/home/<USER>/workspace/node_modules/element-plus/es/constants/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/constants/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/virtual-list/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/virtual-list/style/"}, "/home/<USER>/workspace/node_modules/element-plus/theme-chalk/src/common/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/theme-chalk/src/common/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/upload/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/upload/"}, "/home/<USER>/workspace/node_modules/element-plus/theme-chalk/src/date-picker/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/theme-chalk/src/date-picker/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-timeout/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-timeout/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-teleport/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-teleport/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree-v2/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree-v2/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree-select/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree-select/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/upload/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/upload/src/"}, "/home/<USER>/workspace/node_modules/element-plus/theme-chalk/src/mixins/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/theme-chalk/src/mixins/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-intermediate-render/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-intermediate-render/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-attrs/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-attrs/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-draggable/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-draggable/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree/"}, "/home/<USER>/workspace/node_modules/element-plus/es/directives/mousewheel/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/directives/mousewheel/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree-select/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree-select/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree-v2/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree-v2/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-calc-input-width/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-calc-input-width/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-namespace/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-namespace/"}, "/home/<USER>/workspace/node_modules/element-plus/es/directives/repeat-click/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/directives/repeat-click/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-floating/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-floating/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-focus/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-focus/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-forward-ref/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-forward-ref/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/watermark/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/watermark/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-popper-container/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-popper-container/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/transfer/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/transfer/"}, "/home/<USER>/workspace/node_modules/element-plus/theme-chalk/src/color/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/theme-chalk/src/color/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-transition-fallthrough/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-transition-fallthrough/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/transfer/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/transfer/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-prop/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-prop/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-deprecated/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-deprecated/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-cursor/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-cursor/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-delayed-toggle/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-delayed-toggle/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-throttle-render/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-throttle-render/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-modal/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-modal/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/upload/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/upload/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-ordered-children/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-ordered-children/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-z-index/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-z-index/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/virtual-list/src/builders/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/virtual-list/src/builders/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/watermark/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/watermark/"}, "/home/<USER>/workspace/node_modules/element-plus/es/directives/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/directives/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-escape-keydown/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-escape-keydown/"}, "/home/<USER>/workspace/node_modules/element-plus/theme-chalk/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/theme-chalk/dark/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-empty-values/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-empty-values/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-size/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-size/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-model-toggle/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-model-toggle/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-id/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-id/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/visual-hidden/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/visual-hidden/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-focus-controller/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-focus-controller/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-same-target/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-same-target/"}, "/home/<USER>/workspace/node_modules/element-plus/es/directives/click-outside/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/directives/click-outside/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tree-v2/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tree-v2/"}, "/home/<USER>/workspace/node_modules/element-plus/theme-chalk/src/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/theme-chalk/src/dark/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/virtual-list/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/virtual-list/"}, "/home/<USER>/workspace/node_modules/element-plus/es/hooks/use-aria/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/hooks/use-aria/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tabs/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tabs/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/time-select/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/time-select/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tab-pane/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tab-pane/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/steps/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/steps/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table/src/table-header/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table/src/table-header/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/sub-menu/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/sub-menu/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table-v2/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table-v2/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tabs/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tabs/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/select-v2/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/select-v2/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table-v2/src/renderers/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table-v2/src/renderers/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/slider/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/slider/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tag/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tag/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table/src/table-column/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table/src/table-column/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/switch/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/switch/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/segmented/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/segmented/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table-v2/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table-v2/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table/src/table/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table/src/table/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/segmented/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/segmented/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/time-picker/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/time-picker/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/time-picker/src/props/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/time-picker/src/props/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/text/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/text/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/splitter/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/splitter/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/space/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/space/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/slider/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/slider/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tour/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tour/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/slider/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/slider/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/steps/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/steps/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/select-v2/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/select-v2/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/select/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/select/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/timeline/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/timeline/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/time-picker/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/time-picker/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/scrollbar/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/scrollbar/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table/src/store/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table/src/store/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tooltip-v2/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tooltip-v2/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/skeleton/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/skeleton/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tooltip/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tooltip/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/teleport/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/teleport/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/timeline/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/timeline/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/splitter/src/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/splitter/src/hooks/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/time-picker/src/common/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/time-picker/src/common/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/time-picker/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/time-picker/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/scrollbar/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/scrollbar/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table-v2/src/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table-v2/src/components/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table/src/table-body/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table/src/table-body/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/statistic/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/statistic/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/time-picker/src/time-picker-com/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/time-picker/src/time-picker-com/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tooltip/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tooltip/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/text/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/text/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/skeleton/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/skeleton/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/space/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/space/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tour/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tour/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/timeline-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/timeline-item/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table-v2/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table-v2/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/select-v2/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/select-v2/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/select/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/select/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/statistic/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/statistic/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/timeline/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/timeline/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/step/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/step/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/statistic/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/statistic/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tooltip/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tooltip/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/segmented/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/segmented/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/splitter/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/splitter/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/select/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/select/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tabs/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tabs/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/teleport/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/teleport/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/skeleton/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/skeleton/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/time-select/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/time-select/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table-column/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table-column/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/steps/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/steps/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/skeleton-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/skeleton-item/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/switch/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/switch/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/teleport/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/teleport/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tag/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tag/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table-v2/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table-v2/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/slider/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/slider/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table/src/table-footer/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table/src/table-footer/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/splitter/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/splitter/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/slot/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/slot/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tour-step/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tour-step/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/switch/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/switch/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/text/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/text/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/splitter-panel/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/splitter-panel/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/slot/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/slot/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/time-picker/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/time-picker/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/scrollbar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/scrollbar/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/table/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/table/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/time-select/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/time-select/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tooltip-v2/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tooltip-v2/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/space/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/space/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tour/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tour/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/tag/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/tag/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/row/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/row/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/row/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/row/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/row/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/row/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/roving-focus-group/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/roving-focus-group/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/roving-focus-group/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/roving-focus-group/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/result/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/result/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/result/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/result/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/result/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/result/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/rate/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/rate/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/rate/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/rate/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/rate/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/rate/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/radio-group/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/radio-group/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/radio-button/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/radio-button/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/radio/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/radio/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/radio/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/radio/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/radio/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/radio/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/progress/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/progress/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/progress/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/progress/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/progress/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/progress/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/popper/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/popper/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/popper/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/popper/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/popper/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/popper/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/popper/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/popper/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/popover/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/popover/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/popover/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/popover/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/popover/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/popover/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/popconfirm/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/popconfirm/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/popconfirm/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/popconfirm/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/popconfirm/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/popconfirm/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/pagination/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/pagination/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/pagination/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/pagination/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/pagination/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/pagination/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/pagination/src/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/pagination/src/components/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/page-header/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/page-header/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/page-header/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/page-header/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/page-header/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/page-header/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/overlay/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/overlay/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/overlay/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/overlay/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/overlay/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/overlay/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/option-group/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/option-group/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/option/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/option/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/notification/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/notification/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/notification/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/notification/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/notification/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/notification/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/message-box/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/message-box/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/message-box/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/message-box/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/message-box/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/message-box/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/message/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/message/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/message/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/message/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/message/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/message/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/menu-item-group/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/menu-item-group/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/menu-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/menu-item/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/menu/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/menu/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/menu/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/menu/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/menu/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/menu/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/menu/src/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/menu/src/utils/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/mention/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/mention/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/mention/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/mention/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/mention/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/mention/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/main/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/main/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/loading/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/loading/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/loading/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/loading/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/loading/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/loading/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/link/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/link/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/link/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/link/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/link/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/link/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/input-tag/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/input-tag/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/input-tag/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/input-tag/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/input-tag/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/input-tag/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/input-tag/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/input-tag/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/input-number/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/input-number/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/input-number/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/input-number/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/input-number/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/input-number/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/input/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/input/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/input/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/input/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/input/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/input/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/date-picker/src/date-picker-com/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/date-picker/src/date-picker-com/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/form/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/form/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/container/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/container/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/date-picker/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/date-picker/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/date-picker/src/props/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/date-picker/src/props/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/image/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/image/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/descriptions/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/descriptions/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/image-viewer/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/image-viewer/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/image-viewer/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/image-viewer/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/empty/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/empty/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/date-picker/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/date-picker/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/date-picker/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/date-picker/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/dialog/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/dialog/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/form-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/form-item/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/dialog/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/dialog/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/footer/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/footer/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/empty/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/empty/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/container/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/container/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/descriptions-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/descriptions-item/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/infinite-scroll/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/infinite-scroll/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/form/src/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/form/src/hooks/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/dropdown/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/dropdown/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/form/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/form/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/focus-trap/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/focus-trap/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/dropdown-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/dropdown-item/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/divider/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/divider/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/header/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/header/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/dropdown/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/dropdown/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/countdown/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/countdown/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/infinite-scroll/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/infinite-scroll/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/dropdown/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/dropdown/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/date-picker/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/date-picker/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/image/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/image/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/image-viewer/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/image-viewer/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/dialog/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/dialog/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/descriptions/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/descriptions/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/icon/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/icon/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/drawer/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/drawer/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/countdown/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/countdown/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/dropdown-menu/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/dropdown-menu/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/focus-trap/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/focus-trap/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/image/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/image/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/icon/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/icon/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/form/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/form/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/container/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/container/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/drawer/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/drawer/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/drawer/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/drawer/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/empty/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/empty/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/divider/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/divider/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/infinite-scroll/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/infinite-scroll/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/countdown/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/countdown/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/icon/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/icon/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/descriptions/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/descriptions/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/divider/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/divider/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/carousel/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/carousel/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/collapse/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/collapse/src/"}, "/home/<USER>/workspace/node_modules/echarts/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/export/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/export/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/aside/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/aside/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/renderer/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/renderer/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/collapse-transition/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/collapse-transition/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/model/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/model/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/breadcrumb-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/breadcrumb-item/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/legacy/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/legacy/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/visual/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/visual/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/i18n/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/i18n/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/coord/geo/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/coord/geo/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/calendar/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/calendar/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/checkbox/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/checkbox/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/checkbox/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/checkbox/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/collapse-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/collapse-item/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/data/helper/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/data/helper/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/anchor/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/anchor/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/checkbox/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/checkbox/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/cascader/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/cascader/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/util/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/util/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/export/api/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/export/api/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/coord/cartesian/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/coord/cartesian/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/base/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/base/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/config-provider/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/config-provider/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/button/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/button/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/avatar/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/avatar/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/calendar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/calendar/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/color-picker/src/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/color-picker/src/utils/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/coord/geo/fix/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/coord/geo/fix/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/color-picker/src/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/color-picker/src/components/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/backtop/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/backtop/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/anchor/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/anchor/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/view/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/view/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/alert/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/alert/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/cascader-panel/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/cascader-panel/src/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/coord/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/coord/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/cascader-panel/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/cascader-panel/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/avatar/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/avatar/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/checkbox/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/checkbox/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/alert/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/alert/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/color-picker/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/color-picker/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/core/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/core/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/affix/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/affix/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/affix/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/affix/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/autocomplete/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/autocomplete/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/col/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/col/src/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/preprocessor/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/preprocessor/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/checkbox-group/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/checkbox-group/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/check-tag/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/check-tag/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/collapse/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/collapse/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/collection/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/collection/src/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/coord/parallel/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/coord/parallel/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/label/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/label/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/col/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/col/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/backtop/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/backtop/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/model/mixin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/model/mixin/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/alert/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/alert/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/coord/calendar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/coord/calendar/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/card/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/card/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/color-picker/src/props/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/color-picker/src/props/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/color-picker/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/color-picker/src/composables/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/anchor-link/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/anchor-link/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/badge/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/badge/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/collapse/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/collapse/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/color-picker/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/color-picker/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/card/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/card/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/badge/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/badge/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/data/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/data/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/carousel/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/carousel/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/check-tag/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/check-tag/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/preprocessor/helper/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/preprocessor/helper/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/visualMap/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/visualMap/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/collapse-transition/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/collapse-transition/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/color-picker/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/color-picker/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/button/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/button/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/checkbox-button/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/checkbox-button/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/cascader/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/cascader/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/config-provider/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/config-provider/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/avatar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/avatar/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/coord/radar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/coord/radar/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/coord/polar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/coord/polar/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/anchor/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/anchor/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/util/shape/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/util/shape/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/config-provider/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/config-provider/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/button/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/button/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/breadcrumb/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/breadcrumb/src/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/badge/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/badge/src/"}, "/home/<USER>/workspace/node_modules/electron-to-chromium/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/electron-to-chromium/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/check-tag/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/check-tag/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/cascader-panel/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/cascader-panel/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/col/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/col/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/button-group/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/button-group/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/scale/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/scale/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/carousel/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/carousel/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/carousel-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/carousel-item/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/backtop/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/backtop/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/breadcrumb/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/breadcrumb/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/card/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/card/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/loading/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/loading/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/affix/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/affix/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/collection/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/collection/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/autocomplete/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/autocomplete/style/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/processor/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/processor/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/layout/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/layout/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/breadcrumb/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/breadcrumb/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/autocomplete/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/autocomplete/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/coord/single/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/coord/single/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/config-provider/src/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/config-provider/src/hooks/"}, "/home/<USER>/workspace/node_modules/element-plus/es/_virtual/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/_virtual/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/calendar/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/calendar/style/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/collapse-transition/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/collapse-transition/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/theme/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/theme/"}, "/home/<USER>/workspace/node_modules/element-plus/es/components/cascader/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/element-plus/es/components/cascader/"}, "/home/<USER>/workspace/node_modules/dayjs/locale/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/locale/"}, "/home/<USER>/workspace/node_modules/colorjs.io/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/colorjs.io/src/"}, "/home/<USER>/workspace/node_modules/dayjs/plugin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/plugin/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/sankey/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/sankey/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/graph/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/graph/"}, "/home/<USER>/workspace/node_modules/colorjs.io/types/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/colorjs.io/types/src/"}, "/home/<USER>/workspace/node_modules/echarts/extension/bmap/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/extension/bmap/"}, "/home/<USER>/workspace/node_modules/colorjs.io/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/colorjs.io/"}, "/home/<USER>/workspace/node_modules/copy-anything/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/copy-anything/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/tooltip/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/tooltip/"}, "/home/<USER>/workspace/node_modules/colorjs.io/types/src/spaces/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/colorjs.io/types/src/spaces/"}, "/home/<USER>/workspace/node_modules/echarts/i18n/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/i18n/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/treemap/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/treemap/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/isLeapYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/isLeapYear/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/legend/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/legend/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/helper/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/helper/"}, "/home/<USER>/workspace/node_modules/echarts/theme/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/theme/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/negativeYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/negativeYear/"}, "/home/<USER>/workspace/node_modules/echarts/ssr/client/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/ssr/client/"}, "/home/<USER>/workspace/node_modules/combined-stream/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/combined-stream/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/locale/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/locale/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/"}, "/home/<USER>/workspace/node_modules/detect-libc/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/detect-libc/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/relativeTime/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/relativeTime/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/helper/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/helper/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/localizedFormat/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/localizedFormat/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/axisPointer/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/axisPointer/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/map/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/map/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/marker/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/marker/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/dataZoom/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/dataZoom/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/tree/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/tree/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/preParsePostFormat/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/preParsePostFormat/"}, "/home/<USER>/workspace/node_modules/colorjs.io/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/colorjs.io/types/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/brush/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/brush/"}, "/home/<USER>/workspace/node_modules/colorjs.io/types/src/deltaE/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/colorjs.io/types/src/deltaE/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/buddhistEra/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/buddhistEra/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/localeData/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/localeData/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/calendar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/calendar/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/weekOfYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/weekOfYear/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/funnel/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/funnel/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/singleAxis/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/singleAxis/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/sunburst/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/sunburst/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/themeRiver/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/themeRiver/"}, "/home/<USER>/workspace/node_modules/de-indent/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/de-indent/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/effectScatter/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/effectScatter/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/axis/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/axis/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/bar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/bar/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/animation/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/animation/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/badMutable/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/badMutable/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/grid/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/grid/"}, "/home/<USER>/workspace/node_modules/echarts/ssr/client/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/ssr/client/types/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/lines/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/lines/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/geo/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/geo/"}, "/home/<USER>/workspace/node_modules/dayjs/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/custom/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/custom/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/radar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/radar/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/parallel/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/parallel/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/pie/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/pie/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/candlestick/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/candlestick/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/toolbox/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/toolbox/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/graphic/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/graphic/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/arraySupport/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/arraySupport/"}, "/home/<USER>/workspace/node_modules/echarts/extension/dataTool/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/extension/dataTool/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/line/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/line/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/duration/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/duration/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/calendar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/calendar/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/polar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/polar/"}, "/home/<USER>/workspace/node_modules/delayed-stream/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/delayed-stream/"}, "/home/<USER>/workspace/node_modules/dunder-proto/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dunder-proto/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/title/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/title/"}, "/home/<USER>/workspace/node_modules/colorjs.io/types/src/contrast/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/colorjs.io/types/src/contrast/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/timeline/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/timeline/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/toolbox/feature/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/toolbox/feature/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/isSameOrAfter/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/isSameOrAfter/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/gauge/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/gauge/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/toObject/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/toObject/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/updateLocale/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/updateLocale/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/toArray/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/toArray/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/quarterOfYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/quarterOfYear/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/isYesterday/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/isYesterday/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/timezone/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/timezone/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/dataset/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/dataset/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/weekYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/weekYear/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/transform/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/transform/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/action/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/action/"}, "/home/<USER>/workspace/node_modules/csstype/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/csstype/"}, "/home/<USER>/workspace/node_modules/echarts/licenses/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/licenses/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/isoWeeksInYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/isoWeeksInYear/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/minMax/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/minMax/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/weekday/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/weekday/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/isToday/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/isToday/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/boxplot/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/boxplot/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/heatmap/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/heatmap/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/isoWeek/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/isoWeek/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/advancedFormat/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/advancedFormat/"}, "/home/<USER>/workspace/node_modules/dunder-proto/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dunder-proto/test/"}, "/home/<USER>/workspace/node_modules/dunder-proto/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dunder-proto/.github/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/isBetween/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/isBetween/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/devHelper/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/devHelper/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/parallel/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/parallel/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/objectSupport/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/objectSupport/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/utc/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/utc/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/aria/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/aria/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/customParseFormat/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/customParseFormat/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/component/radar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/component/radar/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/isTomorrow/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/isTomorrow/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/isSameOrBefore/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/isSameOrBefore/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/pluralGetSet/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/pluralGetSet/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/dayOfYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/dayOfYear/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/isMoment/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/isMoment/"}, "/home/<USER>/workspace/node_modules/dayjs/esm/plugin/bigIntSupport/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/dayjs/esm/plugin/bigIntSupport/"}, "/home/<USER>/workspace/node_modules/echarts/types/src/chart/scatter/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/echarts/types/src/chart/scatter/"}, "/home/<USER>/workspace/node_modules/detect-libc/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/detect-libc/bin/"}, "/home/<USER>/workspace/node_modules/caniuse-lite/data/features/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/caniuse-lite/data/features/"}, "/home/<USER>/workspace/node_modules/caniuse-lite/data/regions/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/caniuse-lite/data/regions/"}, "/home/<USER>/workspace/node_modules/colorjs.io/src/contrast/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/colorjs.io/src/contrast/"}, "/home/<USER>/workspace/node_modules/colorjs.io/src/deltaE/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/colorjs.io/src/deltaE/"}, "/home/<USER>/workspace/node_modules/colorjs.io/src/spaces/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/colorjs.io/src/spaces/"}, "/home/<USER>/workspace/node_modules/chokidar/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/chokidar/"}, "/home/<USER>/workspace/node_modules/chokidar/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/chokidar/esm/"}, "/home/<USER>/workspace/node_modules/caniuse-lite/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/caniuse-lite/"}, "/home/<USER>/workspace/node_modules/caniuse-lite/data/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/caniuse-lite/data/"}, "/home/<USER>/workspace/node_modules/braces/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/braces/"}, "/home/<USER>/workspace/node_modules/@types/lodash/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@types/lodash/"}, "/home/<USER>/workspace/node_modules/@types/lodash-es/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@types/lodash-es/"}, "/home/<USER>/workspace/node_modules/@types/lodash/fp/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@types/lodash/fp/"}, "/home/<USER>/workspace/node_modules/call-bind-apply-helpers/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/call-bind-apply-helpers/.github/"}, "/home/<USER>/workspace/node_modules/async-validator/dist-types/validator/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/async-validator/dist-types/validator/"}, "/home/<USER>/workspace/node_modules/axios/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/axios/"}, "/home/<USER>/workspace/node_modules/@vue/reactivity/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/reactivity/"}, "/home/<USER>/workspace/node_modules/alien-signals/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/alien-signals/esm/"}, "/home/<USER>/workspace/node_modules/@vue/language-core/node_modules/picomatch/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/language-core/node_modules/picomatch/"}, "/home/<USER>/workspace/node_modules/autoprefixer/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/autoprefixer/"}, "/home/<USER>/workspace/node_modules/@vueuse/core/node_modules/vue-demi/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vueuse/core/node_modules/vue-demi/"}, "/home/<USER>/workspace/node_modules/call-bind-apply-helpers/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/call-bind-apply-helpers/"}, "/home/<USER>/workspace/node_modules/@vueuse/core/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vueuse/core/"}, "/home/<USER>/workspace/node_modules/@volar/typescript/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@volar/typescript/"}, "/home/<USER>/workspace/node_modules/@vue/compiler-core/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/compiler-core/"}, "/home/<USER>/workspace/node_modules/@types/web-bluetooth/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@types/web-bluetooth/"}, "/home/<USER>/workspace/node_modules/async-validator/dist-node/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/async-validator/dist-node/"}, "/home/<USER>/workspace/node_modules/@vueuse/metadata/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vueuse/metadata/"}, "/home/<USER>/workspace/node_modules/@vue/runtime-core/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/runtime-core/"}, "/home/<USER>/workspace/node_modules/browserslist/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/browserslist/"}, "/home/<USER>/workspace/node_modules/@vue/compiler-sfc/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/compiler-sfc/"}, "/home/<USER>/workspace/node_modules/@vueuse/core/node_modules/vue-demi/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vueuse/core/node_modules/vue-demi/bin/"}, "/home/<USER>/workspace/node_modules/autoprefixer/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/autoprefixer/bin/"}, "/home/<USER>/workspace/node_modules/buffer-builder/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/buffer-builder/"}, "/home/<USER>/workspace/node_modules/@vue/devtools-api/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/devtools-api/"}, "/home/<USER>/workspace/node_modules/@volar/source-map/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@volar/source-map/"}, "/home/<USER>/workspace/node_modules/async-validator/dist-types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/async-validator/dist-types/"}, "/home/<USER>/workspace/node_modules/async-validator/dist-types/rule/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/async-validator/dist-types/rule/"}, "/home/<USER>/workspace/node_modules/@vue/compiler-ssr/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/compiler-ssr/"}, "/home/<USER>/workspace/node_modules/asynckit/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/asynckit/"}, "/home/<USER>/workspace/node_modules/alien-signals/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/alien-signals/"}, "/home/<USER>/workspace/node_modules/call-bind-apply-helpers/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/call-bind-apply-helpers/test/"}, "/home/<USER>/workspace/node_modules/async-validator/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/async-validator/"}, "/home/<USER>/workspace/node_modules/@vue/compiler-dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/compiler-dom/"}, "/home/<USER>/workspace/node_modules/@vue/compiler-vue2/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/compiler-vue2/"}, "/home/<USER>/workspace/node_modules/@vue/server-renderer/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/server-renderer/"}, "/home/<USER>/workspace/node_modules/@vitejs/plugin-vue/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vitejs/plugin-vue/"}, "/home/<USER>/workspace/node_modules/@vue/devtools-kit/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/devtools-kit/"}, "/home/<USER>/workspace/node_modules/@vue/shared/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/shared/"}, "/home/<USER>/workspace/node_modules/@vue/runtime-dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/runtime-dom/"}, "/home/<USER>/workspace/node_modules/@vue/devtools-shared/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/devtools-shared/"}, "/home/<USER>/workspace/node_modules/@vueuse/shared/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vueuse/shared/"}, "/home/<USER>/workspace/node_modules/alien-signals/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/alien-signals/cjs/"}, "/home/<USER>/workspace/node_modules/alien-signals/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/alien-signals/types/"}, "/home/<USER>/workspace/node_modules/@vueuse/shared/node_modules/vue-demi/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vueuse/shared/node_modules/vue-demi/"}, "/home/<USER>/workspace/node_modules/@volar/language-core/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@volar/language-core/"}, "/home/<USER>/workspace/node_modules/@vueuse/shared/node_modules/vue-demi/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vueuse/shared/node_modules/vue-demi/scripts/"}, "/home/<USER>/workspace/node_modules/@vue/language-core/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/language-core/"}, "/home/<USER>/workspace/node_modules/autoprefixer/data/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/autoprefixer/data/"}, "/home/<USER>/workspace/node_modules/@vueuse/shared/node_modules/vue-demi/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vueuse/shared/node_modules/vue-demi/bin/"}, "/home/<USER>/workspace/node_modules/@vueuse/core/node_modules/vue-demi/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vueuse/core/node_modules/vue-demi/scripts/"}, "/home/<USER>/workspace/node_modules/birpc/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/birpc/"}, "/home/<USER>/workspace/node_modules/async-validator/dist-web/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/async-validator/dist-web/"}, "/home/<USER>/workspace/node_modules/@vue/compiler-vue2/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@vue/compiler-vue2/types/"}, "/home/<USER>/workspace/node_modules/buffer-builder/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/buffer-builder/test/"}, "/home/<USER>/workspace/frontend/node_modules/type-fest/source/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/type-fest/source/"}, "/home/<USER>/workspace/frontend/node_modules/wrap-ansi/node_modules/strip-ansi/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/wrap-ansi/node_modules/strip-ansi/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/types/generated/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/types/generated/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/stubs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/stubs/"}, "/home/<USER>/workspace/frontend/src/views/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/admin/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/types/"}, "/home/<USER>/workspace/node_modules/@babel/parser/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@babel/parser/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/scripts/"}, "/home/<USER>/workspace/frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/"}, "/home/<USER>/workspace/frontend/node_modules/thenify/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/thenify/"}, "/home/<USER>/workspace/frontend/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/"}, "/home/<USER>/workspace/node_modules/@babel/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@babel/types/"}, "/home/<USER>/workspace/frontend/node_modules/sucrase/node_modules/brace-expansion/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/sucrase/node_modules/brace-expansion/"}, "/home/<USER>/workspace/node_modules/@types/lodash/common/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@types/lodash/common/"}, "/home/<USER>/workspace/frontend/node_modules/wrap-ansi/node_modules/ansi-regex/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/wrap-ansi/node_modules/ansi-regex/"}, "/home/<USER>/workspace/node_modules/@floating-ui/utils/dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@floating-ui/utils/dom/"}, "/home/<USER>/workspace/frontend/node_modules/vue-demi/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vue-demi/scripts/"}, "/home/<USER>/workspace/frontend/node_modules/xml-name-validator/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/xml-name-validator/"}, "/home/<USER>/workspace/frontend/node_modules/strip-json-comments/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/strip-json-comments/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/src/"}, "/home/<USER>/workspace/frontend/node_modules/undici-types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/undici-types/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/"}, "/home/<USER>/workspace/frontend/node_modules/supports-preserve-symlinks-flag/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/supports-preserve-symlinks-flag/"}, "/home/<USER>/workspace/node_modules/@babel/helper-validator-identifier/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@babel/helper-validator-identifier/"}, "/home/<USER>/workspace/frontend/src/components/dialog/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/dialog/"}, "/home/<USER>/workspace/frontend/node_modules/supports-color/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/supports-color/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/src/macos/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/src/macos/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/scripts/"}, "/home/<USER>/workspace/frontend/node_modules/wrap-ansi/node_modules/ansi-styles/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/wrap-ansi/node_modules/ansi-styles/"}, "/home/<USER>/workspace/frontend/node_modules/sucrase/register/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/sucrase/register/"}, "/home/<USER>/workspace/frontend/node_modules/vue-router/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vue-router/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/src/util/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/src/util/"}, "/home/<USER>/workspace/frontend/node_modules/wrap-ansi-cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/wrap-ansi-cjs/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/"}, "/home/<USER>/workspace/node_modules/@bufbuild/protobuf/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@bufbuild/protobuf/"}, "/home/<USER>/workspace/frontend/node_modules/yaml/browser/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/yaml/browser/"}, "/home/<USER>/workspace/frontend/node_modules/type-fest/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/type-fest/"}, "/home/<USER>/workspace/frontend/node_modules/text-table/example/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/text-table/example/"}, "/home/<USER>/workspace/node_modules/@element-plus/icons-vue/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@element-plus/icons-vue/"}, "/home/<USER>/workspace/frontend/node_modules/word-wrap/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/word-wrap/"}, "/home/<USER>/workspace/node_modules/@ctrl/tinycolor/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@ctrl/tinycolor/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/src/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/src/value-parser/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/src/value-parser/"}, "/home/<USER>/workspace/frontend/node_modules/type-fest/ts41/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/type-fest/ts41/"}, "/home/<USER>/workspace/frontend/node_modules/text-table/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/text-table/test/"}, "/home/<USER>/workspace/node_modules/@jridgewell/sourcemap-codec/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@jridgewell/sourcemap-codec/types/"}, "/home/<USER>/workspace/frontend/node_modules/string-width/node_modules/strip-ansi/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/string-width/node_modules/strip-ansi/"}, "/home/<USER>/workspace/frontend/src/store/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/store/"}, "/home/<USER>/workspace/node_modules/@jridgewell/sourcemap-codec/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@jridgewell/sourcemap-codec/src/"}, "/home/<USER>/workspace/frontend/node_modules/strip-ansi/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/strip-ansi/"}, "/home/<USER>/workspace/frontend/node_modules/string-width/node_modules/ansi-regex/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/string-width/node_modules/ansi-regex/"}, "/home/<USER>/workspace/frontend/node_modules/vue/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vue/"}, "/home/<USER>/workspace/frontend/node_modules/vite/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vite/types/"}, "/home/<USER>/workspace/frontend/node_modules/vue-eslint-parser/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vue-eslint-parser/"}, "/home/<USER>/workspace/frontend/node_modules/strip-ansi-cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/strip-ansi-cjs/"}, "/home/<USER>/workspace/frontend/node_modules/util-deprecate/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/util-deprecate/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/src/public/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/src/public/"}, "/home/<USER>/workspace/node_modules/@babel/parser/typings/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@babel/parser/typings/"}, "/home/<USER>/workspace/frontend/node_modules/update-browserslist-db/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/update-browserslist-db/"}, "/home/<USER>/workspace/frontend/node_modules/ts-interface-checker/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/ts-interface-checker/"}, "/home/<USER>/workspace/frontend/node_modules/yocto-queue/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/yocto-queue/"}, "/home/<USER>/workspace/frontend/node_modules/string-width-cjs/node_modules/emoji-regex/es2015/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/string-width-cjs/node_modules/emoji-regex/es2015/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/src/cli/init/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/src/cli/init/"}, "/home/<USER>/workspace/frontend/node_modules/sucrase/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/sucrase/"}, "/home/<USER>/workspace/node_modules/@rollup/rollup-linux-x64-musl/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@rollup/rollup-linux-x64-musl/"}, "/home/<USER>/workspace/frontend/node_modules/string-width-cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/string-width-cjs/"}, "/home/<USER>/workspace/frontend/src/api/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/api/"}, "/home/<USER>/workspace/frontend/node_modules/vue/compiler-sfc/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vue/compiler-sfc/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher-linux-x64-glibc/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher-linux-x64-glibc/"}, "/home/<USER>/workspace/frontend/node_modules/sucrase/node_modules/glob/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/sucrase/node_modules/glob/"}, "/home/<USER>/workspace/migrations/": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/"}, "/home/<USER>/workspace/frontend/node_modules/sucrase/node_modules/minimatch/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/sucrase/node_modules/minimatch/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/src/wasm/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/src/wasm/"}, "/home/<USER>/workspace/frontend/node_modules/to-regex-range/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/to-regex-range/"}, "/home/<USER>/workspace/frontend/node_modules/which/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/which/bin/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/src/linux/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/src/linux/"}, "/home/<USER>/workspace/frontend/node_modules/type-check/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/type-check/"}, "/home/<USER>/workspace/frontend/src/views/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/"}, "/home/<USER>/workspace/frontend/node_modules/vue/server-renderer/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vue/server-renderer/"}, "/home/<USER>/workspace/frontend/node_modules/string-width-cjs/node_modules/emoji-regex/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/string-width-cjs/node_modules/emoji-regex/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/src/css/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/src/css/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/src/windows/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/src/windows/"}, "/home/<USER>/workspace/frontend/node_modules/string-width/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/string-width/"}, "/home/<USER>/workspace/frontend/src/components/layout/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/src/watchman/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/src/watchman/"}, "/home/<USER>/workspace/frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/es2015/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/es2015/"}, "/home/<USER>/workspace/frontend/src/components/article/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/article/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/src/unix/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/src/unix/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/src/postcss-plugins/nesting/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/src/postcss-plugins/nesting/"}, "/home/<USER>/workspace/frontend/node_modules/wrappy/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/wrappy/"}, "/home/<USER>/workspace/frontend/node_modules/which/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/which/"}, "/home/<USER>/workspace/node_modules/@rollup/rollup-linux-x64-gnu/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@rollup/rollup-linux-x64-gnu/"}, "/home/<USER>/workspace/frontend/node_modules/text-table/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/text-table/"}, "/home/<USER>/workspace/frontend/node_modules/vue/jsx-runtime/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vue/jsx-runtime/"}, "/home/<USER>/workspace/frontend/node_modules/vue-demi/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vue-demi/"}, "/home/<USER>/workspace/node_modules/@popperjs/core/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@popperjs/core/"}, "/home/<USER>/workspace/frontend/src/views/auth/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/"}, "/home/<USER>/workspace/frontend/node_modules/vue-router/vetur/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vue-router/vetur/"}, "/home/<USER>/workspace/frontend/node_modules/vite/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vite/bin/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher-linux-x64-musl/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher-linux-x64-musl/"}, "/home/<USER>/workspace/frontend/node_modules/uri-js/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/uri-js/"}, "/home/<USER>/workspace/frontend/node_modules/thenify-all/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/thenify-all/"}, "/home/<USER>/workspace/frontend/node_modules/supports-preserve-symlinks-flag/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/supports-preserve-symlinks-flag/test/"}, "/home/<USER>/workspace/frontend/node_modules/wrap-ansi-cjs/node_modules/string-width/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/wrap-ansi-cjs/node_modules/string-width/"}, "/home/<USER>/workspace/frontend/node_modules/wrap-ansi/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/wrap-ansi/"}, "/home/<USER>/workspace/frontend/node_modules/vite/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vite/"}, "/home/<USER>/workspace/node_modules/@floating-ui/dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@floating-ui/dom/"}, "/home/<USER>/workspace/frontend/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/"}, "/home/<USER>/workspace/node_modules/@jridgewell/sourcemap-codec/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@jridgewell/sourcemap-codec/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/nesting/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/nesting/"}, "/home/<USER>/workspace/node_modules/@floating-ui/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@floating-ui/utils/"}, "/home/<USER>/workspace/frontend/src/components/reading/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/reading/"}, "/home/<USER>/workspace/node_modules/@types/estree/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@types/estree/"}, "/home/<USER>/workspace/node_modules/@babel/helper-string-parser/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@babel/helper-string-parser/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/src/cli/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/src/cli/"}, "/home/<USER>/workspace/node_modules/@rolldown/pluginutils/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@rolldown/pluginutils/"}, "/home/<USER>/workspace/node_modules/@floating-ui/core/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@floating-ui/core/"}, "/home/<USER>/workspace/node_modules/@esbuild/linux-x64/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@esbuild/linux-x64/"}, "/home/<USER>/workspace/frontend/node_modules/vue-demi/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/vue-demi/bin/"}, "/home/<USER>/workspace/frontend/node_modules/yaml/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/yaml/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/src/kqueue/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/src/kqueue/"}, "/home/<USER>/workspace/frontend/node_modules/sucrase/node_modules/brace-expansion/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/sucrase/node_modules/brace-expansion/.github/"}, "/home/<USER>/workspace/frontend/node_modules/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/"}, "/home/<USER>/workspace/middleware/": {"rootPath": "/home/<USER>/workspace", "relPath": "middleware/"}, "/home/<USER>/workspace/frontend/node_modules/supports-preserve-symlinks-flag/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/supports-preserve-symlinks-flag/.github/"}, "/home/<USER>/workspace/node_modules/@parcel/watcher/src/shared/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@parcel/watcher/src/shared/"}, "/home/<USER>/workspace/frontend/src/router/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/router/"}, "/home/<USER>/workspace/node_modules/@babel/parser/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "node_modules/@babel/parser/bin/"}, "/home/<USER>/workspace/frontend/node_modules/tailwindcss/src/cli/help/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/tailwindcss/src/cli/help/"}, "/home/<USER>/workspace/frontend/node_modules/sucrase/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/sucrase/bin/"}, "/home/<USER>/workspace/frontend/src/assets/css/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/assets/css/"}, "/home/<USER>/workspace/frontend/node_modules/sucrase/ts-node-plugin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/sucrase/ts-node-plugin/"}, "/home/<USER>/workspace/frontend/node_modules/source-map-js/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/source-map-js/"}, "/home/<USER>/workspace/frontend/node_modules/signal-exit/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/signal-exit/"}, "/home/<USER>/workspace/frontend/node_modules/shebang-regex/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/shebang-regex/"}, "/home/<USER>/workspace/frontend/node_modules/shebang-command/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/shebang-command/"}, "/home/<USER>/workspace/frontend/node_modules/semver/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/semver/"}, "/home/<USER>/workspace/frontend/node_modules/semver/ranges/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/semver/ranges/"}, "/home/<USER>/workspace/frontend/node_modules/semver/internal/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/semver/internal/"}, "/home/<USER>/workspace/frontend/node_modules/semver/functions/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/semver/functions/"}, "/home/<USER>/workspace/frontend/node_modules/semver/classes/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/semver/classes/"}, "/home/<USER>/workspace/frontend/node_modules/semver/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/semver/bin/"}, "/home/<USER>/workspace/frontend/node_modules/run-parallel/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/run-parallel/"}, "/home/<USER>/workspace/frontend/node_modules/rollup/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/rollup/"}, "/home/<USER>/workspace/frontend/node_modules/rimraf/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/rimraf/"}, "/home/<USER>/workspace/frontend/node_modules/reusify/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/reusify/"}, "/home/<USER>/workspace/frontend/node_modules/reusify/benchmarks/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/reusify/benchmarks/"}, "/home/<USER>/workspace/frontend/node_modules/reusify/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/reusify/.github/"}, "/home/<USER>/workspace/frontend/node_modules/reusify/.github/workflows/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/reusify/.github/workflows/"}, "/home/<USER>/workspace/frontend/node_modules/resolve-from/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve-from/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/shadowed_core/node_modules/util/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/shadowed_core/node_modules/util/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/without_basedir/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/without_basedir/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/symlinked/package/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/symlinked/package/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/symlinked/_/symlink_target/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/symlinked/_/symlink_target/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/symlinked/_/node_modules/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/symlinked/_/node_modules/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/same_names/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/same_names/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/same_names/foo/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/same_names/foo/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/quux/foo/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/quux/foo/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/other_path/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/other_path/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/nested_symlinks/mylib/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/nested_symlinks/mylib/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/multirepo/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/multirepo/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/multirepo/packages/package-b/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/multirepo/packages/package-b/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/multirepo/packages/package-a/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/multirepo/packages/package-a/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/invalid_main/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/invalid_main/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/incorrect_main/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/incorrect_main/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/false_main/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/false_main/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/dot_slash_main/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/dot_slash_main/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/dot_main/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/dot_main/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/browser_field/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/browser_field/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/resolver/baz/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/resolver/baz/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/precedence/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/precedence/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/precedence/bbb/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/precedence/bbb/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/precedence/aaa/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/precedence/aaa/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/pathfilter/deep_ref/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/pathfilter/deep_ref/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/node_path/y/ccc/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/node_path/y/ccc/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/node_path/y/bbb/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/node_path/y/bbb/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/node_path/x/ccc/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/node_path/x/ccc/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/node_path/x/aaa/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/node_path/x/aaa/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/module_dir/zmodules/bbb/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/module_dir/zmodules/bbb/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/module_dir/ymodules/aaa/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/module_dir/ymodules/aaa/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/module_dir/xmodules/aaa/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/module_dir/xmodules/aaa/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/dotdot/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/dotdot/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/test/dotdot/abc/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/test/dotdot/abc/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/example/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/example/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/bin/"}, "/home/<USER>/workspace/frontend/node_modules/resolve/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/resolve/.github/"}, "/home/<USER>/workspace/frontend/node_modules/readdirp/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/readdirp/"}, "/home/<USER>/workspace/frontend/node_modules/read-cache/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/read-cache/"}, "/home/<USER>/workspace/frontend/node_modules/queue-microtask/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/queue-microtask/"}, "/home/<USER>/workspace/frontend/node_modules/punycode/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/punycode/"}, "/home/<USER>/workspace/frontend/node_modules/proxy-from-env/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/proxy-from-env/"}, "/home/<USER>/workspace/frontend/node_modules/prettier/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/prettier/"}, "/home/<USER>/workspace/frontend/node_modules/prettier/plugins/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/prettier/plugins/"}, "/home/<USER>/workspace/frontend/node_modules/prettier/internal/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/prettier/internal/"}, "/home/<USER>/workspace/frontend/node_modules/prettier/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/prettier/bin/"}, "/home/<USER>/workspace/frontend/node_modules/prelude-ls/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/prelude-ls/"}, "/home/<USER>/workspace/frontend/node_modules/postcss-value-parser/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/postcss-value-parser/"}, "/home/<USER>/workspace/frontend/node_modules/postcss-selector-parser/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/postcss-selector-parser/"}, "/home/<USER>/workspace/frontend/node_modules/postcss-nested/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/postcss-nested/"}, "/home/<USER>/workspace/frontend/node_modules/postcss-load-config/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/postcss-load-config/"}, "/home/<USER>/workspace/frontend/node_modules/postcss-load-config/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/postcss-load-config/src/"}, "/home/<USER>/workspace/frontend/node_modules/postcss-js/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/postcss-js/"}, "/home/<USER>/workspace/frontend/node_modules/postcss-import/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/postcss-import/"}, "/home/<USER>/workspace/frontend/node_modules/postcss/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/postcss/"}, "/home/<USER>/workspace/frontend/node_modules/pirates/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/pirates/"}, "/home/<USER>/workspace/frontend/node_modules/pinia/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/pinia/"}, "/home/<USER>/workspace/frontend/node_modules/pify/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/pify/"}, "/home/<USER>/workspace/frontend/node_modules/picomatch/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/picomatch/"}, "/home/<USER>/workspace/frontend/node_modules/picocolors/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/picocolors/"}, "/home/<USER>/workspace/frontend/node_modules/path-scurry/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/path-scurry/"}, "/home/<USER>/workspace/frontend/node_modules/path-parse/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/path-parse/"}, "/home/<USER>/workspace/frontend/node_modules/path-key/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/path-key/"}, "/home/<USER>/workspace/frontend/node_modules/path-is-absolute/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/path-is-absolute/"}, "/home/<USER>/workspace/frontend/node_modules/path-exists/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/path-exists/"}, "/home/<USER>/workspace/frontend/node_modules/parent-module/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/parent-module/"}, "/home/<USER>/workspace/frontend/node_modules/package-json-from-dist/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/package-json-from-dist/"}, "/home/<USER>/workspace/frontend/node_modules/p-locate/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/p-locate/"}, "/home/<USER>/workspace/frontend/node_modules/p-limit/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/p-limit/"}, "/home/<USER>/workspace/frontend/node_modules/optionator/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/optionator/"}, "/home/<USER>/workspace/frontend/node_modules/once/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/once/"}, "/home/<USER>/workspace/frontend/node_modules/object-hash/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/object-hash/"}, "/home/<USER>/workspace/frontend/node_modules/object-assign/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/object-assign/"}, "/home/<USER>/workspace/frontend/node_modules/nth-check/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/nth-check/"}, "/home/<USER>/workspace/frontend/node_modules/normalize-wheel-es/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/normalize-wheel-es/"}, "/home/<USER>/workspace/frontend/node_modules/normalize-range/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/normalize-range/"}, "/home/<USER>/workspace/frontend/node_modules/normalize-path/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/normalize-path/"}, "/home/<USER>/workspace/frontend/node_modules/node-releases/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/node-releases/"}, "/home/<USER>/workspace/frontend/node_modules/node-releases/data/release-schedule/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/node-releases/data/release-schedule/"}, "/home/<USER>/workspace/frontend/node_modules/node-releases/data/processed/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/node-releases/data/processed/"}, "/home/<USER>/workspace/frontend/node_modules/natural-compare/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/natural-compare/"}, "/home/<USER>/workspace/frontend/node_modules/nanoid/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/nanoid/"}, "/home/<USER>/workspace/frontend/node_modules/nanoid/url-alphabet/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/nanoid/url-alphabet/"}, "/home/<USER>/workspace/frontend/node_modules/nanoid/non-secure/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/nanoid/non-secure/"}, "/home/<USER>/workspace/frontend/node_modules/nanoid/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/nanoid/bin/"}, "/home/<USER>/workspace/frontend/node_modules/nanoid/async/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/nanoid/async/"}, "/home/<USER>/workspace/frontend/node_modules/mz/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/mz/"}, "/home/<USER>/workspace/frontend/node_modules/ms/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/ms/"}, "/home/<USER>/workspace/frontend/node_modules/minipass/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/minipass/"}, "/home/<USER>/workspace/frontend/node_modules/minimatch/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/minimatch/"}, "/home/<USER>/workspace/frontend/node_modules/mime-types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/mime-types/"}, "/home/<USER>/workspace/frontend/node_modules/mime-db/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/mime-db/"}, "/home/<USER>/workspace/frontend/node_modules/micromatch/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/micromatch/"}, "/home/<USER>/workspace/frontend/node_modules/merge2/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/merge2/"}, "/home/<USER>/workspace/frontend/node_modules/memoize-one/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/memoize-one/"}, "/home/<USER>/workspace/frontend/node_modules/memoize-one/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/memoize-one/src/"}, "/home/<USER>/workspace/frontend/node_modules/math-intrinsics/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/math-intrinsics/"}, "/home/<USER>/workspace/frontend/node_modules/math-intrinsics/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/math-intrinsics/test/"}, "/home/<USER>/workspace/frontend/node_modules/math-intrinsics/constants/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/math-intrinsics/constants/"}, "/home/<USER>/workspace/frontend/node_modules/math-intrinsics/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/math-intrinsics/.github/"}, "/home/<USER>/workspace/frontend/node_modules/magic-string/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/magic-string/"}, "/home/<USER>/workspace/frontend/node_modules/lru-cache/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/lru-cache/"}, "/home/<USER>/workspace/frontend/node_modules/lodash.merge/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/lodash.merge/"}, "/home/<USER>/workspace/frontend/node_modules/lodash-unified/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/lodash-unified/"}, "/home/<USER>/workspace/frontend/node_modules/lodash-es/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/lodash-es/"}, "/home/<USER>/workspace/frontend/node_modules/lodash/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/lodash/"}, "/home/<USER>/workspace/frontend/node_modules/lodash/fp/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/lodash/fp/"}, "/home/<USER>/workspace/frontend/node_modules/has-symbols/test/shams/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/has-symbols/test/shams/"}, "/home/<USER>/workspace/frontend/node_modules/fast-json-stable-stringify/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-json-stable-stringify/test/"}, "/home/<USER>/workspace/frontend/node_modules/has-symbols/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/has-symbols/test/"}, "/home/<USER>/workspace/frontend/node_modules/has-tostringtag/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/has-tostringtag/test/"}, "/home/<USER>/workspace/frontend/node_modules/escape-html/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/escape-html/"}, "/home/<USER>/workspace/frontend/node_modules/espree/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/espree/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/theme-chalk/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/theme-chalk/"}, "/home/<USER>/workspace/frontend/node_modules/glob-parent/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/glob-parent/"}, "/home/<USER>/workspace/frontend/node_modules/esbuild/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/esbuild/"}, "/home/<USER>/workspace/frontend/node_modules/estraverse/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/estraverse/"}, "/home/<USER>/workspace/frontend/node_modules/has-symbols/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/has-symbols/"}, "/home/<USER>/workspace/frontend/node_modules/esutils/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/esutils/"}, "/home/<USER>/workspace/frontend/node_modules/json-stable-stringify-without-jsonify/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/json-stable-stringify-without-jsonify/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/out/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/out/"}, "/home/<USER>/workspace/frontend/node_modules/fast-deep-equal/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-deep-equal/"}, "/home/<USER>/workspace/frontend/node_modules/flatted/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/flatted/"}, "/home/<USER>/workspace/frontend/node_modules/estree-walker/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/estree-walker/types/"}, "/home/<USER>/workspace/frontend/node_modules/fast-deep-equal/es6/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-deep-equal/es6/"}, "/home/<USER>/workspace/frontend/node_modules/has-tostringtag/test/shams/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/has-tostringtag/test/shams/"}, "/home/<USER>/workspace/frontend/node_modules/fastq/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fastq/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/node_modules/glob-parent/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/node_modules/glob-parent/"}, "/home/<USER>/workspace/frontend/node_modules/entities/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/entities/"}, "/home/<USER>/workspace/frontend/node_modules/fraction.js/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fraction.js/"}, "/home/<USER>/workspace/frontend/node_modules/is-extglob/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/is-extglob/"}, "/home/<USER>/workspace/frontend/node_modules/es-object-atoms/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-object-atoms/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/out/providers/transformers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/out/providers/transformers/"}, "/home/<USER>/workspace/frontend/node_modules/es-set-tostringtag/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-set-tostringtag/"}, "/home/<USER>/workspace/frontend/node_modules/globals/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/globals/"}, "/home/<USER>/workspace/frontend/node_modules/js-yaml/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/js-yaml/bin/"}, "/home/<USER>/workspace/frontend/node_modules/fastq/.github/workflows/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fastq/.github/workflows/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/out/readers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/out/readers/"}, "/home/<USER>/workspace/frontend/node_modules/is-path-inside/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/is-path-inside/"}, "/home/<USER>/workspace/frontend/node_modules/estree-walker/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/estree-walker/"}, "/home/<USER>/workspace/frontend/node_modules/locate-path/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/locate-path/"}, "/home/<USER>/workspace/frontend/node_modules/eslint/messages/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/eslint/messages/"}, "/home/<USER>/workspace/frontend/node_modules/eslint-scope/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/eslint-scope/"}, "/home/<USER>/workspace/frontend/node_modules/foreground-child/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/foreground-child/"}, "/home/<USER>/workspace/frontend/node_modules/escape-string-regexp/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/escape-string-regexp/"}, "/home/<USER>/workspace/frontend/node_modules/emoji-regex/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/emoji-regex/"}, "/home/<USER>/workspace/frontend/node_modules/is-binary-path/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/is-binary-path/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/out/providers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/out/providers/"}, "/home/<USER>/workspace/frontend/node_modules/escalade/sync/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/escalade/sync/"}, "/home/<USER>/workspace/frontend/node_modules/has-tostringtag/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/has-tostringtag/"}, "/home/<USER>/workspace/frontend/node_modules/es-define-property/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-define-property/"}, "/home/<USER>/workspace/frontend/node_modules/get-proto/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/get-proto/"}, "/home/<USER>/workspace/frontend/node_modules/esquery/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/esquery/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/out/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/out/utils/"}, "/home/<USER>/workspace/frontend/node_modules/fast-json-stable-stringify/example/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-json-stable-stringify/example/"}, "/home/<USER>/workspace/frontend/node_modules/ignore/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/ignore/"}, "/home/<USER>/workspace/frontend/node_modules/is-number/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/is-number/"}, "/home/<USER>/workspace/frontend/node_modules/es-errors/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-errors/"}, "/home/<USER>/workspace/frontend/node_modules/isexe/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/isexe/"}, "/home/<USER>/workspace/frontend/node_modules/eslint-plugin-vue/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/eslint-plugin-vue/"}, "/home/<USER>/workspace/frontend/node_modules/json-schema-traverse/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/json-schema-traverse/"}, "/home/<USER>/workspace/frontend/node_modules/file-entry-cache/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/file-entry-cache/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/out/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/out/types/"}, "/home/<USER>/workspace/frontend/node_modules/emoji-regex/es2015/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/emoji-regex/es2015/"}, "/home/<USER>/workspace/frontend/node_modules/function-bind/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/function-bind/"}, "/home/<USER>/workspace/frontend/node_modules/fast-levenshtein/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-levenshtein/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/out/providers/filters/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/out/providers/filters/"}, "/home/<USER>/workspace/frontend/node_modules/is-core-module/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/is-core-module/"}, "/home/<USER>/workspace/frontend/node_modules/js-yaml/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/js-yaml/"}, "/home/<USER>/workspace/frontend/node_modules/eslint-visitor-keys/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/eslint-visitor-keys/"}, "/home/<USER>/workspace/frontend/node_modules/fill-range/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fill-range/"}, "/home/<USER>/workspace/frontend/node_modules/is-core-module/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/is-core-module/test/"}, "/home/<USER>/workspace/frontend/node_modules/json-stable-stringify-without-jsonify/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/json-stable-stringify-without-jsonify/test/"}, "/home/<USER>/workspace/frontend/node_modules/json-schema-traverse/spec/fixtures/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/json-schema-traverse/spec/fixtures/"}, "/home/<USER>/workspace/frontend/node_modules/fastq/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fastq/test/"}, "/home/<USER>/workspace/frontend/node_modules/find-up/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/find-up/"}, "/home/<USER>/workspace/frontend/node_modules/hasown/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/hasown/"}, "/home/<USER>/workspace/frontend/node_modules/import-fresh/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/import-fresh/"}, "/home/<USER>/workspace/frontend/node_modules/flatted/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/flatted/types/"}, "/home/<USER>/workspace/frontend/node_modules/fs.realpath/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fs.realpath/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/out/managers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/out/managers/"}, "/home/<USER>/workspace/frontend/node_modules/is-glob/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/is-glob/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/out/providers/matchers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/out/providers/matchers/"}, "/home/<USER>/workspace/frontend/node_modules/gopd/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/gopd/.github/"}, "/home/<USER>/workspace/frontend/node_modules/fast-json-stable-stringify/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-json-stable-stringify/"}, "/home/<USER>/workspace/frontend/node_modules/flatted/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/flatted/cjs/"}, "/home/<USER>/workspace/frontend/node_modules/form-data/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/form-data/"}, "/home/<USER>/workspace/frontend/node_modules/is-fullwidth-code-point/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/is-fullwidth-code-point/"}, "/home/<USER>/workspace/frontend/node_modules/jiti/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/jiti/"}, "/home/<USER>/workspace/frontend/node_modules/flat-cache/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/flat-cache/"}, "/home/<USER>/workspace/frontend/node_modules/has-tostringtag/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/has-tostringtag/.github/"}, "/home/<USER>/workspace/frontend/node_modules/lines-and-columns/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/lines-and-columns/"}, "/home/<USER>/workspace/frontend/node_modules/inherits/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/inherits/"}, "/home/<USER>/workspace/frontend/node_modules/flat-cache/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/flat-cache/src/"}, "/home/<USER>/workspace/frontend/node_modules/get-intrinsic/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/get-intrinsic/"}, "/home/<USER>/workspace/frontend/node_modules/es-object-atoms/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-object-atoms/test/"}, "/home/<USER>/workspace/frontend/node_modules/json-buffer/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/json-buffer/"}, "/home/<USER>/workspace/frontend/node_modules/eslint/conf/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/eslint/conf/"}, "/home/<USER>/workspace/frontend/node_modules/lilconfig/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/lilconfig/"}, "/home/<USER>/workspace/frontend/node_modules/esrecurse/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/esrecurse/"}, "/home/<USER>/workspace/frontend/node_modules/estree-walker/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/estree-walker/src/"}, "/home/<USER>/workspace/frontend/node_modules/function-bind/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/function-bind/test/"}, "/home/<USER>/workspace/frontend/node_modules/follow-redirects/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/follow-redirects/"}, "/home/<USER>/workspace/frontend/node_modules/es-define-property/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-define-property/test/"}, "/home/<USER>/workspace/frontend/node_modules/fast-json-stable-stringify/benchmark/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-json-stable-stringify/benchmark/"}, "/home/<USER>/workspace/frontend/node_modules/escalade/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/escalade/"}, "/home/<USER>/workspace/frontend/node_modules/gopd/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/gopd/"}, "/home/<USER>/workspace/frontend/node_modules/get-proto/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/get-proto/.github/"}, "/home/<USER>/workspace/frontend/node_modules/glob/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/glob/"}, "/home/<USER>/workspace/frontend/node_modules/imurmurhash/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/imurmurhash/"}, "/home/<USER>/workspace/frontend/node_modules/fast-glob/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-glob/"}, "/home/<USER>/workspace/frontend/node_modules/fastq/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fastq/.github/"}, "/home/<USER>/workspace/frontend/node_modules/has-flag/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/has-flag/"}, "/home/<USER>/workspace/frontend/node_modules/isexe/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/isexe/test/"}, "/home/<USER>/workspace/frontend/node_modules/levn/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/levn/"}, "/home/<USER>/workspace/frontend/node_modules/es-errors/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-errors/test/"}, "/home/<USER>/workspace/frontend/node_modules/keyv/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/keyv/"}, "/home/<USER>/workspace/frontend/node_modules/jiti/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/jiti/bin/"}, "/home/<USER>/workspace/frontend/node_modules/es-set-tostringtag/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-set-tostringtag/test/"}, "/home/<USER>/workspace/frontend/node_modules/keyv/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/keyv/src/"}, "/home/<USER>/workspace/frontend/node_modules/has-symbols/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/has-symbols/.github/"}, "/home/<USER>/workspace/frontend/node_modules/graphemer/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/graphemer/"}, "/home/<USER>/workspace/frontend/node_modules/json-stable-stringify-without-jsonify/example/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/json-stable-stringify-without-jsonify/example/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/"}, "/home/<USER>/workspace/frontend/node_modules/inflight/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/inflight/"}, "/home/<USER>/workspace/frontend/node_modules/function-bind/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/function-bind/.github/"}, "/home/<USER>/workspace/frontend/node_modules/json-schema-traverse/spec/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/json-schema-traverse/spec/"}, "/home/<USER>/workspace/frontend/node_modules/jackspeak/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/jackspeak/"}, "/home/<USER>/workspace/frontend/node_modules/eslint/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/eslint/"}, "/home/<USER>/workspace/frontend/node_modules/lilconfig/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/lilconfig/src/"}, "/home/<USER>/workspace/frontend/node_modules/get-intrinsic/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/get-intrinsic/test/"}, "/home/<USER>/workspace/frontend/node_modules/es-define-property/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-define-property/.github/"}, "/home/<USER>/workspace/frontend/node_modules/get-intrinsic/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/get-intrinsic/.github/"}, "/home/<USER>/workspace/frontend/node_modules/fast-json-stable-stringify/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/fast-json-stable-stringify/.github/"}, "/home/<USER>/workspace/frontend/node_modules/es-errors/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-errors/.github/"}, "/home/<USER>/workspace/frontend/node_modules/es-object-atoms/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/es-object-atoms/.github/"}, "/home/<USER>/workspace/frontend/node_modules/hasown/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/hasown/.github/"}, "/home/<USER>/workspace/frontend/node_modules/eslint/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/eslint/bin/"}, "/home/<USER>/workspace/frontend/node_modules/get-proto/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/get-proto/test/"}, "/home/<USER>/workspace/frontend/node_modules/flatted/php/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/flatted/php/"}, "/home/<USER>/workspace/frontend/node_modules/gopd/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/gopd/test/"}, "/home/<USER>/workspace/frontend/node_modules/flatted/python/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/flatted/python/"}, "/home/<USER>/workspace/frontend/node_modules/json-buffer/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/json-buffer/test/"}, "/home/<USER>/workspace/frontend/node_modules/flatted/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/flatted/esm/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/virtual-list/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/virtual-list/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/locale/lang/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/locale/lang/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/virtual-list/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/virtual-list/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/utils/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/theme-chalk/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/theme-chalk/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree-select/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree-select/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/upload/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/upload/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/constants/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/constants/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree-v2/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree-v2/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree-select/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree-select/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-empty-values/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-empty-values/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-focus/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-focus/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree-v2/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree-v2/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/virtual-list/src/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/virtual-list/src/components/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/utils/dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/utils/dom/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/transfer/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/transfer/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/utils/vue/props/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/utils/vue/props/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/node_modules/@vueuse/core/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/node_modules/@vueuse/core/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/visual-hidden/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/visual-hidden/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/node_modules/@vueuse/metadata/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/node_modules/@vueuse/metadata/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-modal/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-modal/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/utils/vue/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/utils/vue/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/directives/repeat-click/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/directives/repeat-click/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-namespace/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-namespace/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/watermark/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/watermark/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/upload/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/upload/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-draggable/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-draggable/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/virtual-list/src/builders/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/virtual-list/src/builders/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/theme-chalk/src/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/theme-chalk/src/dark/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/directives/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/directives/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/theme-chalk/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/theme-chalk/dark/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-aria/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-aria/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/watermark/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/watermark/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-popper/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-popper/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/virtual-list/src/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/virtual-list/src/hooks/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-calc-input-width/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-calc-input-width/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-focus-controller/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-focus-controller/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/locale/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/locale/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/theme-chalk/src/date-picker/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/theme-chalk/src/date-picker/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-popper-container/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-popper-container/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree/src/model/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree/src/model/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-id/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-id/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-prop/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-prop/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-teleport/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-teleport/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/theme-chalk/src/mixins/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/theme-chalk/src/mixins/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-same-target/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-same-target/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/theme-chalk/src/common/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/theme-chalk/src/common/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-throttle-render/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-throttle-render/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/visual-hidden/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/visual-hidden/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/watermark/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/watermark/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/theme-chalk/src/color/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/theme-chalk/src/color/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/node_modules/@vueuse/shared/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/node_modules/@vueuse/shared/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-ordered-children/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-ordered-children/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/directives/trap-focus/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/directives/trap-focus/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/directives/click-outside/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/directives/click-outside/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-escape-keydown/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-escape-keydown/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-forward-ref/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-forward-ref/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/node_modules/@types/web-bluetooth/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/node_modules/@types/web-bluetooth/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-prevent-global/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-prevent-global/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/virtual-list/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/virtual-list/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-cursor/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-cursor/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree-v2/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree-v2/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-model-toggle/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-model-toggle/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-composition/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-composition/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-lockscreen/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-lockscreen/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/directives/mousewheel/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/directives/mousewheel/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-transition-fallthrough/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-transition-fallthrough/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-floating/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-floating/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-attrs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-attrs/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/upload/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/upload/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree-v2/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree-v2/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-timeout/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-timeout/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tree-select/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tree-select/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/transfer/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/transfer/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-delayed-toggle/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-delayed-toggle/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-size/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-size/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-z-index/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-z-index/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-locale/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-locale/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-deprecated/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-deprecated/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/hooks/use-intermediate-render/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/hooks/use-intermediate-render/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/splitter/src/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/splitter/src/hooks/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/select-v2/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/select-v2/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/space/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/space/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/text/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/text/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table-v2/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table-v2/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tour/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tour/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/teleport/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/teleport/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/scrollbar/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/scrollbar/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/select/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/select/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table-v2/src/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table-v2/src/components/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table/src/table-body/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table/src/table-body/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tooltip/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tooltip/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table-v2/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table-v2/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tooltip-v2/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tooltip-v2/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tabs/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tabs/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/slider/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/slider/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/text/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/text/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/time-picker/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/time-picker/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table-v2/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table-v2/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table-v2/src/renderers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table-v2/src/renderers/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/splitter/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/splitter/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/slider/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/slider/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/teleport/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/teleport/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/transfer/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/transfer/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table/src/store/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table/src/store/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/space/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/space/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table/src/table-footer/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table/src/table-footer/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/time-picker/src/props/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/time-picker/src/props/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/time-select/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/time-select/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/teleport/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/teleport/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/roving-focus-group/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/roving-focus-group/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/select/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/select/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/time-picker/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/time-picker/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tab-pane/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tab-pane/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/steps/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/steps/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/text/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/text/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/time-picker/src/common/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/time-picker/src/common/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tag/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tag/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/time-picker/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/time-picker/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/step/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/step/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/row/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/row/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/skeleton/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/skeleton/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/statistic/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/statistic/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tabs/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tabs/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/space/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/space/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/slot/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/slot/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/scrollbar/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/scrollbar/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tour/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tour/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/time-picker/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/time-picker/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/skeleton/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/skeleton/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/switch/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/switch/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/time-select/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/time-select/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/splitter/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/splitter/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/splitter/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/splitter/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/row/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/row/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/segmented/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/segmented/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tag/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tag/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table/src/table-header/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table/src/table-header/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/steps/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/steps/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/transfer/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/transfer/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table/src/table/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table/src/table/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/segmented/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/segmented/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/roving-focus-group/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/roving-focus-group/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table/src/table-column/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table/src/table-column/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/time-select/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/time-select/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tour/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tour/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/timeline-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/timeline-item/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/slider/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/slider/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/timeline/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/timeline/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tag/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tag/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/splitter-panel/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/splitter-panel/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/timeline/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/timeline/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/skeleton/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/skeleton/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/sub-menu/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/sub-menu/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/select/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/select/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tour-step/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tour-step/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/select-v2/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/select-v2/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/row/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/row/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/skeleton-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/skeleton-item/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tooltip/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tooltip/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table-column/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table-column/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/switch/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/switch/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/timeline/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/timeline/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/slot/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/slot/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/statistic/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/statistic/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/segmented/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/segmented/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/select-v2/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/select-v2/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/steps/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/steps/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/slider/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/slider/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/table-v2/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/table-v2/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/time-picker/src/time-picker-com/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/time-picker/src/time-picker-com/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/statistic/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/statistic/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/switch/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/switch/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tooltip/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tooltip/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tooltip-v2/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tooltip-v2/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/tabs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/tabs/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/scrollbar/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/scrollbar/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/result/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/result/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/result/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/result/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/result/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/result/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/rate/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/rate/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/rate/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/rate/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/rate/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/rate/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/radio-group/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/radio-group/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/radio-button/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/radio-button/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/radio/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/radio/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/radio/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/radio/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/radio/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/radio/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/progress/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/progress/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/progress/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/progress/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/progress/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/progress/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/popper/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/popper/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/popper/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/popper/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/popper/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/popper/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/popper/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/popper/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/popover/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/popover/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/popover/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/popover/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/popover/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/popover/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/popconfirm/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/popconfirm/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/popconfirm/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/popconfirm/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/popconfirm/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/popconfirm/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/pagination/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/pagination/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/pagination/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/pagination/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/pagination/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/pagination/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/pagination/src/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/pagination/src/components/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/page-header/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/page-header/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/page-header/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/page-header/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/page-header/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/page-header/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/overlay/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/overlay/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/overlay/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/overlay/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/overlay/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/overlay/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/option-group/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/option-group/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/option/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/option/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/notification/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/notification/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/notification/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/notification/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/notification/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/notification/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/message-box/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/message-box/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/message-box/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/message-box/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/message-box/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/message-box/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/message/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/message/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/message/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/message/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/message/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/message/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/menu-item-group/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/menu-item-group/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/menu-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/menu-item/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/menu/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/menu/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/menu/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/menu/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/menu/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/menu/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/menu/src/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/menu/src/utils/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/mention/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/mention/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/mention/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/mention/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/mention/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/mention/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/main/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/main/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/loading/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/loading/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/loading/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/loading/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/loading/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/loading/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/link/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/link/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/link/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/link/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/link/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/link/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/input-tag/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/input-tag/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/input-tag/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/input-tag/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/input-tag/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/input-tag/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/input-tag/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/input-tag/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/input-number/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/input-number/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/input-number/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/input-number/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/input-number/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/input-number/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/input/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/input/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/input/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/input/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/input/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/input/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/infinite-scroll/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/infinite-scroll/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/infinite-scroll/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/infinite-scroll/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/infinite-scroll/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/infinite-scroll/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/image-viewer/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/image-viewer/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/image-viewer/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/image-viewer/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/image-viewer/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/image-viewer/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/image/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/image/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/image/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/image/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/image/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/image/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/icon/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/icon/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/icon/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/icon/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/icon/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/icon/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/header/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/header/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/form-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/form-item/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/form/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/form/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/form/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/form/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/form/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/form/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/form/src/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/form/src/hooks/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/footer/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/footer/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/focus-trap/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/focus-trap/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/focus-trap/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/focus-trap/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/empty/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/empty/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/empty/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/empty/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/empty/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/empty/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/dropdown-menu/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/dropdown-menu/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/dropdown-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/dropdown-item/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/dropdown/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/dropdown/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/dropdown/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/dropdown/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/dropdown/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/dropdown/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/drawer/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/drawer/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/drawer/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/drawer/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/drawer/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/drawer/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/divider/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/divider/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/divider/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/divider/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/divider/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/divider/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/dialog/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/dialog/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/dialog/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/dialog/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/dialog/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/dialog/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/descriptions-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/descriptions-item/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/descriptions/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/descriptions/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/descriptions/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/descriptions/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/descriptions/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/descriptions/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/date-picker/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/date-picker/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/date-picker/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/date-picker/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/date-picker/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/date-picker/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/date-picker/src/props/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/date-picker/src/props/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/date-picker/src/date-picker-com/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/date-picker/src/date-picker-com/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/date-picker/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/date-picker/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/countdown/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/countdown/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/countdown/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/countdown/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/countdown/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/countdown/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/container/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/container/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/container/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/container/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/container/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/container/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/config-provider/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/config-provider/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/config-provider/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/config-provider/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/config-provider/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/config-provider/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/config-provider/src/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/config-provider/src/hooks/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/color-picker/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/color-picker/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/color-picker/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/color-picker/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/color-picker/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/color-picker/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/color-picker/src/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/color-picker/src/utils/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/color-picker/src/props/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/color-picker/src/props/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/color-picker/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/color-picker/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/color-picker/src/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/color-picker/src/components/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/collection/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/collection/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/collection/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/collection/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/collapse-transition/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/collapse-transition/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/collapse-transition/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/collapse-transition/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/collapse-transition/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/collapse-transition/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/collapse-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/collapse-item/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/collapse/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/collapse/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/collapse/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/collapse/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/collapse/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/collapse/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/col/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/col/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/col/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/col/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/col/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/col/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/checkbox-group/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/checkbox-group/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/checkbox-button/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/checkbox-button/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/checkbox/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/checkbox/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/checkbox/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/checkbox/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/checkbox/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/checkbox/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/checkbox/src/composables/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/checkbox/src/composables/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/check-tag/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/check-tag/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/check-tag/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/check-tag/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/check-tag/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/check-tag/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/cascader-panel/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/cascader-panel/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/cascader-panel/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/cascader-panel/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/cascader-panel/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/cascader-panel/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/cascader/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/cascader/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/cascader/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/cascader/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/cascader/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/cascader/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/carousel-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/carousel-item/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/carousel/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/carousel/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/carousel/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/carousel/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/carousel/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/carousel/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/card/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/card/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/card/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/card/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/card/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/card/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/calendar/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/calendar/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/calendar/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/calendar/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/calendar/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/calendar/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/button-group/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/button-group/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/button/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/button/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/button/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/button/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/button/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/button/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/breadcrumb-item/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/breadcrumb-item/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/breadcrumb/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/breadcrumb/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/breadcrumb/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/breadcrumb/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/breadcrumb/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/breadcrumb/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/base/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/base/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/badge/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/badge/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/badge/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/badge/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/badge/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/badge/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/backtop/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/backtop/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/backtop/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/backtop/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/backtop/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/backtop/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/avatar/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/avatar/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/avatar/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/avatar/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/avatar/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/avatar/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/autocomplete/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/autocomplete/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/autocomplete/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/autocomplete/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/autocomplete/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/autocomplete/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/aside/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/aside/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/anchor-link/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/anchor-link/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/anchor/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/anchor/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/anchor/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/anchor/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/anchor/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/anchor/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/alert/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/alert/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/alert/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/alert/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/alert/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/alert/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/affix/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/affix/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/affix/style/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/affix/style/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/components/affix/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/components/affix/src/"}, "/home/<USER>/workspace/frontend/node_modules/element-plus/es/_virtual/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/element-plus/es/_virtual/"}, "/home/<USER>/workspace/frontend/node_modules/electron-to-chromium/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/electron-to-chromium/"}, "/home/<USER>/workspace/frontend/node_modules/doctrine/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/doctrine/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/devHelper/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/devHelper/"}, "/home/<USER>/workspace/frontend/node_modules/deep-is/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/deep-is/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/plugin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/plugin/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/locale/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/locale/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/isLeapYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/isLeapYear/"}, "/home/<USER>/workspace/frontend/node_modules/debug/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/debug/"}, "/home/<USER>/workspace/frontend/node_modules/delayed-stream/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/delayed-stream/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/locale/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/locale/"}, "/home/<USER>/workspace/frontend/node_modules/didyoumean/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/didyoumean/"}, "/home/<USER>/workspace/frontend/node_modules/eastasianwidth/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/eastasianwidth/"}, "/home/<USER>/workspace/frontend/node_modules/dunder-proto/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dunder-proto/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/objectSupport/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/objectSupport/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/badMutable/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/badMutable/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/advancedFormat/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/advancedFormat/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/negativeYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/negativeYear/"}, "/home/<USER>/workspace/frontend/node_modules/debug/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/debug/src/"}, "/home/<USER>/workspace/frontend/node_modules/dlv/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dlv/"}, "/home/<USER>/workspace/frontend/node_modules/dunder-proto/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dunder-proto/test/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/weekday/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/weekday/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/isSameOrAfter/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/isSameOrAfter/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/isSameOrBefore/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/isSameOrBefore/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/arraySupport/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/arraySupport/"}, "/home/<USER>/workspace/frontend/node_modules/deep-is/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/deep-is/test/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/weekOfYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/weekOfYear/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/pluralGetSet/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/pluralGetSet/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/isYesterday/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/isYesterday/"}, "/home/<USER>/workspace/frontend/node_modules/deep-is/example/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/deep-is/example/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/localeData/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/localeData/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/toObject/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/toObject/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/relativeTime/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/relativeTime/"}, "/home/<USER>/workspace/frontend/node_modules/dunder-proto/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dunder-proto/.github/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/customParseFormat/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/customParseFormat/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/bigIntSupport/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/bigIntSupport/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/toArray/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/toArray/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/isTomorrow/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/isTomorrow/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/isBetween/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/isBetween/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/localizedFormat/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/localizedFormat/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/minMax/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/minMax/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/isMoment/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/isMoment/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/quarterOfYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/quarterOfYear/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/calendar/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/calendar/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/isoWeeksInYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/isoWeeksInYear/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/utc/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/utc/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/timezone/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/timezone/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/preParsePostFormat/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/preParsePostFormat/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/weekYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/weekYear/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/updateLocale/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/updateLocale/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/duration/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/duration/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/isToday/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/isToday/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/dayOfYear/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/dayOfYear/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/buddhistEra/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/buddhistEra/"}, "/home/<USER>/workspace/frontend/node_modules/dayjs/esm/plugin/isoWeek/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/dayjs/esm/plugin/isoWeek/"}, "/home/<USER>/workspace/frontend/node_modules/caniuse-lite/data/features/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/caniuse-lite/data/features/"}, "/home/<USER>/workspace/frontend/node_modules/caniuse-lite/data/regions/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/caniuse-lite/data/regions/"}, "/home/<USER>/workspace/frontend/node_modules/cross-spawn/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/cross-spawn/"}, "/home/<USER>/workspace/frontend/node_modules/browserslist/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/browserslist/"}, "/home/<USER>/workspace/frontend/node_modules/color-name/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/color-name/"}, "/home/<USER>/workspace/frontend/node_modules/commander/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/commander/"}, "/home/<USER>/workspace/frontend/node_modules/color-convert/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/color-convert/"}, "/home/<USER>/workspace/frontend/node_modules/cssesc/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/cssesc/bin/"}, "/home/<USER>/workspace/frontend/node_modules/csstype/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/csstype/"}, "/home/<USER>/workspace/frontend/node_modules/chalk/source/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/chalk/source/"}, "/home/<USER>/workspace/frontend/node_modules/concat-map/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/concat-map/test/"}, "/home/<USER>/workspace/frontend/node_modules/callsites/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/callsites/"}, "/home/<USER>/workspace/frontend/node_modules/caniuse-lite/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/caniuse-lite/"}, "/home/<USER>/workspace/frontend/node_modules/caniuse-lite/data/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/caniuse-lite/data/"}, "/home/<USER>/workspace/frontend/node_modules/combined-stream/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/combined-stream/"}, "/home/<USER>/workspace/frontend/node_modules/chokidar/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/chokidar/"}, "/home/<USER>/workspace/frontend/node_modules/call-bind-apply-helpers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/call-bind-apply-helpers/"}, "/home/<USER>/workspace/frontend/node_modules/chalk/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/chalk/"}, "/home/<USER>/workspace/frontend/node_modules/concat-map/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/concat-map/"}, "/home/<USER>/workspace/frontend/node_modules/camelcase-css/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/camelcase-css/"}, "/home/<USER>/workspace/frontend/node_modules/chokidar/node_modules/glob-parent/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/chokidar/node_modules/glob-parent/"}, "/home/<USER>/workspace/frontend/node_modules/call-bind-apply-helpers/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/call-bind-apply-helpers/test/"}, "/home/<USER>/workspace/frontend/node_modules/call-bind-apply-helpers/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/call-bind-apply-helpers/.github/"}, "/home/<USER>/workspace/frontend/node_modules/cssesc/man/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/cssesc/man/"}, "/home/<USER>/workspace/frontend/node_modules/commander/typings/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/commander/typings/"}, "/home/<USER>/workspace/frontend/node_modules/cssesc/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/cssesc/"}, "/home/<USER>/workspace/frontend/node_modules/concat-map/example/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/concat-map/example/"}, "/home/<USER>/workspace/frontend/node_modules/chokidar/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/chokidar/types/"}, "/home/<USER>/workspace/frontend/node_modules/@vueuse/metadata/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vueuse/metadata/"}, "/home/<USER>/workspace/frontend/node_modules/axios/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/axios/"}, "/home/<USER>/workspace/frontend/node_modules/@types/lodash/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/lodash/"}, "/home/<USER>/workspace/frontend/node_modules/@types/node/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/node/"}, "/home/<USER>/workspace/frontend/node_modules/asynckit/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/asynckit/"}, "/home/<USER>/workspace/frontend/node_modules/braces/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/braces/"}, "/home/<USER>/workspace/frontend/node_modules/@types/lodash-es/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/lodash-es/"}, "/home/<USER>/workspace/frontend/node_modules/@types/lodash/fp/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/lodash/fp/"}, "/home/<USER>/workspace/frontend/node_modules/acorn-jsx/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/acorn-jsx/"}, "/home/<USER>/workspace/frontend/node_modules/brace-expansion/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/brace-expansion/"}, "/home/<USER>/workspace/frontend/node_modules/any-promise/register/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/any-promise/register/"}, "/home/<USER>/workspace/frontend/node_modules/@vueuse/core/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vueuse/core/"}, "/home/<USER>/workspace/frontend/node_modules/@ungap/structured-clone/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@ungap/structured-clone/"}, "/home/<USER>/workspace/frontend/node_modules/async-validator/dist-types/rule/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/async-validator/dist-types/rule/"}, "/home/<USER>/workspace/frontend/node_modules/@types/node/compatibility/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/node/compatibility/"}, "/home/<USER>/workspace/frontend/node_modules/autoprefixer/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/autoprefixer/bin/"}, "/home/<USER>/workspace/frontend/node_modules/argparse/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/argparse/"}, "/home/<USER>/workspace/frontend/node_modules/boolbase/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/boolbase/"}, "/home/<USER>/workspace/frontend/node_modules/ajv/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/ajv/scripts/"}, "/home/<USER>/workspace/frontend/node_modules/@vueuse/shared/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vueuse/shared/"}, "/home/<USER>/workspace/frontend/node_modules/async-validator/dist-types/validator/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/async-validator/dist-types/validator/"}, "/home/<USER>/workspace/frontend/node_modules/async-validator/dist-types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/async-validator/dist-types/"}, "/home/<USER>/workspace/frontend/node_modules/@vue/runtime-dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vue/runtime-dom/"}, "/home/<USER>/workspace/frontend/node_modules/@vue/reactivity/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vue/reactivity/"}, "/home/<USER>/workspace/frontend/node_modules/@vue/runtime-core/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vue/runtime-core/"}, "/home/<USER>/workspace/frontend/node_modules/async-validator/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/async-validator/"}, "/home/<USER>/workspace/frontend/node_modules/@types/node/fs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/node/fs/"}, "/home/<USER>/workspace/frontend/node_modules/acorn/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/acorn/"}, "/home/<USER>/workspace/frontend/node_modules/any-promise/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/any-promise/"}, "/home/<USER>/workspace/frontend/node_modules/@vue/compiler-core/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vue/compiler-core/"}, "/home/<USER>/workspace/frontend/node_modules/@vitejs/plugin-vue/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vitejs/plugin-vue/"}, "/home/<USER>/workspace/frontend/node_modules/binary-extensions/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/binary-extensions/"}, "/home/<USER>/workspace/frontend/node_modules/balanced-match/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/balanced-match/"}, "/home/<USER>/workspace/frontend/node_modules/@ungap/structured-clone/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@ungap/structured-clone/esm/"}, "/home/<USER>/workspace/frontend/node_modules/autoprefixer/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/autoprefixer/"}, "/home/<USER>/workspace/frontend/node_modules/@types/node/stream/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/node/stream/"}, "/home/<USER>/workspace/frontend/node_modules/@types/web-bluetooth/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/web-bluetooth/"}, "/home/<USER>/workspace/frontend/node_modules/@vue/compiler-dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vue/compiler-dom/"}, "/home/<USER>/workspace/frontend/node_modules/anymatch/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/anymatch/"}, "/home/<USER>/workspace/frontend/node_modules/ansi-styles/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/ansi-styles/"}, "/home/<USER>/workspace/frontend/node_modules/arg/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/arg/"}, "/home/<USER>/workspace/frontend/node_modules/balanced-match/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/balanced-match/.github/"}, "/home/<USER>/workspace/frontend/node_modules/@vue/compiler-sfc/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vue/compiler-sfc/"}, "/home/<USER>/workspace/frontend/node_modules/ansi-regex/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/ansi-regex/"}, "/home/<USER>/workspace/frontend/node_modules/@vue/devtools-api/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vue/devtools-api/"}, "/home/<USER>/workspace/frontend/node_modules/@vue/server-renderer/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vue/server-renderer/"}, "/home/<USER>/workspace/frontend/node_modules/@vue/compiler-ssr/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vue/compiler-ssr/"}, "/home/<USER>/workspace/frontend/node_modules/ajv/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/ajv/"}, "/home/<USER>/workspace/frontend/node_modules/async-validator/dist-web/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/async-validator/dist-web/"}, "/home/<USER>/workspace/frontend/node_modules/@types/node/ts5.6/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/node/ts5.6/"}, "/home/<USER>/workspace/frontend/node_modules/@types/node/readline/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/node/readline/"}, "/home/<USER>/workspace/frontend/node_modules/autoprefixer/data/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/autoprefixer/data/"}, "/home/<USER>/workspace/frontend/node_modules/async-validator/dist-node/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/async-validator/dist-node/"}, "/home/<USER>/workspace/frontend/node_modules/@ungap/structured-clone/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@ungap/structured-clone/cjs/"}, "/home/<USER>/workspace/frontend/node_modules/@types/node/timers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/node/timers/"}, "/home/<USER>/workspace/frontend/node_modules/@vue/shared/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@vue/shared/"}, "/home/<USER>/workspace/frontend/node_modules/acorn/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/acorn/bin/"}, "/home/<USER>/workspace/frontend/node_modules/@ungap/structured-clone/.github/workflows/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@ungap/structured-clone/.github/workflows/"}, "/home/<USER>/workspace/frontend/node_modules/@types/node/dns/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/node/dns/"}, "/home/<USER>/workspace/frontend/node_modules/@types/node/assert/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/node/assert/"}, "/home/<USER>/workspace/blueprints/tickets/": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/tickets/"}, "/home/<USER>/workspace/frontend/node_modules/@humanwhocodes/object-schema/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@humanwhocodes/object-schema/src/"}, "/home/<USER>/workspace/frontend/node_modules/@jridgewell/sourcemap-codec/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/support/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/support/"}, "/home/<USER>/workspace/blueprints/upload/": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/upload/"}, "/home/<USER>/workspace/frontend/node_modules/@eslint/eslintrc/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@eslint/eslintrc/"}, "/home/<USER>/workspace/frontend/node_modules/@types/estree/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/estree/"}, "/home/<USER>/workspace/frontend/node_modules/@floating-ui/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@floating-ui/utils/"}, "/home/<USER>/workspace/blueprints/": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/"}, "/home/<USER>/workspace/frontend/node_modules/@jridgewell/trace-mapping/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/finance/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/finance/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.walk/out/providers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.walk/out/providers/"}, "/home/<USER>/workspace/frontend/node_modules/@types/lodash/common/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@types/lodash/common/"}, "/home/<USER>/workspace/frontend/node_modules/.vite/deps/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/.vite/deps/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/support/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/support/admin/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/admin/finance/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/admin/finance/"}, "/home/<USER>/workspace/frontend/node_modules/@jridgewell/trace-mapping/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/auth/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/auth/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/static/js/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/static/js/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/users/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/users/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/static/js/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/static/js/components/"}, "/home/<USER>/workspace/frontend/node_modules/@eslint-community/eslint-utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@eslint-community/eslint-utils/"}, "/home/<USER>/workspace/frontend/node_modules/@popperjs/core/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@popperjs/core/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.walk/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.walk/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/scripts/"}, "/home/<USER>/workspace/frontend/node_modules/@eslint-community/regexpp/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@eslint-community/regexpp/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.scandir/out/adapters/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.scandir/out/adapters/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/routes/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/routes/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/reading/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/reading/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/static/js/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/static/js/admin/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/payment/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/payment/"}, "/home/<USER>/workspace/blueprints/payment/": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/payment/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/static/css/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/static/css/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.scandir/out/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.scandir/out/"}, "/home/<USER>/workspace/frontend/node_modules/@humanwhocodes/module-importer/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@humanwhocodes/module-importer/src/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/articles/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/articles/"}, "/home/<USER>/workspace/frontend/node_modules/@floating-ui/utils/dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@floating-ui/utils/dom/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.walk/out/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.walk/out/types/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/auth/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/auth/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/email/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/email/"}, "/home/<USER>/workspace/frontend/node_modules/@jridgewell/gen-mapping/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/admin/users/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/admin/users/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/admin/"}, "/home/<USER>/workspace/frontend/node_modules/@jridgewell/sourcemap-codec/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/src/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.walk/out/readers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.walk/out/readers/"}, "/home/<USER>/workspace/blueprints/reading/": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/reading/"}, "/home/<USER>/workspace/blueprints/admin_folder_backup/": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/admin_folder_backup/"}, "/home/<USER>/workspace/frontend/node_modules/@humanwhocodes/object-schema/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@humanwhocodes/object-schema/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.stat/out/providers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.stat/out/providers/"}, "/home/<USER>/workspace/blueprints/api/": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/api/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/error_report/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/error_report/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/email/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/email/"}, "/home/<USER>/workspace/frontend/node_modules/@jridgewell/trace-mapping/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/"}, "/home/<USER>/workspace/frontend/node_modules/@eslint/js/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@eslint/js/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.scandir/out/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.scandir/out/utils/"}, "/home/<USER>/workspace/frontend/node_modules/@isaacs/cliui/node_modules/strip-ansi/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@isaacs/cliui/node_modules/strip-ansi/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/support/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/support/admin/"}, "/home/<USER>/workspace/frontend/node_modules/@babel/helper-string-parser/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@babel/helper-string-parser/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/admin/readers/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/admin/readers/"}, "/home/<USER>/workspace/frontend/node_modules/@alloc/quick-lru/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@alloc/quick-lru/"}, "/home/<USER>/workspace/frontend/node_modules/@pkgjs/parseargs/internal/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@pkgjs/parseargs/internal/"}, "/home/<USER>/workspace/frontend/node_modules/@humanwhocodes/module-importer/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@humanwhocodes/module-importer/"}, "/home/<USER>/workspace/frontend/node_modules/@babel/parser/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@babel/parser/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/static/js/admin/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/static/js/admin/components/"}, "/home/<USER>/workspace/frontend/node_modules/@rollup/rollup-linux-x64-gnu/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@rollup/rollup-linux-x64-gnu/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/static/js/admin/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/static/js/admin/hooks/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/announcement/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/announcement/admin/"}, "/home/<USER>/workspace/frontend/node_modules/@eslint/js/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@eslint/js/src/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/announcement/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/announcement/admin/"}, "/home/<USER>/workspace/frontend/node_modules/@pkgjs/parseargs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@pkgjs/parseargs/"}, "/home/<USER>/workspace/frontend/node_modules/@pkgjs/parseargs/examples/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@pkgjs/parseargs/examples/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/subscription/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/subscription/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.stat/out/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.stat/out/"}, "/home/<USER>/workspace/frontend/node_modules/@humanwhocodes/config-array/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@humanwhocodes/config-array/"}, "/home/<USER>/workspace/frontend/node_modules/@esbuild/linux-x64/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@esbuild/linux-x64/"}, "/home/<USER>/workspace/frontend/node_modules/@isaacs/cliui/node_modules/ansi-regex/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@isaacs/cliui/node_modules/ansi-regex/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/admin/articles/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/admin/articles/"}, "/home/<USER>/workspace/frontend/node_modules/@babel/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@babel/types/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/authors/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/authors/"}, "/home/<USER>/workspace/attached_assets/test/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/test/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/article/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/article/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/article/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/article/"}, "/home/<USER>/workspace/frontend/node_modules/@isaacs/cliui/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@isaacs/cliui/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/readers/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/readers/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/announcement/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/announcement/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.scandir/out/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.scandir/out/types/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/static/js/admin/views/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/static/js/admin/views/"}, "/home/<USER>/workspace/frontend/node_modules/@jridgewell/gen-mapping/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.walk/out/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.walk/out/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.scandir/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.scandir/"}, "/home/<USER>/workspace/frontend/node_modules/@ctrl/tinycolor/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@ctrl/tinycolor/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/admin/orders/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/admin/orders/"}, "/home/<USER>/workspace/frontend/node_modules/@jridgewell/resolve-uri/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/"}, "/home/<USER>/workspace/frontend/node_modules/@eslint/eslintrc/conf/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@eslint/eslintrc/conf/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.stat/out/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.stat/out/types/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/examples/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/examples/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/examples/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/examples/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/utils/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/subscription/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/subscription/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.scandir/out/providers/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.scandir/out/providers/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/reading/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/reading/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.stat/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.stat/"}, "/home/<USER>/workspace/frontend/node_modules/@babel/helper-validator-identifier/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@babel/helper-validator-identifier/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/error_report/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/error_report/admin/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/support/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/support/"}, "/home/<USER>/workspace/frontend/node_modules/@jridgewell/sourcemap-codec/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/payment/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/payment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-global-state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-global-state/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/error_report/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/error_report/admin/"}, "/home/<USER>/workspace/frontend/node_modules/@floating-ui/core/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@floating-ui/core/"}, "/home/<USER>/workspace/frontend/node_modules/@floating-ui/dom/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@floating-ui/dom/"}, "/home/<USER>/workspace/frontend/node_modules/@jridgewell/gen-mapping/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/src/"}, "/home/<USER>/workspace/blueprints/auth/": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/auth/"}, "/home/<USER>/workspace/frontend/node_modules/@eslint/js/src/configs/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@eslint/js/src/configs/"}, "/home/<USER>/workspace/attached_assets/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/announcement/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/announcement/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/error_report/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/error_report/"}, "/home/<USER>/workspace/frontend/node_modules/@nodelib/fs.stat/out/adapters/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@nodelib/fs.stat/out/adapters/"}, "/home/<USER>/workspace/frontend/node_modules/@babel/parser/typings/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@babel/parser/typings/"}, "/home/<USER>/workspace/frontend/node_modules/@element-plus/icons-vue/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@element-plus/icons-vue/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/orders/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/templatesPast01/admin/orders/"}, "/home/<USER>/workspace/frontend/node_modules/@babel/parser/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/node_modules/@babel/parser/bin/"}, "/home/<USER>/workspace/attached_assets/templatesPast01/admin/authors/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/templatesPast01/admin/authors/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/migrations/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/migrations/"}, "/home/<USER>/workspace/attached_assets/Tarotsniff/Tarotsniff/.cursor/rules/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Tarotsniff/Tarotsniff/.cursor/rules/"}, "/home/<USER>/workspace/attached_assets/TarotReader/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/"}, "/home/<USER>/workspace/attached_assets/TarotReader/文档/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/文档/"}, "/home/<USER>/workspace/attached_assets/TarotReader/文档/08-历史对话疑难杂症/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/文档/08-历史对话疑难杂症/"}, "/home/<USER>/workspace/attached_assets/TarotReader/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/utils/"}, "/home/<USER>/workspace/attached_assets/TarotReader/templates/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/templates/"}, "/home/<USER>/workspace/attached_assets/TarotReader/templates/auth/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/templates/auth/"}, "/home/<USER>/workspace/attached_assets/TarotReader/templates/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/templates/admin/"}, "/home/<USER>/workspace/attached_assets/TarotReader/tarot_uploader/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/tarot_uploader/"}, "/home/<USER>/workspace/attached_assets/TarotReader/tarot_uploader/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/tarot_uploader/scripts/"}, "/home/<USER>/workspace/attached_assets/TarotReader/static/uploads/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/static/uploads/"}, "/home/<USER>/workspace/attached_assets/TarotReader/static/js/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/static/js/"}, "/home/<USER>/workspace/attached_assets/TarotReader/static/img/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/static/img/"}, "/home/<USER>/workspace/attached_assets/TarotReader/static/css/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/static/css/"}, "/home/<USER>/workspace/attached_assets/TarotReader/blueprints/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/blueprints/"}, "/home/<USER>/workspace/attached_assets/TarotReader/backups/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/backups/"}, "/home/<USER>/workspace/attached_assets/TarotReader/attached_assets/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/attached_assets/"}, "/home/<USER>/workspace/attached_assets/TarotReader/attached_assets/orders/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/attached_assets/orders/"}, "/home/<USER>/workspace/attached_assets/TarotReader/attached_assets/migrations/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/attached_assets/migrations/"}, "/home/<USER>/workspace/attached_assets/TarotReader/attached_assets/finance/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/attached_assets/finance/"}, "/home/<USER>/workspace/attached_assets/TarotReader/attached_assets/authors/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/attached_assets/authors/"}, "/home/<USER>/workspace/attached_assets/TarotReader/attached_assets/announcement/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/attached_assets/announcement/"}, "/home/<USER>/workspace/attached_assets/TarotReader/attached_assets/announcement/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/attached_assets/announcement/admin/"}, "/home/<USER>/workspace/attached_assets/TarotReader/attached_assets/.cursor/rules/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/attached_assets/.cursor/rules/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/文档/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/文档/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/文档/08-历史对话疑难杂症/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/文档/08-历史对话疑难杂症/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/utils/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/test_uploads/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/test_uploads/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/test_simple/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/test_simple/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/templates/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/templates/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/templates/auth/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/templates/auth/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/templates/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/templates/admin/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/static/uploads/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/static/uploads/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/static/js/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/static/js/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/static/img/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/static/img/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/static/css/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/static/css/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/blueprints/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/blueprints/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/backups/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/backups/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/attached_assets/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/attached_assets/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/attached_assets/orders/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/attached_assets/orders/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/attached_assets/migrations/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/attached_assets/migrations/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/attached_assets/finance/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/attached_assets/finance/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/attached_assets/authors/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/attached_assets/authors/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/attached_assets/announcement/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/attached_assets/announcement/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/attached_assets/announcement/admin/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/attached_assets/announcement/admin/"}, "/home/<USER>/workspace/attached_assets/TarotReader/TarotReader/attached_assets/.cursor/rules/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/TarotReader/TarotReader/attached_assets/.cursor/rules/"}, "/home/<USER>/workspace/api/v1/": {"rootPath": "/home/<USER>/workspace", "relPath": "api/v1/"}, "/home/<USER>/workspace/admin-vue/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/"}, "/home/<USER>/workspace/admin-vue/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/"}, "/home/<USER>/workspace/admin-vue/src/views/users/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/users/components/"}, "/home/<USER>/workspace/admin-vue/src/views/uploader/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/uploader/"}, "/home/<USER>/workspace/admin-vue/src/views/tickets/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/tickets/components/"}, "/home/<USER>/workspace/admin-vue/src/views/tags/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/tags/components/"}, "/home/<USER>/workspace/admin-vue/src/views/statistics/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/statistics/"}, "/home/<USER>/workspace/admin-vue/src/views/settings/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/settings/"}, "/home/<USER>/workspace/admin-vue/src/views/points/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/points/"}, "/home/<USER>/workspace/admin-vue/src/views/points/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/points/components/"}, "/home/<USER>/workspace/admin-vue/src/views/finance/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/finance/"}, "/home/<USER>/workspace/admin-vue/src/views/error/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/error/"}, "/home/<USER>/workspace/admin-vue/src/views/dashboard/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/dashboard/"}, "/home/<USER>/workspace/admin-vue/src/views/authors/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/authors/components/"}, "/home/<USER>/workspace/admin-vue/src/views/auth/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/auth/"}, "/home/<USER>/workspace/admin-vue/src/views/announcements/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/announcements/"}, "/home/<USER>/workspace/admin-vue/src/views/announcements/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/announcements/components/"}, "/home/<USER>/workspace/admin-vue/src/styles/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/styles/"}, "/home/<USER>/workspace/admin-vue/src/layouts/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/layouts/"}, "/home/<USER>/workspace/admin-vue/src/components/common/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/components/common/"}, "/home/<USER>/workspace/admin-vue/src/api/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/api/"}, "/home/<USER>/workspace/.local/state/replit/agent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/"}, "/home/<USER>/workspace/.local/share/": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/share/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/assets/vscode-material-icons/icons/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/assets/vscode-material-icons/icons/"}, "/home/<USER>/workspace/attached_assets/AugmentCode-Free-v1.0.6-Portable/gui_qt6/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/AugmentCode-Free-v1.0.6-Portable/gui_qt6/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/"}, "/home/<USER>/workspace/admin-vue/src/views/users/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/users/"}, "/home/<USER>/workspace/attached_assets/AugmentCode-Free-v1.0.6-Portable/config/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/AugmentCode-Free-v1.0.6-Portable/config/"}, "/home/<USER>/workspace/attached_assets/AugmentCode-Free-v1.0.6-Portable/augment_tools_core/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/AugmentCode-Free-v1.0.6-Portable/augment_tools_core/"}, "/home/<USER>/workspace/admin-vue/src/views/articles/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/articles/"}, "/home/<USER>/workspace/.config/.vscode-server/data/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/integrations/theme/default-themes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/integrations/theme/default-themes/"}, "/home/<USER>/workspace/admin-vue/src/stores/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/stores/"}, "/home/<USER>/workspace/admin-vue/src/views/tags/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/tags/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/assets/vscode-material-icons/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/assets/vscode-material-icons/"}, "/home/<USER>/workspace/attached_assets/AugmentCode-Free-v1.0.6-Portable/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/AugmentCode-Free-v1.0.6-Portable/"}, "/home/<USER>/workspace/admin-vue/src/views/tickets/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/tickets/"}, "/home/<USER>/workspace/admin-vue/src/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/types/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/"}, "/home/<USER>/workspace/admin-vue/src/views/authors/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/authors/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/"}, "/home/<USER>/workspace/admin-vue/src/router/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/router/"}, "/home/<USER>/workspace/attached_assets/AugmentCode-Free-v1.0.6-Portable/languages/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/AugmentCode-Free-v1.0.6-Portable/languages/"}, "/home/<USER>/workspace/admin-vue/src/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/utils/"}, "/home/<USER>/workspace/.upm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".upm/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/include/leveldb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/include/leveldb/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/util/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/snappy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/snappy/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/db/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/db/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/util/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/port-libuv/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/port-libuv/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/linux/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/linux/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/mac/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/mac/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/solaris/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/solaris/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/db/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/db/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/helpers/memenv/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/helpers/memenv/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/port/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/port/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/table/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/table/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/helpers/memenv/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/helpers/memenv/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/assets/codicons/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/assets/codicons/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/openbsd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/openbsd/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/table/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/table/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/node-gyp-build@4.8.4/node_modules/node-gyp-build/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/node-gyp-build@4.8.4/node_modules/node-gyp-build/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/solaris/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/solaris/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/port-libuv/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/port-libuv/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/maybe-combine-errors@1.0.0/node_modules/maybe-combine-errors/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/maybe-combine-errors@1.0.0/node_modules/maybe-combine-errors/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/snappy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/snappy/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/snappy/cmake/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/snappy/cmake/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/include/leveldb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/include/leveldb/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/module-error@1.0.2/node_modules/module-error/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/module-error@1.0.2/node_modules/module-error/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/level/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/level/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/freebsd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/freebsd/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/snappy/cmake/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/snappy/cmake/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/assets/icons/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/assets/icons/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/browser-level/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/browser-level/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/port/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/port/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/level/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/level/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/linux/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/linux/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/patches/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/patches/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/win32/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/win32/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/issues/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/issues/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/freebsd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/freebsd/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/port/win/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/port/win/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/issues/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/leveldb/leveldb-1.20/issues/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/openbsd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/openbsd/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/mac/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/mac/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/assets/images/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.7/assets/images/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/win32/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/classic-level/deps/snappy/win32/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/patches/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/patches/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/napi-macros@2.2.2/node_modules/napi-macros/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/napi-macros@2.2.2/node_modules/napi-macros/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/snappy/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/browser-level/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/browser-level/util/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/types/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/test/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/test/traits/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/test/traits/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/test/self/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/test/self/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/port/win/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/runtime/deps/leveldb/leveldb-1.20/port/win/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/test/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/test/hooks/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/test/events/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level@10.0.0/node_modules/abstract-level/test/events/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level-transcoder@1.0.1/node_modules/module-error/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level-transcoder@1.0.1/node_modules/module-error/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level-transcoder@1.0.1/node_modules/level-transcoder/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level-transcoder@1.0.1/node_modules/level-transcoder/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level-transcoder@1.0.1/node_modules/buffer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level-transcoder@1.0.1/node_modules/buffer/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level-supports@6.2.0/node_modules/level-supports/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level-supports@6.2.0/node_modules/level-supports/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level-supports@6.2.0/node_modules/level-supports/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/level-supports@6.2.0/node_modules/level-supports/test/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/is-buffer@2.0.5/node_modules/is-buffer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/is-buffer@2.0.5/node_modules/is-buffer/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/ieee754@1.2.1/node_modules/ieee754/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/ieee754@1.2.1/node_modules/ieee754/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/node-gyp-build/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/node-gyp-build/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/napi-macros/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/napi-macros/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/module-error/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/module-error/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/types/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/test/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/test/traits/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/test/traits/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/test/self/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/test/self/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/test/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/test/hooks/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/test/events/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/abstract-level/test/events/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/buffer@6.0.3/node_modules/ieee754/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/buffer@6.0.3/node_modules/ieee754/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/buffer@6.0.3/node_modules/buffer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/buffer@6.0.3/node_modules/buffer/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/buffer@6.0.3/node_modules/base64-js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/buffer@6.0.3/node_modules/base64-js/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/browser-level/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/browser-level/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/browser-level/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/browser-level/util/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/types/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/test/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/test/traits/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/test/traits/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/test/self/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/test/self/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/test/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/test/hooks/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/test/events/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/browser-level@3.0.0/node_modules/abstract-level/test/events/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/base64-js@1.5.1/node_modules/base64-js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/base64-js@1.5.1/node_modules/base64-js/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/module-error/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/module-error/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/maybe-combine-errors/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/maybe-combine-errors/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/level-transcoder/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/level-transcoder/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/level-supports/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/level-supports/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/level-supports/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/level-supports/test/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/is-buffer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/is-buffer/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/buffer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/buffer/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/types/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/test/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/test/traits/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/test/traits/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/test/self/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/test/self/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/test/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/test/hooks/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/test/events/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/out/node_modules/.aspect_rules_js/abstract-level@3.1.0/node_modules/abstract-level/test/events/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/media/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/media/next-edit/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/media/next-edit/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/common-webviews/assets/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/common-webviews/assets/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/61b7f4e8/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/61b7f4e8/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/media/keyboard/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/media/keyboard/dark/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/a8e7410/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/a8e7410/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/65e415be/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/65e415be/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/media/keyboard/light/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/media/keyboard/light/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/71dd8a79/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/71dd8a79/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6b33bcc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6b33bcc/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/756ff82d/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/756ff82d/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/76ca786a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/76ca786a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/7dbb22de/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/7dbb22de/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/785ba94/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/785ba94/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6d07a70c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6d07a70c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/fb43638/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/fb43638/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5e33c2f9/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5e33c2f9/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/61c7714d/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/61c7714d/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/df81e13/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/df81e13/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/common-webviews/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/common-webviews/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/f2a9ca/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/f2a9ca/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6b276b2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6b276b2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/69ea3ed9/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/69ea3ed9/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/faa360e/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/faa360e/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/7ad369f9/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/7ad369f9/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6d46a4c8/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6d46a4c8/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/b9da829/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/b9da829/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/8e28e85/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/8e28e85/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/ead33f2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/ead33f2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/7ead3f71/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/7ead3f71/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6f11621a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6f11621a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6be46575/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6be46575/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/7c77fe95/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/7c77fe95/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/42198ab90b9b232d6a97395a012874ee/Augment.vscode-augment/augment-global-state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/42198ab90b9b232d6a97395a012874ee/Augment.vscode-augment/augment-global-state/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/8d8e422/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/8d8e422/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.513.0/media/keyboard/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.513.0/media/keyboard/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/42198ab90b9b232d6a97395a012874ee/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/42198ab90b9b232d6a97395a012874ee/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/rooveterinaryinc.roo-cline/tasks/10de6789-af38-4e30-87f9-1c3857f1a356/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/rooveterinaryinc.roo-cline/tasks/10de6789-af38-4e30-87f9-1c3857f1a356/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/rooveterinaryinc.roo-cline/settings/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/rooveterinaryinc.roo-cline/settings/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/rooveterinaryinc.roo-cline/cache/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/rooveterinaryinc.roo-cline/cache/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5cf199a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5cf199a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5adddea8/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5adddea8/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5ad06433/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5ad06433/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5a9eabb0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5a9eabb0/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/58014982/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/58014982/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/52d757bb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/52d757bb/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/51e11c32/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/51e11c32/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5196bf08/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5196bf08/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4f5693dd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4f5693dd/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4e7e39e8/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4e7e39e8/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4ddfd66e/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4ddfd66e/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4cca3702/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4cca3702/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4bd4cce1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4bd4cce1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/461a08b0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/461a08b0/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/438bc4ed/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/438bc4ed/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/3c27e09c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/3c27e09c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/3b5b6df7/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/3b5b6df7/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/3af6ce37/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/3af6ce37/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/352e66b9/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/352e66b9/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/30bd8f50/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/30bd8f50/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2f467dd7/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2f467dd7/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2f460978/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2f460978/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2f4520ba/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2f4520ba/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2f44ac5b/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2f44ac5b/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2f4437fc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2f4437fc/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2db9a5e0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2db9a5e0/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2dae4899/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2dae4899/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2cd8fce9/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2cd8fce9/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2bddb557/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2bddb557/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/297fcb70/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/297fcb70/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/24d5e0f7/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/24d5e0f7/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/22f0ee6a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/22f0ee6a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1f7f3f8f/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1f7f3f8f/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1b5d496a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1b5d496a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1ab1004d/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1ab1004d/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/19826c86/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/19826c86/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/373690eb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/373690eb/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1689b0e0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1689b0e0/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1615587f/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1615587f/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/151a1f7a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/151a1f7a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/14c2ba76/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/14c2ba76/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-e926682/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-e926682/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-ceb1e2a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-ceb1e2a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-b579e84/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-b579e84/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7f8a3c42/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7f8a3c42/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7d3219a1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7d3219a1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-76f6dd8c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-76f6dd8c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-75d184b8/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-75d184b8/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-755d3cd8/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-755d3cd8/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-712f975f/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-712f975f/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-6c6f8e48/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-6c6f8e48/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-6666fa67/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-6666fa67/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-64922b6f/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-64922b6f/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-63460264/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-63460264/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-6216c5a2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-6216c5a2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-6193b9a3/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-6193b9a3/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-612f5141/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-612f5141/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5ecd027/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5ecd027/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5d21868a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5d21868a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5aab4fb3/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5aab4fb3/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5a22ec7a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5a22ec7a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-58fca512/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-58fca512/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5848555b/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5848555b/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-573540f8/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-573540f8/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-537cb3b/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-537cb3b/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5332bef7/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5332bef7/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-66df48b4/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-66df48b4/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5318fce2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5318fce2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-52820c2d/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-52820c2d/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5248a37/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5248a37/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-51b9354e/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-51b9354e/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4ff289c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4ff289c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4fde69ef/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4fde69ef/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4af9ad1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4af9ad1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4a1310fc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4a1310fc/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4a0d62e6/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4a0d62e6/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4a07aa11/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4a07aa11/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-49b37b19/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-49b37b19/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4755965a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4755965a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-43dc1d4a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-43dc1d4a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-434aec5e/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-434aec5e/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4f605586/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4f605586/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4e6d16e1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4e6d16e1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4e49bc95/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4e49bc95/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3e2e5f1c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3e2e5f1c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3d655ec/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3d655ec/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3b1de67c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3b1de67c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-38ace3b3/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-38ace3b3/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3697a6bc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3697a6bc/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-35d58a08/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-35d58a08/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3234bbf7/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3234bbf7/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2b4a2c32/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2b4a2c32/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-29edc5f/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-29edc5f/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-27db9497/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-27db9497/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-26f8ccf4/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-26f8ccf4/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2554e9bd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2554e9bd/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-23abdb55/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-23abdb55/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-22f23d1c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-22f23d1c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2260b78a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2260b78a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-225f4874/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-225f4874/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-201229f9/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-201229f9/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-1e8eab6c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-1e8eab6c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-1a9ad366/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-1a9ad366/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-1a1df88f/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-1a1df88f/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-180fd136/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-180fd136/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-13a03504/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-13a03504/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-12d2a658/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-12d2a658/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-121dd35a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-121dd35a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/CachedProfilesData/__default__profile__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/CachedProfilesData/__default__profile__/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/wrappy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/wrappy/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vsda/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vsda/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vsda/rust/web/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vsda/rust/web/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-textmate/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-textmate/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-regexpp/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-regexpp/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-regexp-languagedetection/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-regexp-languagedetection/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-oniguruma/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-oniguruma/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-oniguruma/release/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-oniguruma/release/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/uuid/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/uuid/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/util-deprecate/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/util-deprecate/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/universalify/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/universalify/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/undici/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/undici/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/undici/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/undici/scripts/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/tunnel-agent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/tunnel-agent/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/to-regex-range/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/to-regex-range/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/tiny-inflate/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/tiny-inflate/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/tas-client-umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/tas-client-umd/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/tar-stream/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/tar-stream/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/tar-fs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/tar-fs/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/strip-json-comments/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/strip-json-comments/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/string_decoder/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/string_decoder/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/sprintf-js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/sprintf-js/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/sprintf-js/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/sprintf-js/src/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/socks-proxy-agent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/socks-proxy-agent/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/socks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/socks/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/smart-buffer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/smart-buffer/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/simple-get/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/simple-get/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/simple-concat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/simple-concat/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/ranges/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/ranges/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/internal/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/internal/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/functions/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/functions/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/classes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/classes/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/semver/bin/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/safe-buffer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/safe-buffer/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/readable-stream/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/readable-stream/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/rc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/rc/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/fs-constants/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/fs-constants/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/jsbn/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/jsbn/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/ms/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/ms/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/pump/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/pump/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/tree-sitter-wasm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/tree-sitter-wasm/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/dynamicproto-js/tools/rollup/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/dynamicproto-js/tools/rollup/esm/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/opentype.js/src/tables/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/opentype.js/src/tables/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@parcel/watcher/node_modules/detect-libc/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@parcel/watcher/node_modules/detect-libc/bin/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/types/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/dynamicproto-js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/dynamicproto-js/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/jschardet/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/jschardet/scripts/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-core-js/dist-esm/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-core-js/dist-esm/src/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/opentype.js/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/opentype.js/src/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/buffer-crc32/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/buffer-crc32/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/spdlog/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/spdlog/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-post-js/bundle/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-post-js/bundle/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-core-js/bundle/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-core-js/bundle/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/ieee754/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/ieee754/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK.Interfaces/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK.Interfaces/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/deviceid/azure-pipelines/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/deviceid/azure-pipelines/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/vsce-sign/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/vsce-sign/src/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/font-finder/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/font-finder/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/get-system-fonts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/get-system-fonts/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/references-view/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/references-view/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/buffer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/buffer/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/cookie/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/cookie/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/merge-conflict/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/merge-conflict/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/debug/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/debug/src/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-post-js/dist-esm/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-post-js/dist-esm/src/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/debug/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/debug/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/graceful-fs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/graceful-fs/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/bl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/bl/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/is-extglob/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/is-extglob/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/end-of-stream/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/end-of-stream/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK.Enums/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK.Enums/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/dist-esm/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-post-js/dist-esm/src/typings/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-post-js/dist-esm/src/typings/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/http-proxy-agent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/http-proxy-agent/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-shims/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-shims/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/iconv-lite-umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/iconv-lite-umd/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/bindings/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/bindings/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/php-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/php-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/minimist/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/minimist/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/images/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/images/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/proxy-from-env/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/proxy-from-env/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-image/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-image/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/font-ligatures/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/font-ligatures/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@parcel/watcher/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@parcel/watcher/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/ripgrep/node_modules/yauzl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/ripgrep/node_modules/yauzl/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-search/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-search/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/picomatch/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/picomatch/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/lru-cache/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/lru-cache/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/yallist/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/yallist/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-ligatures/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-ligatures/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/micromatch/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/micromatch/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/file-uri-to-path/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/file-uri-to-path/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/yazl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/yazl/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/pend/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/pend/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/braces/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/braces/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/expand-template/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/expand-template/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/https-proxy-agent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/https-proxy-agent/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/once/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/once/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/fs-extra/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/fs-extra/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/ui/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/ui/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/node_modules/typescript/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/node_modules/typescript/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/jsonfile/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/jsonfile/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-core-js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-core-js/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/ip-address/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/ip-address/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/kerberos/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/kerberos/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/is-number/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/is-number/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@parcel/watcher/node_modules/detect-libc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@parcel/watcher/node_modules/detect-libc/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/xterm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/xterm/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/promise-stream-reader/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/promise-stream-reader/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/opentype.js/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/opentype.js/bin/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/spdlog/azure-pipelines/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/spdlog/azure-pipelines/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/fill-range/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/fill-range/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/dynamicproto-js/tools/rollup/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/dynamicproto-js/tools/rollup/node/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/fd-slicer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/fd-slicer/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/github-from-package/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/github-from-package/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/deviceid/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/deviceid/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/jschardet/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/jschardet/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-progress/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-progress/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/mkdirp/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/mkdirp/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/vendor/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/vendor/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/is-glob/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/is-glob/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/native-watchdog/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/native-watchdog/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-post-js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/1ds-post-js/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/proxy-agent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/proxy-agent/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-clipboard/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-clipboard/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/napi-build-utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/napi-build-utils/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@tootallnate/once/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@tootallnate/once/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/detect-libc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/detect-libc/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-textmate/release/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/vscode-textmate/release/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/node-pty/node-addon-api/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/node-pty/node-addon-api/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-webgl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-webgl/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@parcel/watcher/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@parcel/watcher/scripts/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/vsce-sign/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/vsce-sign/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/targets/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/targets/node/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/js-base64/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/js-base64/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/node-abi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/node-abi/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/ripgrep/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/ripgrep/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/mkdirp-classic/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/mkdirp-classic/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-unicode11/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-unicode11/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/node-abi/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/node-abi/scripts/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/inherits/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/inherits/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-shims/dist-esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-shims/dist-esm/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-shims/browser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-shims/browser/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/base64-js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/base64-js/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/decompress-response/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/decompress-response/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/opentype.js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/opentype.js/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-image/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-image/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/browser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@microsoft/applicationinsights-core-js/browser/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/files/node/watcher/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/files/node/watcher/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/vscode-languagedetection/model/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/vscode-languagedetection/model/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/vscode-languagedetection/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/vscode-languagedetection/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/vscode-languagedetection/cli/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/vscode-languagedetection/cli/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/chownr/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/chownr/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-serialize/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/addon-serialize/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/xterm/css/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/xterm/css/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/deep-extend/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/deep-extend/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/ini/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/ini/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/node-pty/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/node-pty/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/proxy-agent/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/proxy-agent/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/base/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/base/node/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/yauzl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/yauzl/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-176dd5b1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-176dd5b1/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/mimic-response/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/mimic-response/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/headless/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/headless/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/mkdirp/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/mkdirp/bin/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/tree-sitter-wasm/wasm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@vscode/tree-sitter-wasm/wasm/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/opentype.js/externs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/opentype.js/externs/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/agent-base/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/agent-base/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/preview-styles/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/preview-styles/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/notebook-out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/notebook-out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/jake/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/jake/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/notebook-out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/notebook-out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/gulp/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/gulp/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/grunt/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/grunt/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/languages/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/languages/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/remote-cli/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/remote-cli/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/helpers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/helpers/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/task-storage/tasks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/task-storage/tasks/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/task-storage/manifest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/task-storage/manifest/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-kv-store/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-kv-store/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/headless/lib-headless/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node_modules/@xterm/headless/lib-headless/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/notebook-out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/notebook-out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/extension-editing/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/extension-editing/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/emmet/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/emmet/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-server-ready/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-server-ready/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/agent-edits/shards/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/agent-edits/shards/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/checkpoint-documents/9789af72-6be7-4be2-b3c7-3985c90164d3/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/checkpoint-documents/9789af72-6be7-4be2-b3c7-3985c90164d3/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2f46f236/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2f46f236/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/assets/vscode-material-icons/icons/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/assets/vscode-material-icons/icons/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/integrations/theme/default-themes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/integrations/theme/default-themes/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/assets/vscode-material-icons/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/assets/vscode-material-icons/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/assets/images/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/assets/images/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/assets/icons/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/assets/icons/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/assets/codicons/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8/assets/codicons/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/checkpoint-documents/cf2dfe0d-7a26-4d2f-aa00-ca68acd6dae3/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/f7633ca54a5f1a021a60064f46aab074/Augment.vscode-augment/augment-user-assets/checkpoint-documents/cf2dfe0d-7a26-4d2f-aa00-ca68acd6dae3/"}}