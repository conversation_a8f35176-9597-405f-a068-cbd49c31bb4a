<template>
  <div class="user-detail">


    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">用户财务详情 - {{ user.username || '加载中...' }}</h1>
      <div class="header-actions">
        <button class="btn-secondary" @click="goBack">
          <i class="fas fa-arrow-left mr-2"></i>返回用户列表
        </button>
      </div>
    </div>



    <div v-if="loading" class="loading-container">
      <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
      <p class="text-gray-400 mt-2">加载中...</p>
    </div>

    <div v-else-if="error" class="error-container">
      <i class="fas fa-exclamation-triangle text-4xl text-red-400 mb-4"></i>
      <p class="text-red-400">{{ error }}</p>
      <button class="btn-primary mt-4" @click="loadUserDetail">重试</button>
    </div>

    <div v-else class="space-y-6">


      <!-- 用户基本信息 -->
      <div class="info-card">
        <h2 class="card-title">用户信息</h2>

        <div class="user-info-grid">
          <div class="info-item">
            <label>用户名</label>
            <p>{{ user.username || 'testuser' }}</p>
          </div>
          <div class="info-item">
            <label>邮箱</label>
            <p>{{ user.email || '<EMAIL>' }}</p>
          </div>
          <div class="info-item">
            <label>注册时间</label>
            <p>{{ user.created_at ? formatDateTime(user.created_at) : '2024/1/1 08:00:00' }}</p>
          </div>
          <div class="info-item">
            <label>VIP等级</label>
            <div class="vip-info">
              <span :class="['vip-badge', getVipClassWithExpire(user.finance?.vip_level, user.finance?.vip_expire_at)]">
                {{ getVipStatusWithExpire(user.finance?.vip_level, user.finance?.vip_expire_at) }}
              </span>

              <!-- VIP到期时间 -->
              <div v-if="user.finance?.vip_level > 0 && user.finance?.vip_expire_at" class="vip-expire-info">
                <small class="text-gray-400">
                  到期时间: {{ formatDateTime(user.finance.vip_expire_at) }}
                  <span v-if="isVipExpired(user.finance.vip_expire_at)" class="text-red-400 ml-2">(已过期)</span>
                </small>
              </div>

              <!-- 配额使用情况 -->
              <div v-if="user.finance" class="quota-info mt-2">
                <div class="quota-item">
                  <small class="text-gray-400">
                    基础配额: {{ user.finance.basic_quota - user.finance.basic_quota_used || 0 }}/{{ user.finance.basic_quota || 0 }}
                  </small>
                </div>
                <div class="quota-item">
                  <small class="text-gray-400">
                    高级配额: {{ user.finance.premium_quota - user.finance.premium_quota_used || 0 }}/{{ user.finance.premium_quota || 0 }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- 财务统计概览 -->
      <div class="info-card">
        <h2 class="card-title">财务统计概览</h2>

        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value text-green-400">¥{{ ((financeStats.total_cash_used || 0) / 100).toFixed(2) }}</div>
            <div class="stat-label">现金支出总额</div>
          </div>
          <div class="stat-item">
            <div class="stat-value text-blue-400">{{ financeStats.total_purchases || 0 }}</div>
            <div class="stat-label">购买次数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value text-purple-400">{{ financeStats.basic_purchases || 0 }}</div>
            <div class="stat-label">基础内容</div>
          </div>
          <div class="stat-item">
            <div class="stat-value text-pink-400">{{ financeStats.premium_purchases || 0 }}</div>
            <div class="stat-label">高级内容</div>
          </div>
          <div class="stat-item">
            <div class="stat-value text-cyan-400">{{ financeStats.total_quota_used || 0 }}个</div>
            <div class="stat-label">配额使用总量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value text-orange-400">{{ financeStats.total_points_used || 0 }}分</div>
            <div class="stat-label">积分使用总量</div>
          </div>
        </div>
      </div>



      <!-- 支付方式统计 -->
      <div class="info-card">
        <h2 class="card-title">支付方式统计</h2>

        <div class="payment-stats-grid">
          <div class="stat-item">
            <div class="stat-value text-green-400">¥{{ ((financeStats.total_cash_used || 0) / 100).toFixed(2) }}</div>
            <div class="stat-label">现金使用总额</div>
            <div class="stat-sublabel">{{ financeStats.cash_payments || 0 }}次支付</div>
          </div>
          <div class="stat-item">
            <div class="stat-value text-cyan-400">{{ financeStats.total_quota_used || 0 }}个</div>
            <div class="stat-label">配额使用总量</div>
            <div class="stat-sublabel">{{ financeStats.quota_payments || 0 }}次支付</div>
          </div>
          <div class="stat-item">
            <div class="stat-value text-orange-400">{{ financeStats.total_points_used || 0 }}分</div>
            <div class="stat-label">积分使用总量</div>
            <div class="stat-sublabel">{{ financeStats.points_payments || 0 }}次支付</div>
          </div>
        </div>
      </div>

      <!-- 配额详细统计 -->
      <div class="info-card">
        <h2 class="card-title">配额使用详情</h2>

        <div class="quota-details-grid">
          <div class="quota-detail-item">
            <div class="quota-type">基础配额</div>
            <div class="quota-progress">
              <div class="quota-bar">
                <div class="quota-used" :style="{ width: getQuotaPercentage(financeStats.basic_quota_used, financeStats.basic_quota_total) + '%' }"></div>
              </div>
              <div class="quota-text">
                {{ financeStats.basic_quota_used || 0 }} / {{ financeStats.basic_quota_total || 0 }}
                (剩余: {{ financeStats.basic_quota_remaining || 0 }})
              </div>
            </div>
          </div>

          <div class="quota-detail-item">
            <div class="quota-type">高级配额</div>
            <div class="quota-progress">
              <div class="quota-bar">
                <div class="quota-used premium" :style="{ width: getQuotaPercentage(financeStats.premium_quota_used, financeStats.premium_quota_total) + '%' }"></div>
              </div>
              <div class="quota-text">
                {{ financeStats.premium_quota_used || 0 }} / {{ financeStats.premium_quota_total || 0 }}
                (剩余: {{ financeStats.premium_quota_remaining || 0 }})
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 月度统计 -->
      <div class="info-card">
        <h2 class="card-title">月度财务统计</h2>

        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>月份</th>
                <th>购买金额</th>
                <th>消费金额</th>
                <th>净收支</th>
              </tr>
            </thead>
            <tbody>
              <template v-if="monthlyData && monthlyData.length > 0">
                <tr v-for="(month, index) in monthlyData.slice(0, 12)" :key="`month-${index}`">
                  <td>{{ month.month || '无' }}</td>
                  <td class="text-green-400">¥{{ ((month.purchases || 0) / 100).toFixed(2) }}</td>
                  <td class="text-red-400">¥{{ ((month.consumption || 0) / 100).toFixed(2) }}</td>
                  <td :class="(month.net || 0) >= 0 ? 'text-green-400' : 'text-red-400'">
                    ¥{{ ((month.net || 0) / 100).toFixed(2) }}
                  </td>
                </tr>
              </template>
              <template v-else>
                <tr>
                  <td colspan="4" class="text-center text-gray-400">暂无月度数据</td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>



      <!-- 筛选器 -->
      <div class="info-card">
        <h2 class="card-title">财务记录筛选</h2>

        <div class="filter-form">
          <div class="form-group">
            <label>记录类型</label>
            <select v-model="filters.record_type" class="form-select" @change="loadTransactions">
              <option value="">全部记录</option>
              <option value="purchase">购买记录</option>
              <option value="consumption">消费记录</option>
              <option value="quota">配额使用</option>
              <option value="points">积分记录</option>
              <option value="subscription">订阅记录</option>
            </select>
          </div>

          <div class="form-group">
            <label>时间范围</label>
            <select v-model="filters.time_range" class="form-select" @change="loadTransactions">
              <option value="">全部时间</option>
              <option value="today">今天</option>
              <option value="week">最近一周</option>
              <option value="month">最近一月</option>
              <option value="quarter">最近三月</option>
            </select>
          </div>

          <div class="form-group">
            <label>内容类型</label>
            <select v-model="filters.content_type" class="form-select" @change="loadTransactions">
              <option value="">全部类型</option>
              <option value="basic">基础内容</option>
              <option value="premium">高级内容</option>
              <option value="vip">VIP服务</option>
              <option value="recharge">充值</option>
            </select>
          </div>
        </div>
      </div>



      <!-- 详细财务流水 -->
      <div class="info-card">
        <h2 class="card-title">详细财务流水（原始单据）- 共{{ transactions.length }}条记录</h2>



        <div v-if="loading" class="loading-container">
          <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
          <p class="text-gray-400 mt-2">加载中...</p>
        </div>

        <div v-else-if="transactions.length === 0" class="no-data-container">
          <i class="fas fa-receipt text-4xl text-gray-600 mb-4"></i>
          <p class="text-gray-400">暂无财务记录</p>
        </div>

        <div v-else class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>时间</th>
                <th>类型</th>
                <th>描述</th>
                <th>支付详情</th>
                <th>内容类型</th>
                <th>现金变动</th>
                <th>配额变动</th>
                <th>积分变动</th>
                <th>现金余额</th>
                <th>配额余额</th>
                <th>积分余额</th>
                <th>交易ID</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="transaction in transactions" :key="`${transaction.type}-${transaction.id}`">
                <td class="text-sm">{{ formatDateTime(transaction.date) }}</td>
                <td>
                  <span :class="['type-badge', `type-${transaction.type}`]">
                    {{ getTransactionTypeText(transaction.type) }}
                  </span>
                </td>
                <td class="text-sm">{{ transaction.description }}</td>
                <td class="text-sm">
                  <span v-if="transaction.payment_details" class="payment-details">
                    {{ transaction.payment_details }}
                  </span>
                  <span v-else class="text-gray-400">-</span>
                </td>
                <td>
                  <span v-if="transaction.content_type" :class="['content-badge', `content-${transaction.content_type}`]">
                    {{ getContentTypeText(transaction.content_type) }}
                  </span>
                  <span v-else class="text-gray-400">-</span>
                </td>
                <td :class="getAmountClass(transaction.amount)">
                  <span v-if="transaction.amount !== 0">
                    {{ transaction.amount > 0 ? '+' : '' }}¥{{ (Math.abs(transaction.amount) / 100).toFixed(2) }}
                  </span>
                  <span v-else class="text-gray-400">-</span>
                </td>
                <td :class="getQuotaChangeClass(transaction.quota_change)">
                  <span v-if="transaction.quota_change">
                    {{ transaction.quota_change > 0 ? '+' : '' }}{{ transaction.quota_change }}
                    <span class="text-xs">
                      ({{ transaction.quota_type === 'basic' ? '基础配额' :
                           transaction.quota_type === 'premium' ? '高级配额' :
                           transaction.quota_type || '配额' }})
                    </span>
                  </span>
                  <span v-else class="text-gray-400">-</span>
                </td>
                <td :class="getPointsChangeClass(transaction.points_change)">
                  <span v-if="transaction.points_change">
                    {{ transaction.points_change > 0 ? '+' : '' }}{{ transaction.points_change }}
                  </span>
                  <span v-else class="text-gray-400">-</span>
                </td>
                <!-- 现金余额 -->
                <td class="text-gray-300">
                  <div v-if="transaction.balance_before !== null || transaction.balance_after !== null">
                    <div class="text-xs">前: ¥{{ ((transaction.balance_before || 0) / 100).toFixed(2) }}</div>
                    <div class="text-xs">后: ¥{{ ((transaction.balance_after || 0) / 100).toFixed(2) }}</div>
                  </div>
                  <span v-else class="text-gray-400">-</span>
                </td>

                <!-- 配额余额 -->
                <td class="text-cyan-300">
                  <div v-if="transaction.quota_before !== null || transaction.quota_after !== null">
                    <div class="text-xs">前: {{ transaction.quota_before || 0 }}</div>
                    <div class="text-xs">后: {{ transaction.quota_after || 0 }}</div>
                  </div>
                  <span v-else class="text-gray-400">-</span>
                </td>

                <!-- 积分余额 -->
                <td class="text-orange-300">
                  <div v-if="transaction.points_before !== null || transaction.points_after !== null">
                    <div class="text-xs">前: {{ transaction.points_before || 0 }}</div>
                    <div class="text-xs">后: {{ transaction.points_after || 0 }}</div>
                  </div>
                  <span v-else class="text-gray-400">-</span>
                </td>
                <td class="text-xs text-gray-400">{{ transaction.transaction_id || '-' }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div v-if="transactionPagination.pages > 1" class="pagination">
          <button
            class="btn-secondary"
            :disabled="!transactionPagination.has_prev"
            @click="handleTransactionPageChange(transactionPagination.prev_num)"
          >
            上一页
          </button>
          <div class="page-numbers">
            <button
              v-for="page in getTransactionPageNumbers()"
              :key="page"
              :class="['page-btn', page === transactionPagination.page ? 'active' : '']"
              @click="handleTransactionPageChange(page)"
            >
              {{ page }}
            </button>
          </div>
          <button
            class="btn-secondary"
            :disabled="!transactionPagination.has_next"
            @click="handleTransactionPageChange(transactionPagination.next_num)"
          >
            下一页
          </button>
        </div>
      </div>

      <!-- 筛选器 -->
      <div class="info-card">
        <h2 class="card-title">财务记录筛选</h2>

        <div class="filter-form">
          <div class="form-group">
            <label>记录类型</label>
            <select v-model="filters.record_type" class="form-select" @change="loadTransactions">
              <option value="">全部记录</option>
              <option value="purchase">购买记录</option>
              <option value="consumption">消费记录</option>
              <option value="quota">配额使用</option>
              <option value="points">积分记录</option>
              <option value="subscription">订阅记录</option>
            </select>
          </div>

          <div class="form-group">
            <label>时间范围</label>
            <select v-model="filters.time_range" class="form-select" @change="loadTransactions">
              <option value="">全部时间</option>
              <option value="today">今天</option>
              <option value="week">最近一周</option>
              <option value="month">最近一月</option>
              <option value="quarter">最近三月</option>
            </select>
          </div>

          <div class="form-group">
            <label>内容类型</label>
            <select v-model="filters.content_type" class="form-select" @change="loadTransactions">
              <option value="">全部类型</option>
              <option value="basic">基础内容</option>
              <option value="premium">高级内容</option>
              <option value="vip">VIP服务</option>
              <option value="recharge">充值</option>
            </select>
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usersApi } from '@/api/users'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const error = ref('')
const user = ref({})
const financeStats = reactive({
  total_spent: 0,
  total_purchases: 0,
  basic_purchases: 0,
  premium_purchases: 0,
  total_consumption: 0,
  current_balance: 0,
  cash_payments: 0,
  quota_payments: 0,
  points_payments: 0
})
const monthlyData = ref([])
const transactions = ref([])

// 调试信息
const debugInfo = reactive({
  loadTransactionsCalled: false,
  loadUserDetailCalled: false,
  mountedCalled: false,
  mockDataLength: 0
})

const filters = reactive({
  record_type: '',
  time_range: '',
  content_type: '',
  page: 1,
  per_page: 20
})

const transactionPagination = reactive({
  page: 1,
  pages: 1,
  total: 0,
  per_page: 20,
  has_prev: false,
  has_next: false,
  prev_num: null,
  next_num: null
})

// 方法
const formatDateTime = (date: string) => {
  if (!date) return '无'
  try {
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) return '无效日期'
    return dateObj.toLocaleString('zh-CN')
  } catch (error) {
    console.error('formatDateTime error:', error)
    return '格式错误'
  }
}

const getVipStatus = (vipLevel: number) => {
  try {
    if (!vipLevel || vipLevel === 0) return '普通用户'
    if (vipLevel === 2) return 'VIP Pro'
    if (vipLevel === 1) return 'VIP'
    return '普通用户'
  } catch (error) {
    console.error('getVipStatus error:', error)
    return '未知'
  }
}

// 考虑过期时间的VIP状态
const getVipStatusWithExpire = (vipLevel: number, expireAt?: string) => {
  if (!vipLevel || vipLevel === 0) return '普通用户'

  // 检查是否过期
  if (expireAt) {
    const expireDate = new Date(expireAt)
    const now = new Date()
    if (expireDate <= now) {
      return '已过期'
    }
  }

  if (vipLevel === 2) return 'VIP Pro'
  if (vipLevel === 1) return 'VIP'
  return '普通用户'
}

// 检查VIP是否过期
const isVipExpired = (expireAt?: string) => {
  if (!expireAt) return false
  const expireDate = new Date(expireAt)
  const now = new Date()
  return expireDate <= now
}

// 计算配额使用百分比
const getQuotaPercentage = (used: number, total: number) => {
  if (!total || total === 0) return 0
  return Math.min((used / total) * 100, 100)
}

const getVipClass = (vipLevel: number) => {
  try {
    if (!vipLevel || vipLevel === 0) return 'vip-free'
    if (vipLevel === 2) return 'vip-premium'
    return 'vip-basic'
  } catch (error) {
    console.error('getVipClass error:', error)
    return 'vip-free'
  }
}

// 考虑过期时间的VIP样式类
const getVipClassWithExpire = (vipLevel: number, expireAt?: string) => {
  // 检查是否过期
  if (vipLevel > 0 && expireAt) {
    const expireDate = new Date(expireAt)
    const now = new Date()
    if (expireDate <= now) {
      return 'vip-expired'  // 过期状态：红色
    }
  }

  if (!vipLevel || vipLevel === 0) return 'vip-free'
  if (vipLevel === 2) return 'vip-premium'
  return 'vip-basic'
}

const getTransactionTypeText = (type: string) => {
  const typeMap = {
    purchase: '购买',
    consumption: '消费',
    quota_consumption: '配额消费',
    quota_purchase: '配额购买',
    points_earned: '积分获得',
    points_consumption: '积分消费',
    points_used: '积分消费',
    subscription: '订阅',
    recharge: '充值',
    refund: '退款',
    quota_reset: '配额重置',
    vip_upgrade: 'VIP升级',
    vip_expire: 'VIP到期'
  }
  return typeMap[type] || type
}

const getContentTypeText = (type: string) => {
  const typeMap = {
    basic: '基础内容',
    premium: '高级内容',
    vip: 'VIP服务',
    recharge: '充值',
    subscription: '订阅服务',
    quota_pack: '配额包'
  }
  return typeMap[type] || type
}

const getPaymentMethodText = (method: string) => {
  const methodMap = {
    quota: '配额',
    points: '积分',
    alipay: '支付宝',
    wechat: '微信',
    mock: '模拟支付',
    stripe: 'Stripe',
    balance: '余额'
  }
  return methodMap[method] || method
}

const getCostTypeText = (type: string) => {
  const typeMap = {
    quota: '配额',
    points: '积分',
    balance: '余额'
  }
  return typeMap[type] || type
}

const getAmountClass = (amount: number) => {
  if (amount > 0) return 'text-green-400'
  if (amount < 0) return 'text-red-400'
  return 'text-gray-400'
}

const getQuotaChangeClass = (change: number) => {
  if (change > 0) return 'text-green-400'
  if (change < 0) return 'text-red-400'
  return 'text-gray-400'
}

const getPointsChangeClass = (change: number) => {
  if (change > 0) return 'text-green-400'
  if (change < 0) return 'text-red-400'
  return 'text-gray-400'
}

const getTransactionPageNumbers = () => {
  const pages = []
  const current = transactionPagination.page
  const total = transactionPagination.pages

  const start = Math.max(1, current - 2)
  const end = Math.min(total, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}

const loadUserDetail = async () => {
  loading.value = true
  error.value = ''

  try {
    const userId = route.params.id

    // 真实API调用
    try {
      console.log('🔍 开始调用用户财务详情API:', `/admin/api/users/${userId}/finance`)

      const response = await fetch(`/admin/api/users/${userId}/finance`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'  // 包含认证信息
      })

      console.log('📡 API响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('📊 API返回数据:', data)

      if (data.success) {
        user.value = data.user
        Object.assign(financeStats, data.finance_stats)
        monthlyData.value = data.monthly_data

        console.log('✅ 用户财务数据加载成功')
        // 单独加载交易记录
        await loadTransactions()
        return
      } else {
        throw new Error(data.error || '获取用户财务详情失败')
      }
    } catch (apiError) {
      console.error('❌ API调用失败:', apiError)
      error.value = `API调用失败: ${apiError.message}`

      // 不降级到模拟数据，显示错误信息
      return
    }

  } catch (err) {
    error.value = '加载用户详情失败: ' + err.message
  } finally {
    loading.value = false
    console.log('🐛 loadUserDetail 完成，loading:', loading.value, 'error:', error.value)
  }
}

const loadTransactions = async () => {
  try {
    debugInfo.loadTransactionsCalled = true
    console.log('🐛 loadTransactions 被调用了!')

    const userId = route.params.id

    // 构建查询参数
    const params = new URLSearchParams({
      page: filters.page.toString(),
      per_page: filters.per_page.toString(),
      record_type: filters.record_type || '',
      time_range: filters.time_range || '',
      content_type: filters.content_type || ''
    })

    console.log('🐛 查询参数:', params.toString())

    // 真实API调用
    try {
      const response = await fetch(`/admin/api/users/${userId}/transactions?${params.toString()}`)
      const data = await response.json()

      if (data.success) {
        transactions.value = data.data.transactions || []
        Object.assign(transactionPagination, {
          page: data.data.pagination.page || 1,
          pages: data.data.pagination.pages || 1,
          total: data.data.pagination.total || 0,
          per_page: data.data.pagination.per_page || 20,
          has_prev: data.data.pagination.has_prev || false,
          has_next: data.data.pagination.has_next || false,
          prev_num: data.data.pagination.prev_num || null,
          next_num: data.data.pagination.next_num || null
        })
        return
      } else {
        throw new Error(data.error || '获取交易记录失败')
      }
    } catch (apiError) {
      console.warn('API调用失败，使用模拟数据:', apiError)
    }

    // 模拟详细的交易数据 - 包含所有类型的交易记录
    const mockTransactions = [
      // 1. 购买记录 - 充值
      {
        id: 1,
        type: 'purchase',
        date: '2024-01-01T10:00:00Z',
        description: '支付宝充值',
        article_title: null,
        content_type: 'recharge',
        payment_method: 'alipay',
        amount: 20000, // +200元
        quota_change: 0,
        quota_type: null,
        points_change: 200, // 充值送积分
        balance_before: 0,
        balance_after: 20000,
        transaction_id: 'ALI_20240101_001'
      },
      // 购买记录 - VIP
      {
        id: 2,
        type: 'purchase',
        date: '2024-01-02T09:15:00Z',
        description: '购买VIP1个月',
        article_title: null,
        content_type: 'vip',
        payment_method: 'balance',
        amount: -12800, // -128元
        quota_change: 0,
        quota_type: null,
        points_change: 0,
        balance_before: 20000,
        balance_after: 7200,
        transaction_id: 'VIP_20240102_001'
      },
      // 购买记录 - 文章
      {
        id: 3,
        type: 'purchase',
        date: '2024-01-03T14:20:00Z',
        description: '购买文章: 塔罗牌高级解读技巧',
        article_title: '塔罗牌高级解读技巧',
        content_type: 'premium',
        payment_method: 'balance',
        amount: -1000, // -10元
        quota_change: 0,
        quota_type: null,
        points_change: 10, // 购买送积分
        balance_before: 7200,
        balance_after: 6200,
        transaction_id: 'ART_20240103_001'
      },
      // 消费记录 - 配额消费
      {
        id: 4,
        type: 'consumption',
        date: '2024-01-04T16:45:00Z',
        description: '消费: 爱情塔罗牌阵详解',
        article_title: '爱情塔罗牌阵详解',
        content_type: 'premium',
        payment_method: null,
        cost_type: 'quota',
        amount: 0, // 配额消费不影响余额
        quota_change: -1,
        quota_type: 'premium',
        points_change: 0,
        balance_before: 6200,
        balance_after: 6200,
        transaction_id: null
      },
      // 消费记录 - 余额消费
      {
        id: 5,
        type: 'consumption',
        date: '2024-01-05T11:30:00Z',
        description: '消费: 塔罗牌入门指南',
        article_title: '塔罗牌入门指南',
        content_type: 'basic',
        payment_method: null,
        cost_type: 'balance',
        amount: -500, // -5元
        quota_change: 0,
        quota_type: null,
        points_change: 0,
        balance_before: 6200,
        balance_after: 5700,
        transaction_id: null
      },
      // 消费记录 - 积分消费
      {
        id: 6,
        type: 'consumption',
        date: '2024-01-06T13:15:00Z',
        description: '消费: 塔罗牌牌意解析',
        article_title: '塔罗牌牌意解析',
        content_type: 'basic',
        payment_method: null,
        cost_type: 'points',
        amount: 0, // 积分消费不影响余额
        quota_change: 0,
        quota_type: null,
        points_change: -50,
        balance_before: 5700,
        balance_after: 5700,
        transaction_id: null
      },
      // 积分记录 - 签到奖励
      {
        id: 7,
        type: 'points_earned',
        date: '2024-01-07T08:00:00Z',
        description: '每日签到奖励',
        article_title: null,
        content_type: null,
        payment_method: null,
        cost_type: null,
        amount: 0,
        quota_change: 0,
        quota_type: null,
        points_change: 10,
        balance_before: 5700,
        balance_after: 5700,
        transaction_id: null
      },
      // 配额记录 - 配额重置
      {
        id: 8,
        type: 'quota_reset',
        date: '2024-01-08T00:00:00Z',
        description: 'VIP配额月度重置',
        article_title: null,
        content_type: null,
        payment_method: null,
        cost_type: null,
        amount: 0,
        quota_change: 10, // 重置配额
        quota_type: 'basic',
        points_change: 0,
        balance_before: 5700,
        balance_after: 5700,
        transaction_id: null
      },
      // 退款记录
      {
        id: 9,
        type: 'refund',
        date: '2024-01-09T15:30:00Z',
        description: '文章购买退款: 塔罗牌高级解读技巧',
        article_title: '塔罗牌高级解读技巧',
        content_type: 'premium',
        payment_method: 'balance',
        amount: 1000, // +10元退款
        quota_change: 0,
        quota_type: null,
        points_change: -10, // 扣回赠送积分
        balance_before: 5700,
        balance_after: 6700,
        transaction_id: 'REF_20240109_001'
      },
      // 10. 订阅记录
      {
        id: 10,
        type: 'subscription',
        date: '2024-01-10T12:00:00Z',
        description: '订阅塔罗师Alice',
        article_title: null,
        content_type: 'subscription',
        payment_method: 'balance',
        amount: -2000, // -20元
        quota_change: 0,
        quota_type: null,
        points_change: 20,
        balance_before: 6700,
        balance_after: 4700,
        transaction_id: 'SUB_20240110_001'
      },
      // 11. 积分消费 - 兑换配额
      {
        id: 11,
        type: 'points_consumption',
        date: '2024-01-11T09:30:00Z',
        description: '积分兑换高级配额',
        article_title: null,
        content_type: null,
        payment_method: null,
        cost_type: 'points',
        amount: 0,
        quota_change: 5,
        quota_type: 'premium',
        points_change: -500, // 消费500积分
        balance_before: 4700,
        balance_after: 4700,
        transaction_id: null
      },
      // 12. 积分获得 - 每日签到
      {
        id: 12,
        type: 'points_earned',
        date: '2024-01-12T08:00:00Z',
        description: '每日签到奖励',
        article_title: null,
        content_type: null,
        payment_method: null,
        cost_type: null,
        amount: 0,
        quota_change: 0,
        quota_type: null,
        points_change: 10,
        balance_before: 4700,
        balance_after: 4700,
        transaction_id: null
      },
      // 13. 积分获得 - 推荐奖励
      {
        id: 13,
        type: 'points_earned',
        date: '2024-01-13T14:20:00Z',
        description: '推荐新用户奖励',
        article_title: null,
        content_type: null,
        payment_method: null,
        cost_type: null,
        amount: 0,
        quota_change: 0,
        quota_type: null,
        points_change: 100, // 推荐奖励100积分
        balance_before: 4700,
        balance_after: 4700,
        transaction_id: null
      },
      // 14. 配额消费 - 查看高级内容
      {
        id: 14,
        type: 'quota_consumption',
        date: '2024-01-14T16:45:00Z',
        description: '查看高级塔罗解读',
        article_title: '深度塔罗牌解读：爱情运势',
        content_type: 'premium',
        payment_method: null,
        cost_type: 'quota',
        amount: 0,
        quota_change: -1,
        quota_type: 'premium',
        points_change: 0,
        balance_before: 4700,
        balance_after: 4700,
        transaction_id: null
      },
      // 15. 配额获得 - VIP月度重置
      {
        id: 15,
        type: 'quota_reset',
        date: '2024-02-01T00:00:00Z',
        description: 'VIP配额月度重置',
        article_title: null,
        content_type: null,
        payment_method: null,
        cost_type: null,
        amount: 0,
        quota_change: 20, // 重置20个高级配额
        quota_type: 'premium',
        points_change: 0,
        balance_before: 4700,
        balance_after: 4700,
        transaction_id: null
      },
      // 16. 积分消费 - 购买文章
      {
        id: 16,
        type: 'points_consumption',
        date: '2024-02-02T11:15:00Z',
        description: '积分购买文章',
        article_title: '塔罗牌占卜技巧大全',
        content_type: 'basic',
        payment_method: null,
        cost_type: 'points',
        amount: 0,
        quota_change: 0,
        quota_type: null,
        points_change: -80, // 消费80积分
        balance_before: 4700,
        balance_after: 4700,
        transaction_id: null
      },
      // 17. 积分获得 - 评论奖励
      {
        id: 17,
        type: 'points_earned',
        date: '2024-02-03T15:30:00Z',
        description: '优质评论奖励',
        article_title: '塔罗牌占卜技巧大全',
        content_type: null,
        payment_method: null,
        cost_type: null,
        amount: 0,
        quota_change: 0,
        quota_type: null,
        points_change: 20, // 评论奖励20积分
        balance_before: 4700,
        balance_after: 4700,
        transaction_id: null
      },
      // 18. 配额消费 - 基础内容
      {
        id: 18,
        type: 'quota_consumption',
        date: '2024-02-04T10:20:00Z',
        description: '查看基础塔罗内容',
        article_title: '塔罗牌基础知识入门',
        content_type: 'basic',
        payment_method: null,
        cost_type: 'quota',
        amount: 0,
        quota_change: -1,
        quota_type: 'basic',
        points_change: 0,
        balance_before: 4700,
        balance_after: 4700,
        transaction_id: null
      },
      // 19. 积分获得 - 活动奖励
      {
        id: 19,
        type: 'points_earned',
        date: '2024-02-05T18:00:00Z',
        description: '春节活动奖励',
        article_title: null,
        content_type: null,
        payment_method: null,
        cost_type: null,
        amount: 0,
        quota_change: 0,
        quota_type: null,
        points_change: 200, // 活动奖励200积分
        balance_before: 4700,
        balance_after: 4700,
        transaction_id: 'EVENT_CNY_2024'
      },
      // 20. 配额购买 - 单独购买配额
      {
        id: 20,
        type: 'quota_purchase',
        date: '2024-02-06T13:45:00Z',
        description: '购买高级配额包',
        article_title: null,
        content_type: 'quota_pack',
        payment_method: 'balance',
        amount: -1500, // -15元
        quota_change: 10,
        quota_type: 'premium',
        points_change: 15, // 购买送积分
        balance_before: 4700,
        balance_after: 3200,
        transaction_id: 'QUOTA_20240206_001'
      }
    ]

    console.log('🐛 模拟数据长度:', mockTransactions.length)
    debugInfo.mockDataLength = mockTransactions.length

    // 根据筛选条件过滤数据
    let filteredTransactions = mockTransactions

    if (filters.record_type) {
      filteredTransactions = filteredTransactions.filter(t => t.type === filters.record_type)
    }

    if (filters.content_type) {
      filteredTransactions = filteredTransactions.filter(t => t.content_type === filters.content_type)
    }

    console.log('🐛 过滤后数据长度:', filteredTransactions.length)

    // 模拟分页
    const total = filteredTransactions.length
    const pages = Math.ceil(total / filters.per_page)
    const start = (filters.page - 1) * filters.per_page
    const end = start + filters.per_page
    const paginatedTransactions = filteredTransactions.slice(start, end)

    console.log('🐛 分页后数据长度:', paginatedTransactions.length)
    console.log('🐛 第一条记录:', paginatedTransactions[0])

    transactions.value = paginatedTransactions
    console.log('🐛 transactions.value 设置完成，长度:', transactions.value.length)
    Object.assign(transactionPagination, {
      page: filters.page,
      pages: pages,
      total: total,
      per_page: filters.per_page,
      has_prev: filters.page > 1,
      has_next: filters.page < pages,
      prev_num: filters.page > 1 ? filters.page - 1 : null,
      next_num: filters.page < pages ? filters.page + 1 : null
    })

  } catch (err) {
    console.error('加载交易记录失败:', err)
  }
}

const handleTransactionPageChange = (page: number) => {
  if (page && page !== filters.page) {
    filters.page = page
    loadTransactions()
  }
}

const goBack = () => {
  router.push('/users')
}

const getCurrentTime = () => {
  return new Date().toLocaleString()
}



// 初始化
onMounted(() => {
  loadUserDetail()
  loadTransactions()
})
</script>

<style scoped>
/* 深色主题样式 */
.user-detail {
  color: #e0e0e0;
  padding: 1.5rem;
  background-color: #111827;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

@media (min-width: 768px) {
  .page-header {
    flex-direction: row;
    align-items: center;
    gap: 0;
  }
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* 按钮样式 */
.btn-primary {
  background-color: #f59e0b;
  color: #1f2937;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary:hover {
  background-color: #d97706;
}

.btn-secondary {
  background-color: #6b7280;
  color: #d1d5db;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-secondary:hover {
  background-color: #4b5563;
  color: #fff;
}

/* 加载和错误状态 */
.loading-container, .error-container, .no-data-container {
  text-align: center;
  padding: 2rem;
}

/* 信息卡片 */
.info-card {
  background-color: #374151;
  border: 1px solid #4B5563;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #f59e0b;
  margin: 0 0 1rem 0;
}

/* 用户信息网格 */
.user-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .user-info-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-item label {
  color: #d1d5db;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.info-item p {
  color: #ffffff;
  font-weight: 500;
  margin: 0;
}

/* VIP徽章 */
.vip-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
}

.vip-free {
  background-color: rgba(107, 114, 128, 0.2);
  color: #d1d5db;
}

.vip-basic {
  background-color: rgba(245, 158, 11, 0.2);
  color: #fcd34d;
}

.vip-premium {
  background-color: rgba(245, 158, 11, 0.2);
  color: #fcd34d;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

@media (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

.payment-stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .payment-stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #9ca3af;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #4B5563;
}

.data-table th {
  background-color: transparent;
  color: #d1d5db;
  font-weight: 600;
  font-size: 0.875rem;
}

.data-table td {
  color: #e0e0e0;
}

/* 徽章样式 */
.type-badge, .content-badge, .payment-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 交易类型徽章 */
.type-purchase {
  background-color: rgba(16, 185, 129, 0.2);
  color: #6ee7b7;
}

.type-consumption {
  background-color: rgba(220, 38, 38, 0.2);
  color: #fca5a5;
}

.type-points_earned {
  background-color: rgba(245, 158, 11, 0.2);
  color: #fcd34d;
}

.type-points_used,
.type-points_consumption {
  background-color: rgba(251, 146, 60, 0.2);
  color: #fdba74;
}

.type-quota_consumption {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

.type-quota_purchase {
  background-color: rgba(34, 197, 94, 0.2);
  color: #4ade80;
}

.type-subscription {
  background-color: rgba(139, 92, 246, 0.2);
  color: #c4b5fd;
}

.type-refund {
  background-color: rgba(34, 197, 94, 0.2);
  color: #86efac;
}

.type-quota_reset {
  background-color: rgba(59, 130, 246, 0.2);
  color: #93c5fd;
}

.type-vip_upgrade {
  background-color: rgba(245, 158, 11, 0.2);
  color: #fcd34d;
}

.type-vip_expire {
  background-color: rgba(107, 114, 128, 0.2);
  color: #d1d5db;
}

.content-basic {
  background-color: rgba(59, 130, 246, 0.2);
  color: #93c5fd;
}

.content-premium {
  background-color: rgba(139, 92, 246, 0.2);
  color: #c4b5fd;
}

.payment-quota {
  background-color: rgba(6, 182, 212, 0.2);
  color: #67e8f9;
}

.payment-points {
  background-color: rgba(251, 146, 60, 0.2);
  color: #fdba74;
}

.payment-balance {
  background-color: rgba(34, 197, 94, 0.2);
  color: #86efac;
}

.payment-alipay {
  background-color: rgba(59, 130, 246, 0.2);
  color: #93c5fd;
}

.payment-wechat {
  background-color: rgba(34, 197, 94, 0.2);
  color: #86efac;
}

.payment-mock, .payment-stripe {
  background-color: rgba(139, 92, 246, 0.2);
  color: #c4b5fd;
}

.payment-other {
  background-color: rgba(107, 114, 128, 0.2);
  color: #d1d5db;
}

/* 筛选表单 */
.filter-form {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .filter-form {
    grid-template-columns: repeat(3, 1fr);
  }
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  color: #d1d5db;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.form-select {
  background-color: #1f2937;
  border: 1px solid #374151;
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  color: #e0e0e0;
  font-size: 0.875rem;
}

.form-select:focus {
  outline: none;
  border-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);
}

/* 文章链接 */
.article-link {
  color: #60a5fa;
  text-decoration: underline;
  cursor: pointer;
}

.article-link:hover {
  color: #93c5fd;
}

.loading-container,
.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

/* VIP信息样式 */
.vip-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.vip-expire-info {
  margin-top: 0.25rem;
}

.quota-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.quota-item {
  font-size: 0.75rem;
}

/* 配额详细统计样式 */
.quota-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.quota-detail-item {
  background: rgba(55, 65, 81, 0.5);
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #4B5563;
}

.quota-type {
  font-weight: 600;
  color: #E5E7EB;
  margin-bottom: 0.5rem;
}

.quota-progress {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quota-bar {
  width: 100%;
  height: 8px;
  background: #374151;
  border-radius: 4px;
  overflow: hidden;
}

.quota-used {
  height: 100%;
  background: #06B6D4;
  transition: width 0.3s ease;
}

.quota-used.premium {
  background: #F59E0B;
}

.quota-text {
  font-size: 0.875rem;
  color: #9CA3AF;
}

/* 支付方式统计样式 */
.stat-sublabel {
  font-size: 0.75rem;
  color: #6B7280;
  margin-top: 0.25rem;
}

.payment-details {
  font-size: 0.875rem;
  color: #E5E7EB;
}

/* 间距 */
.space-y-6 > * + * {
  margin-top: 1.5rem;
}
</style>
