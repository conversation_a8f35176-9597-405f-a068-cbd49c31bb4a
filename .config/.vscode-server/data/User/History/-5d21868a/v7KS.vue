<template>
  <div class="user-management">
    <!-- 页面头部区域 - 完全复刻原版Flask -->
    <div class="mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0">
      <h1 class="text-2xl font-bold text-white">用户管理</h1>
      <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3">
        <button @click="refreshList" class="admin-btn-secondary px-4 py-2 rounded-lg text-center">
          <i class="fas fa-sync-alt mr-2"></i>刷新列表
        </button>
        <button @click="handleExport" class="admin-btn-primary px-4 py-2 rounded-lg text-center">
          <i class="fas fa-download mr-2"></i>导出数据
        </button>
      </div>
    </div>

    <!-- 用户统计卡片区域 - 完全复刻原版Flask -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="admin-card p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-500 bg-opacity-20">
            <i class="fas fa-users text-blue-400 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-gray-400 text-sm">总用户数</p>
            <p class="text-2xl font-bold text-white">{{ statistics.total_users || '-' }}</p>
          </div>
        </div>
      </div>

      <div class="admin-card p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-500 bg-opacity-20">
            <i class="fas fa-crown text-yellow-400 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-gray-400 text-sm">VIP用户</p>
            <p class="text-2xl font-bold text-white">{{ statistics.vip_users || '-' }}</p>
          </div>
        </div>
      </div>

      <div class="admin-card p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-500 bg-opacity-20">
            <i class="fas fa-check-circle text-green-400 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-gray-400 text-sm">已验证邮箱</p>
            <p class="text-2xl font-bold text-white">{{ statistics.verified_users || '-' }}</p>
          </div>
        </div>
      </div>

      <div class="admin-card p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-500 bg-opacity-20">
            <i class="fas fa-calendar-day text-purple-400 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-gray-400 text-sm">今日新增</p>
            <p class="text-2xl font-bold text-white">{{ statistics.today_users || '-' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 过滤和搜索 - 完全复刻原版Flask -->
    <div class="admin-card p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 搜索框 -->
        <div>
          <label for="search" class="block mb-2 text-gray-300">搜索用户</label>
          <input
            v-model="filters.search"
            type="text"
            id="search"
            class="form-input w-full px-3 py-2 rounded-lg"
            placeholder="用户名或邮箱..."
            @keyup.enter="searchUsers"
          />
        </div>

        <!-- 角色过滤 -->
        <div>
          <label for="role-filter" class="block mb-2 text-gray-300">角色过滤</label>
          <select v-model="filters.role" id="role-filter" class="form-input w-full px-3 py-2 rounded-lg">
            <option value="">全部角色</option>
            <option value="user">普通用户</option>
            <option value="admin">管理员</option>
            <option value="editor">编辑</option>
          </select>
        </div>

        <!-- VIP状态过滤 -->
        <div>
          <label for="vip-filter" class="block mb-2 text-gray-300">VIP状态</label>
          <select v-model="filters.vip_level" id="vip-filter" class="form-input w-full px-3 py-2 rounded-lg">
            <option value="">全部状态</option>
            <option value="0">普通用户</option>
            <option value="1">VIP</option>
            <option value="2">VIP Pro</option>
          </select>
        </div>

        <!-- 搜索按钮 -->
        <div class="flex items-end">
          <button @click="searchUsers" id="search-btn" class="admin-btn-primary px-4 py-2 rounded-lg w-full">
            <i class="fas fa-search mr-2"></i>搜索
          </button>
        </div>
      </div>
    </div>

    <!-- 用户表格 - 完全复刻原版Flask -->
    <div class="admin-card p-4 mb-6 overflow-x-auto">
      <div v-if="loading" id="loading" class="text-center py-8">
        <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
        <p class="text-gray-400 mt-2">加载中...</p>
      </div>

      <div v-else-if="users.length > 0" id="users-table">
        <table class="table-admin w-full">
          <thead>
            <tr>
              <th class="px-4 py-2 text-left">ID</th>
              <th class="px-4 py-2 text-left">用户信息</th>
              <th class="px-4 py-2 text-left">角色</th>
              <th class="px-4 py-2 text-left">VIP状态</th>
              <th class="px-4 py-2 text-left">配额使用</th>
              <th class="px-4 py-2 text-left">余额</th>
              <th class="px-4 py-2 text-left">注册时间</th>
              <th class="px-4 py-2 text-left">操作</th>
            </tr>
          </thead>
          <tbody id="users-tbody">
            <tr
              v-for="user in users"
              :key="user.id"
              class="hover:bg-gray-700/50 transition-colors"
            >
              <!-- ID -->
              <td class="px-4 py-3 border-t border-gray-700">{{ user.id }}</td>

              <!-- 用户信息 -->
              <td class="px-4 py-3 border-t border-gray-700">
                <div class="user-info">
                  <div class="user-name text-white font-medium">{{ user.username }}</div>
                  <div class="user-email text-gray-400 text-sm">{{ user.email || '无邮箱' }}</div>
                  <div :class="['email-status text-xs', user.email_verified ? 'text-green-400' : 'text-red-400']">
                    {{ user.email_verified ? '✓ 已验证' : '✗ 未验证' }}
                  </div>
                </div>
              </td>

              <!-- 角色 -->
              <td class="px-4 py-3 border-t border-gray-700">
                <span :class="['px-2 py-1 rounded-full text-xs', user.role === 'admin' ? 'bg-red-900 text-red-300' : 'bg-gray-700 text-gray-300']">
                  {{ user.role === 'admin' ? '管理员' : '普通用户' }}
                </span>
              </td>

              <!-- VIP状态 -->
              <td class="px-4 py-3 border-t border-gray-700">
                <div class="vip-info">
                  <span :class="getVipBadgeClass(user.finance?.vip_level, user.finance?.vip_expire_at)">
                    {{ getVipStatusText(user.finance?.vip_level, user.finance?.vip_expire_at) }}
                  </span>
                  <div v-if="user.finance?.vip_expire_at && user.finance?.vip_level > 0" class="vip-expire text-xs text-gray-400 mt-1">
                    到期: {{ formatDate(user.finance.vip_expire_at) }}
                  </div>
                </div>
              </td>

              <!-- 配额使用 -->
              <td class="px-4 py-3 border-t border-gray-700">
                <div class="quota-info text-sm">
                  <!-- 管理员和客服显示无限制 -->
                  <div v-if="user.role === 'admin' || user.role === 'superadmin' || user.role === 'staff'">
                    <div class="text-yellow-400">基础: 无限制</div>
                    <div class="text-purple-400">高级: 无限制</div>
                  </div>
                  <!-- 普通用户显示实际配额 -->
                  <div v-else>
                    <div>
                      基础: {{ user.finance?.basic_quota_used || 0 }}/{{ user.finance?.basic_quota_total || 0 }}
                      <span class="text-gray-400">(剩余: {{ (user.finance?.basic_quota_total || 0) - (user.finance?.basic_quota_used || 0) }})</span>
                    </div>
                    <!-- 显示高级配额（如果用户有的话，不管是否过期） -->
                    <div v-if="(user.finance?.premium_quota_total || 0) > 0">
                      高级: {{ user.finance?.premium_quota_used || 0 }}/{{ user.finance?.premium_quota_total || 0 }}
                      <span class="text-gray-400">(剩余: {{ (user.finance?.premium_quota_total || 0) - (user.finance?.premium_quota_used || 0) }})</span>
                    </div>
                  </div>
                </div>
              </td>

              <!-- 余额 -->
              <td class="px-4 py-3 border-t border-gray-700">
                <span class="balance text-green-400 font-mono">¥{{ (user.finance?.balance || 0).toFixed(2) }}</span>
              </td>

              <!-- 注册时间 -->
              <td class="px-4 py-3 border-t border-gray-700 text-sm text-gray-400">
                {{ formatDate(user.created_at) }}
              </td>

              <!-- 操作按钮组 -->
              <td class="px-4 py-3 border-t border-gray-700">
                <div class="action-buttons flex space-x-1">
                  <button
                    @click="viewUserFinance(user)"
                    class="admin-btn-secondary px-2 py-1 rounded text-sm"
                    title="财务详情"
                  >
                    <i class="fas fa-chart-line"></i>
                  </button>
                  <button
                    @click="editUser(user)"
                    class="admin-btn-primary px-2 py-1 rounded text-sm"
                    title="编辑"
                  >
                    <i class="fas fa-edit"></i>
                  </button>
                  <button
                    @click="manageVip(user)"
                    class="admin-btn-secondary px-2 py-1 rounded text-sm"
                    title="VIP管理"
                  >
                    <i class="fas fa-crown"></i>
                  </button>
                  <button
                    @click="resetPassword(user)"
                    class="admin-btn-warning px-2 py-1 rounded text-sm"
                    title="重置密码"
                  >
                    <i class="fas fa-key"></i>
                  </button>
                  <button
                    @click="sendMessage(user)"
                    class="admin-btn-info px-2 py-1 rounded text-sm"
                    title="发送私信"
                  >
                    <i class="fas fa-envelope"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-else id="no-data" class="text-center py-8">
        <i class="fas fa-users text-4xl text-gray-600 mb-4"></i>
        <p class="text-gray-400">暂无用户数据</p>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.pages > 1" class="pagination">
        <button
          class="btn-secondary"
          :disabled="!pagination.has_prev"
          @click="handlePageChange(pagination.prev_num)"
        >
          上一页
        </button>
        <div class="page-numbers">
          <button
            v-for="page in getPageNumbers()"
            :key="page"
            :class="['page-btn', page === pagination.page ? 'active' : '']"
            @click="handlePageChange(page)"
          >
            {{ page }}
          </button>
        </div>
        <button
          class="btn-secondary"
          :disabled="!pagination.has_next"
          @click="handlePageChange(pagination.next_num)"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑用户"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="editForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="editForm.phone" placeholder="请输入手机号（为手机端准备）" />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="editForm.role" placeholder="请选择角色">
            <el-option label="普通用户" value="user" />
            <el-option label="管理员" value="admin" />
            <el-option label="编辑" value="editor" />
            <el-option label="客服" value="customer_service" />
          </el-select>
        </el-form-item>
        <el-form-item label="邮箱验证">
          <el-switch v-model="editForm.email_verified" />
          <span class="ml-2 text-sm text-gray-400">
            未验证用户无法查看首页文章列表
          </span>
        </el-form-item>
        <el-form-item label="账户状态">
          <el-switch v-model="editForm.is_active" />
          <span class="ml-2 text-sm text-gray-400">
            禁用后用户无法登录
          </span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="submitEditUser">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- VIP管理对话框 -->
    <el-dialog
      v-model="showVipDialog"
      title="VIP管理"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="mb-4 p-4 bg-gray-800 rounded-lg">
        <h4 class="text-white mb-2">当前状态</h4>
        <p class="text-gray-300">
          用户：{{ vipForm.username }} |
          当前VIP等级：{{ getVipStatusText(vipForm.current_vip_level, vipForm.current_expire_date) }}
        </p>
      </div>

      <el-form :model="vipForm" label-width="100px">
        <el-form-item label="VIP等级">
          <el-select v-model="vipForm.vip_level" placeholder="请选择VIP等级">
            <el-option label="普通用户" :value="0" />
            <el-option label="VIP" :value="1" />
            <el-option label="VIP Pro" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="到期时间" v-if="vipForm.vip_level > 0">
          <el-date-picker
            v-model="vipForm.expire_date"
            type="date"
            placeholder="选择到期时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
          <div class="text-sm text-gray-400 mt-1">
            留空表示永不过期
          </div>
        </el-form-item>
        <el-form-item label="变更原因">
          <el-select v-model="vipForm.reason" placeholder="请选择变更原因">
            <el-option label="管理员调整" value="admin_change" />
            <el-option label="购买升级" value="purchase" />
            <el-option label="到期降级" value="expire" />
            <el-option label="违规处罚" value="violation" />
          </el-select>
        </el-form-item>
      </el-form>

      <div class="mt-4 p-4 bg-yellow-900 bg-opacity-30 rounded-lg">
        <h4 class="text-yellow-400 mb-2">⚠️ 重要提示</h4>
        <ul class="text-yellow-300 text-sm space-y-1">
          <li>• VIP到期后会自动降级为普通用户</li>
          <li>• 配额使用记录会保存，但使用量会重置</li>
          <li>• 降级不会影响已购买的文章访问权限</li>
        </ul>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showVipDialog = false">取消</el-button>
          <el-button type="primary" @click="submitVipManage">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 私信对话框 -->
    <el-dialog
      v-model="showMessageDialog"
      title="发送私信"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="mb-4 p-4 bg-gray-800 rounded-lg">
        <h4 class="text-white mb-2">收信人</h4>
        <p class="text-gray-300">{{ messageForm.username }}</p>
      </div>

      <el-form :model="messageForm" label-width="80px">
        <el-form-item label="私信内容">
          <el-input
            v-model="messageForm.message"
            type="textarea"
            :rows="6"
            placeholder="请输入要发送的私信内容..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <div class="mt-4 p-4 bg-blue-900 bg-opacity-30 rounded-lg">
        <h4 class="text-blue-400 mb-2">💡 私信功能说明</h4>
        <ul class="text-blue-300 text-sm space-y-1">
          <li>• 私信将发送到用户的消息中心</li>
          <li>• 用户登录后可在个人中心查看</li>
          <li>• 支持系统通知和邮件提醒</li>
        </ul>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showMessageDialog = false">取消</el-button>
          <el-button type="primary" @click="submitMessage">发送</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usersApi } from '@/api/users'

const router = useRouter()

// 响应式数据
const users = ref([])
const loading = ref(false)

// 编辑用户相关
const showEditDialog = ref(false)
const editForm = ref({
  id: null,
  username: '',
  email: '',
  role: 'user',
  email_verified: false,
  phone: '',
  is_active: true
})

const statistics = reactive({
  total_users: 0,
  vip_users: 0,
  active_users: 0,
  today_users: 0
})

const filters = reactive({
  search: '',
  role: '',
  vip_level: '',
  page: 1,
  per_page: 20
})

const pagination = reactive({
  page: 1,
  pages: 1,
  total: 0,
  per_page: 20,
  has_prev: false,
  has_next: false,
  prev_num: null,
  next_num: null
})

// 方法
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const getVipStatusText = (vipLevel: number, expireAt?: string) => {
  if (!vipLevel || vipLevel === 0) return '普通用户'

  // 检查是否过期
  if (expireAt) {
    const expireDate = new Date(expireAt)
    const now = new Date()
    if (expireDate <= now) {
      return '已过期'
    }
  }

  if (vipLevel === 2) return 'VIP Pro'
  if (vipLevel === 1) return 'VIP'
  return '普通用户'
}

const getVipBadgeClass = (vipLevel: number, expireAt?: string) => {
  // 检查是否过期
  if (vipLevel > 0 && expireAt) {
    const expireDate = new Date(expireAt)
    const now = new Date()
    if (expireDate <= now) {
      return 'px-2 py-1 rounded-full text-xs bg-red-900 text-red-300'  // 过期状态：红色
    }
  }

  if (!vipLevel || vipLevel === 0) {
    return 'px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300'  // 普通用户：灰色
  }
  if (vipLevel === 2) {
    return 'px-2 py-1 rounded-full text-xs bg-purple-900 text-purple-300'  // VIP Pro：紫色
  }
  if (vipLevel === 1) {
    return 'px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300'  // VIP：黄色
  }
  return 'px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300'
}



const getPageNumbers = () => {
  const pages = []
  const current = pagination.page
  const total = pagination.pages

  // 简单的分页逻辑，显示当前页前后2页
  const start = Math.max(1, current - 2)
  const end = Math.min(total, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: filters.page,
      per_page: filters.per_page,
      search: filters.search || undefined,
      role: filters.role || undefined,
      vip_level: filters.vip_level || undefined
    }

    const response = await usersApi.getList(params)

    if (response.success) {
      // 后端返回的格式：{success: true, users: [...], total: 100, pages: 5, current_page: 1}
      users.value = response.users || []
      pagination.page = response.current_page || 1
      pagination.pages = response.pages || 1
      pagination.total = response.total || 0
      pagination.per_page = params.per_page || 20
      pagination.has_prev = pagination.page > 1
      pagination.has_next = pagination.page < pagination.pages
    } else {
      ElMessage.error(response.message || '加载用户列表失败')
    }
  } catch (error) {
    console.error('加载用户失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await usersApi.getStats()

    if (response.success) {
      Object.assign(statistics, response.data)
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 刷新列表
const refreshList = () => {
  loadUsers()
  loadStatistics()
}

// 搜索用户
const searchUsers = () => {
  filters.page = 1
  loadUsers()
}

// 导出数据
const handleExport = async () => {
  try {
    ElMessage.info('正在导出用户数据...')
    const response = await usersApi.exportData(filters)

    if (response.success) {
      // 创建下载链接
      const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `用户数据_${new Date().toISOString().split('T')[0]}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功')
    } else {
      ElMessage.error(response.message || '导出失败')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 查看用户财务详情
const viewUserFinance = (user: any) => {
  router.push(`/users/${user.id}/finance`)
}

// 编辑用户
const editUser = (user: any) => {
  // 设置编辑表单数据
  editForm.value = {
    id: user.id,
    username: user.username,
    email: user.email,
    role: user.role,
    email_verified: user.email_verified,
    phone: user.phone || '',
    is_active: user.is_active !== false
  }
  showEditDialog.value = true
}

// VIP管理相关
const showVipDialog = ref(false)
const vipForm = ref({
  id: null,
  username: '',
  current_vip_level: 0,
  current_expire_date: '',
  vip_level: 0,
  expire_date: '',
  reason: 'admin_change'
})

// 私信相关
const showMessageDialog = ref(false)
const messageForm = ref({
  id: null,
  username: '',
  message: ''
})

// VIP管理
const manageVip = (user: any) => {
  // 设置VIP管理表单数据
  const currentVipLevel = user.finance?.vip_level || 0
  const expireDate = user.finance?.vip_expire_at ?
    new Date(user.finance.vip_expire_at).toISOString().split('T')[0] : ''

  vipForm.value = {
    id: user.id,
    username: user.username,
    current_vip_level: currentVipLevel,
    current_expire_date: user.finance?.vip_expire_at || '',
    vip_level: currentVipLevel,
    expire_date: expireDate,
    reason: 'admin_change'
  }
  showVipDialog.value = true
}

// 发送私信
const sendMessage = (user: any) => {
  messageForm.value = {
    id: user.id,
    username: user.username,
    message: ''
  }
  showMessageDialog.value = true
}

// 重置密码
const resetPassword = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户"${user.username}"的密码吗？`,
      '重置密码确认',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await usersApi.resetPassword(user.id)

    if (response.success) {
      ElMessage.success('密码重置成功')
    } else {
      ElMessage.error(response.message || '密码重置失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('密码重置失败:', error)
      ElMessage.error('密码重置失败')
    }
  }
}

const handlePageChange = (page: number) => {
  if (page && page !== pagination.page) {
    filters.page = page
    pagination.page = page
    loadUsers()
  }
}

// 提交编辑用户
const submitEditUser = async () => {
  try {
    const response = await usersApi.update(editForm.value.id, {
      username: editForm.value.username,
      email: editForm.value.email,
      role: editForm.value.role,
      email_verified: editForm.value.email_verified,
      phone: editForm.value.phone,
      is_active: editForm.value.is_active
    })

    if (response.success) {
      ElMessage.success('用户信息更新成功')
      showEditDialog.value = false
      loadUsers() // 刷新列表
    } else {
      ElMessage.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新用户失败:', error)
    ElMessage.error('更新用户失败')
  }
}

// 提交VIP管理
const submitVipManage = async () => {
  try {
    const response = await usersApi.manageVip(vipForm.value.id, {
      vip_level: vipForm.value.vip_level,
      expire_date: vipForm.value.expire_date || null,
      reason: vipForm.value.reason
    })

    if (response.success) {
      ElMessage.success('VIP状态更新成功')
      showVipDialog.value = false
      loadUsers() // 刷新列表
    } else {
      ElMessage.error(response.message || 'VIP状态更新失败')
    }
  } catch (error) {
    console.error('VIP状态更新失败:', error)
    ElMessage.error('VIP状态更新失败')
  }
}

// 提交私信
const submitMessage = async () => {
  if (!messageForm.value.message.trim()) {
    ElMessage.warning('请输入私信内容')
    return
  }

  try {
    const response = await usersApi.sendMessage(messageForm.value.id, messageForm.value.message)

    if (response.success) {
      ElMessage.success('私信发送成功')
      showMessageDialog.value = false
      messageForm.value.message = ''
    } else {
      ElMessage.error(response.message || '私信发送失败')
    }
  } catch (error) {
    console.error('私信发送失败:', error)
    ElMessage.error('私信发送失败')
  }
}

// 初始化
onMounted(() => {
  loadUsers()
  loadStatistics()
})
</script>

<style scoped>
/* 深色主题样式 - 完全按照原Flask管理后台 */
.user-management {
  color: #e0e0e0;
}

/* 页面标题 */
.page-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

@media (min-width: 768px) {
  .page-header {
    flex-direction: row;
    align-items: center;
    gap: 0;
  }
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.header-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@media (min-width: 768px) {
  .header-actions {
    flex-direction: row;
    gap: 0.75rem;
  }
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.stat-card {
  background-color: #374151;
  border: 1px solid #4B5563;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  padding: 0.75rem;
  border-radius: 50%;
  font-size: 1.25rem;
}

.stat-icon .fa-users {
  background-color: rgba(59, 130, 246, 0.2);
}

.stat-icon .fa-crown {
  background-color: rgba(245, 158, 11, 0.2);
}

.stat-icon .fa-check-circle {
  background-color: rgba(16, 185, 129, 0.2);
}

.stat-icon .fa-calendar-day {
  background-color: rgba(139, 92, 246, 0.2);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.stat-label {
  font-size: 0.875rem;
  color: #9ca3af;
  margin: 0;
}

/* 筛选卡片 */
.filter-card {
  background-color: #374151;
  border: 1px solid #4B5563;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.filter-form {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .filter-form {
    grid-template-columns: repeat(3, 1fr);
  }
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  color: #d1d5db;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.form-input, .form-select {
  background-color: #1f2937;
  border: 1px solid #374151;
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  color: #e0e0e0;
  font-size: 0.875rem;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);
}

.form-actions {
  display: flex;
  align-items: end;
}

/* 按钮样式 - 按照原Flask样式 */
.btn-primary {
  background-color: #f59e0b;
  color: #1f2937;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary:hover {
  background-color: #d97706;
}

.btn-secondary {
  background-color: #6b7280;
  color: #d1d5db;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-secondary:hover {
  background-color: #4b5563;
  color: #fff;
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-small {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* 数据卡片 */
.data-card {
  background-color: #374151;
  border: 1px solid #4B5563;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  overflow-x: auto;
}

.loading-container, .no-data-container {
  text-align: center;
  padding: 2rem;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #4B5563;
}

.data-table th {
  background-color: transparent;
  color: #d1d5db;
  font-weight: 600;
  font-size: 0.875rem;
}

.data-table td {
  color: #e0e0e0;
}

.user-row:hover {
  background-color: rgba(75, 85, 99, 0.5);
  transition: background-color 0.2s;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.user-name {
  font-weight: 500;
  color: #ffffff;
}

.user-email {
  font-size: 0.875rem;
  color: #9ca3af;
}

.email-status {
  font-size: 0.75rem;
}

.email-status.verified {
  color: #10b981;
}

.email-status.unverified {
  color: #ef4444;
}

.role-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.role-admin {
  background-color: rgba(220, 38, 38, 0.2);
  color: #fca5a5;
}

.role-user {
  background-color: rgba(107, 114, 128, 0.2);
  color: #d1d5db;
}

.vip-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.vip-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.vip-normal {
  background-color: rgba(107, 114, 128, 0.2);
  color: #d1d5db;
}

.vip-basic {
  background-color: rgba(16, 185, 129, 0.2);
  color: #6ee7b7;
}

.vip-premium {
  background-color: rgba(245, 158, 11, 0.2);
  color: #fcd34d;
}

.vip-expire {
  font-size: 0.75rem;
  color: #9ca3af;
}

.quota-info {
  font-size: 0.875rem;
}

.balance {
  color: #10b981;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.page-numbers {
  display: inline-flex;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.page-btn {
  background-color: #374151;
  color: #d1d5db;
  border: 1px solid #4b5563;
  border-left: none;
  padding: 0.5rem 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.page-btn:first-child {
  border-left: 1px solid #4b5563;
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.page-btn:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.page-btn:hover {
  background-color: #4b5563;
  color: #fff;
}

.page-btn.active {
  background-color: #f59e0b;
  color: #1f2937;
  border-color: #f59e0b;
}

/* 响应式 */
@media (max-width: 768px) {
  .data-table th:nth-child(n+6),
  .data-table td:nth-child(n+6) {
    display: none;
  }
}

@media (max-width: 640px) {
  .data-table th:nth-child(n+4),
  .data-table td:nth-child(n+4) {
    display: none;
  }
}
</style>
